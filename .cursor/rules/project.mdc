---
description: 
globs: 
alwaysApply: true
---
# 灯饰展示网站项目规则

## 项目概述
- 项目类型：WordPress 灯饰展示网站
- 开发框架：WordPress 6.x
- 数据库：MySQL 8.0 (utf8mb4)
- 服务器：PHP 8.x + Apache/Nginx

## 技术规范
### 数据库规范
- 数据库名：light_fixture
- 端口：3308
- 字符集：utf8mb4
- 排序规则：utf8mb4_unicode_ci

### 代码规范
1. PHP 开发规范
   - 遵循 PSR-12 编码规范
   - 使用有意义的变量和函数命名
   - 适当添加注释说明代码功能

2. WordPress 开发规范
   - 使用 WordPress 钩子系统进行功能扩展
   - 遵循 WordPress 主题和插件开发最佳实践
   - 使用 WordPress 内置函数而不是自定义实现

3. 数据库操作规范
   - 使用 WordPress 数据库 API 进行数据库操作
   - 避免直接使用 SQL 查询
   - 注意数据安全性，使用 prepared statements

## 主题规范
### 当前主题
- 主题：Twenty Twenty-Five (v1.2)
- 特点：简约、适应性强的设计风格

### 主题使用规范
1. 布局规范
   - 使用单栏布局作为基础
   - 保持页面结构的一致性
   - 遵循 WordPress 区块编辑器的最佳实践

2. 样式规范
   - 使用主题提供的默认颜色方案
   - 保持字体层级的一致性
   - 遵循无障碍设计标准

3. 内容展示规范
   - 图片展示需保持统一的比例
   - 文字内容需遵循既定的排版规则
   - 确保内容在不同设备上的可读性

## 目录规范
### 核心目录
- wp-admin/：管理后台文件
- wp-content/：主题、插件和上传文件
- wp-includes/：WordPress 核心文件

### 主题目录
```
wp-content/themes/twentytwentyfive/
├── style.css              # 主题样式文件
├── functions.php          # 主题功能文件
├── index.php             # 主题主模板
└── assets/              # 主题资源文件
    ├── css/            # CSS文件
    ├── js/             # JavaScript文件
    └── images/         # 图片资源
```

### 插件目录
```
wp-content/plugins/your-plugin/
├── your-plugin.php      # 插件主文件
├── includes/            # 包含文件
├── admin/              # 管理界面文件
└── assets/             # 资源文件
```

## 开发流程
1. 环境准备
   - PHP >= 8.0
   - MySQL >= 8.0
   - WordPress >= 6.0
   - 支持 mod_rewrite 的 Web 服务器

2. 开发步骤
   - 创建数据库
   - 配置 wp-config.php
   - 安装 WordPress
   - 配置主题和插件

## 维护规范
1. 日常维护
   - 定期更新 WordPress 核心
   - 更新主题和插件
   - 备份数据库和文件

2. 安全维护
   - 定期检查安全日志
   - 更新安全插件
   - 监控异常访问

## 版本控制
- 使用 Git 进行版本控制
- 遵循语义化版本规范
- 保持提交信息清晰明确



