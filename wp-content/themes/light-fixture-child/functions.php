<?php
/**
 * Light Fixture Child Theme functions and definitions
 *
 * @package Light_Fixture_Child
 */

/**
 * 主题功能加载器
 *
 * @package Light_Fixture_Child
 */

// Define theme directory path constant for easy file reference
define( 'LIGHT_FIXTURE_CHILD_THEME_DIR', get_stylesheet_directory() );

// Load core theme setup (menus, widgets, image sizes, etc.)
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/theme-setup.php';

// Load scripts and styles
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/enqueue.php';

// Load custom post types and taxonomies
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/post-types.php';

// Load custom meta boxes
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/meta-boxes.php';

// Load template helper functions (breadcrumbs, etc.)
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/template-helpers.php';

// Load admin-related functionality
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/admin.php';

// Load homepage slider settings
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/slider-options.php'; 

// Load custom blocks
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/blocks.php';

// Load social media settings
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/social-media-settings.php';

// Load WebP conversion functionality - Optimized Core Files
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-utils.php';
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-converter.php';
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-browser-detection.php';
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-hooks-integration.php';

// Load admin and CLI tools
if (is_admin() || (defined('WP_CLI') && WP_CLI)) {
    require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-admin-page.php';
    require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-cli.php';
}

// Load verification and testing scripts only in development environment
if (defined('WP_DEBUG') && WP_DEBUG && (defined('WP_DEBUG_DISPLAY') && WP_DEBUG_DISPLAY)) {
    // Only load testing files in debug mode
    if (file_exists(LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-method-check.php')) {
        require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-method-check.php';
    }
}

/**
 * Force all pages to use custom header and footer templates
 * Solve template inconsistency issues caused by mixing block themes with traditional themes
 */
function light_fixture_force_custom_templates() {
    // Remove block theme template parts, force use of PHP templates
    remove_theme_support('block-templates');
    remove_theme_support('block-template-parts');
}
add_action('after_setup_theme', 'light_fixture_force_custom_templates', 11);

/**
 * Ensure all pages use unified header and footer
 */
function light_fixture_template_redirect() {
    // If it's a page and not the front page, ensure use of page.php template
    if (is_page() && !is_front_page()) {
        // Force load custom header and footer
        add_action('wp_head', 'light_fixture_ensure_custom_styles', 999);
    }
}
add_action('template_redirect', 'light_fixture_template_redirect');

/**
 * Ensure custom styles are loaded on all pages
 */
function light_fixture_ensure_custom_styles() {
    echo '<style>
        /* Force header navigation bar styles to take effect on all pages */
        .site-header,
        header.site-header,
        .wp-site-blocks .site-header,
        body .site-header {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            background-color: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            z-index: 1000 !important;
            padding: 1.5rem 0 !important;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
        }

        /* Ensure page content is not blocked by fixed header */
        body {
            padding-top: 80px !important;
        }

        /* Ensure main navigation menu font */
        .main-navigation a,
        .wp-site-blocks .main-navigation a,
        body .main-navigation a,
        nav.main-navigation a {
            font-family: "Playfair Display", "Georgia", serif !important;
            color: #000000 !important;
        }
    </style>';
}