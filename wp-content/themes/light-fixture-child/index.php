<?php
/**
 * 主模板文件
 * 作为所有模板的后备，确保使用统一的头部和底部
 *
 * @package Light_Fixture_Child
 */

get_header(); ?>

<main id="primary" class="site-main">
    <div class="container">
        <?php if ( have_posts() ) : ?>
            
            <?php if ( is_home() && ! is_front_page() ) : ?>
                <header class="page-header">
                    <h1 class="page-title"><?php single_post_title(); ?></h1>
                </header>
            <?php endif; ?>
            
            <div class="posts-container">
                <?php while ( have_posts() ) : the_post(); ?>
                    
                    <?php if ( is_singular() ) : ?>
                        <!-- 单页面/文章显示 -->
                        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                            <?php if ( has_post_thumbnail() && ! is_front_page() ) : ?>
                                <div class="post-featured-image">
                                    <?php the_post_thumbnail( 'large' ); ?>
                                </div>
                            <?php endif; ?>
                            
                            <header class="entry-header">
                                <?php
                                if ( is_singular() ) :
                                    the_title( '<h1 class="entry-title">', '</h1>' );
                                else :
                                    the_title( '<h2 class="entry-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' );
                                endif;
                                ?>
                            </header>
                            
                            <div class="entry-content">
                                <?php
                                if ( is_singular() ) :
                                    the_content();
                                    
                                    wp_link_pages(
                                        array(
                                            'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'light-fixture-child' ),
                                            'after'  => '</div>',
                                        )
                                    );
                                else :
                                    the_excerpt();
                                endif;
                                ?>
                            </div>
                        </article>
                        
                        <?php
                        // 如果是单页面且启用了评论，显示评论
                        if ( is_singular() && ( comments_open() || get_comments_number() ) ) :
                            comments_template();
                        endif;
                        ?>
                        
                    <?php else : ?>
                        <!-- 列表页显示 -->
                        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                            <?php if ( has_post_thumbnail() ) : ?>
                                <div class="post-thumbnail">
                                    <a href="<?php the_permalink(); ?>">
                                        <?php the_post_thumbnail( 'medium' ); ?>
                                    </a>
                                </div>
                            <?php endif; ?>
                            
                            <header class="entry-header">
                                <?php the_title( '<h2 class="entry-title"><a href="' . esc_url( get_permalink() ) . '" rel="bookmark">', '</a></h2>' ); ?>
                            </header>
                            
                            <div class="entry-summary">
                                <?php the_excerpt(); ?>
                            </div>
                            
                            <footer class="entry-footer">
                                <a href="<?php the_permalink(); ?>" class="read-more">
                                    <?php esc_html_e( 'Read More', 'light-fixture-child' ); ?>
                                </a>
                            </footer>
                        </article>
                    <?php endif; ?>
                    
                <?php endwhile; ?>
                
                <?php if ( ! is_singular() ) : ?>
                    <div class="pagination">
                        <?php
                        the_posts_pagination(
                            array(
                                'mid_size'  => 2,
                                'prev_text' => esc_html__( 'Previous', 'light-fixture-child' ),
                                'next_text' => esc_html__( 'Next', 'light-fixture-child' ),
                            )
                        );
                        ?>
                    </div>
                <?php endif; ?>
                
            </div>
            
        <?php else : ?>
            
            <section class="no-results not-found">
                <header class="page-header">
                    <h1 class="page-title"><?php esc_html_e( 'Nothing here', 'light-fixture-child' ); ?></h1>
                </header>
                
                <div class="page-content">
                    <?php if ( is_home() && current_user_can( 'publish_posts' ) ) : ?>
                        <p>
                            <?php
                            printf(
                                wp_kses(
                                    /* translators: 1: link to WP admin new post page. */
                                    __( 'Ready to publish your first post? <a href="%1$s">Get started here</a>.', 'light-fixture-child' ),
                                    array(
                                        'a' => array(
                                            'href' => array(),
                                        ),
                                    )
                                ),
                                esc_url( admin_url( 'post-new.php' ) )
                            );
                            ?>
                        </p>
                    <?php elseif ( is_search() ) : ?>
                        <p><?php esc_html_e( 'Sorry, but nothing matched your search terms. Please try again with some different keywords.', 'light-fixture-child' ); ?></p>
                        <?php get_search_form(); ?>
                    <?php else : ?>
                        <p><?php esc_html_e( 'It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help.', 'light-fixture-child' ); ?></p>
                        <?php get_search_form(); ?>
                    <?php endif; ?>
                </div>
            </section>
            
        <?php endif; ?>
    </div>
</main>

<?php
get_footer();
