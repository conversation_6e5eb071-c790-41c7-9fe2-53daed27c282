<?php
// 加载 WordPress
require_once('../../../wp-load.php');

// 清理对象缓存
wp_cache_flush();

// 清理瞬态缓存
global $wpdb;
$wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_transient_%'");
$wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_site_transient_%'");

// 清理重写规则
delete_option('rewrite_rules');

// 清理主题缓存
delete_site_transient('update_themes');
delete_site_transient('update_plugins');
delete_site_transient('update_core');

// 清理菜单缓存
delete_site_transient('wp_nav_menu_cache');

// 清理自定义字段缓存
clean_post_cache(get_the_ID());

// 清理用户缓存
clean_user_cache(get_current_user_id());

// 清理分类缓存
clean_term_cache();

// 清理评论缓存
clean_comment_cache();

// 清理缓存后重定向回管理页面
wp_redirect(admin_url('admin.php?page=light-fixture-settings&cache_cleared=1'));
exit;
?> 