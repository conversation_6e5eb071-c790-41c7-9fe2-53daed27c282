<?php
/**
 * Template Name: Contact Us Template
 * Template Post Type: page
 *
 * The template for displaying the contact us page.
 *
 * @package Light_Fixture_Child
 */

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main contact-page">
        <!-- Hero Section -->
        <section class="hero-section contact-hero">
            <div class="hero-background">
                <img src="https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80" alt="Modern Design Office">
            </div>
            <div class="container">
                <div class="hero-content">
                    <span class="hero-subtitle">Contact Us</span>
                    <h1 class="hero-title">We Look Forward<br>To Starting a Conversation</h1>
                    <p class="hero-description">Whether it's product inquiries, project collaborations, or custom lighting design solutions, we look forward to hearing your needs.</p>
                </div>
            </div>
        </section>

        <!-- Contact Information & Form -->
        <section class="section">
            <div class="container">
                <div class="contact-content">
                    <!-- Contact Information -->
                    <div class="contact-info">
                        <div class="contact-info__header">
                            <h2>Contact Information</h2>
                            <p>You can reach us through the following methods. We will respond within 1-2 business days.</p>
                        </div>
                        
                        <div class="contact-info__details">
                            <div class="contact-item">
                                <div class="contact-item__icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg>
                                </div>
                                <div class="contact-item__content">
                                    <h3>Address</h3>
                                    <p>123 Light Street, Design District, City 12345, Country</p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-item__icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
                                </div>
                                <div class="contact-item__content">
                                    <h3>Phone</h3>
                                    <p>+****************</p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-item__icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg>
                                </div>
                                <div class="contact-item__content">
                                    <h3>Email</h3>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            
                            <div class="contact-item">
                                <div class="contact-item__icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>
                                </div>
                                <div class="contact-item__content">
                                    <h3>Working Hours</h3>
                                    <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                                    <p>Saturday: 10:00 AM - 4:00 PM</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="social-links">
                            <h3>Follow Us</h3>
                            <div class="social-icons">
                                <?php
                                $social_links = get_social_media_links();
                                $platform_names = array(
                                    'facebook' => 'Facebook',
                                    'twitter' => 'Twitter',
                                    'instagram' => 'Instagram',
                                    'linkedin' => 'LinkedIn',
                                    'youtube' => 'YouTube',
                                    'pinterest' => 'Pinterest',
                                    'tiktok' => 'TikTok',
                                    'whatsapp' => 'WhatsApp'
                                );

                                if (!empty($social_links)) {
                                    foreach ($social_links as $platform => $url) {
                                        $icon_svg = get_social_media_icon($platform);
                                        $platform_name = isset($platform_names[$platform]) ? $platform_names[$platform] : ucfirst($platform);

                                        if ($icon_svg) {
                                            echo '<a href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer" class="social-icon" aria-label="' . esc_attr($platform_name) . '">';
                                            echo '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">';
                                            echo $icon_svg;
                                            echo '</svg>';
                                            echo '</a>';
                                        }
                                    }
                                } else {
                                    echo '<p style="color: #666; font-style: italic;">请在后台设置中配置社交媒体链接</p>';
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact Form -->
                    <div id="contact-form" class="contact-form">
                        <div class="form-header">
                            <h2>Send Us a Message</h2>
                            <p>Please fill out the form below, and we will get back to you as soon as possible.</p>
                        </div>
                        
                        <form id="contactForm" class="form">
                            <div class="form-group">
                                <label for="name">Your Name <span class="required">*</span></label>
                                <input type="text" id="name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="email">Email Address <span class="required">*</span></label>
                                <input type="email" id="email" name="email" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone">
                            </div>
                            
                            <div class="form-group">
                                <label for="subject">Subject <span class="required">*</span></label>
                                <select id="subject" name="subject" required>
                                    <option value="">Please select...</option>
                                    <option value="Product Inquiry">Product Inquiry</option>
                                    <option value="Customization Service">Customization Service</option>
                                    <option value="Business Cooperation">Business Cooperation</option>
                                    <option value="After-Sales Service">After-Sales Service</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            
                            <div class="form-group full-width">
                                <label for="message">Your Message <span class="required">*</span></label>
                                <textarea id="message" name="message" rows="6" required></textarea>
                            </div>
                            
                            <div class="form-submit">
                                <button type="submit" class="btn btn-primary">Send Message</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Map -->
        <section class="section bg-light">
            <div class="container">
                <div class="section-header">
                    <span class="section-subtitle">Location</span>
                    <h2 class="section-title">How to Find Us</h2>
                    <p class="section-description">Visit our showroom to experience the unique charm of lighting art.</p>
                </div>
                
                <div class="map-container">
                    <!-- Interactive Google Map -->
                    <iframe 
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2624.9916256937604!2d2.292292615984509!3d48.85836827928754!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x47e66e2964e34e2d%3A0x8ddca9ee380ef7e0!2sEiffel%20Tower!5e0!3m2!1sen!2sus!4v1651868878988!5m2!1sen!2sus" 
                        width="100%" 
                        height="450" 
                        style="border:0;" 
                        allowfullscreen="" 
                        loading="lazy" 
                        referrerpolicy="no-referrer-when-downgrade">
                    </iframe>
                    <div class="map-overlay">
                        <div class="map-info">
                            <h3>Our Showroom</h3>
                            <p><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path><circle cx="12" cy="10" r="3"></circle></svg> 123 Light Street, Design District, City 12345</p>
                            <p><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect><path d="M7 11V7a5 5 0 0 1 10 0v4"></path></svg> 5-minute walk from Metro Line 1, Exit A</p>
                            <a href="https://maps.google.com" class="btn btn-secondary" target="_blank">Get Directions <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line><polyline points="12 5 19 12 12 19"></polyline></svg></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Section -->
        <section class="section">
            <div class="container">
                <div class="section-header">
                    <span class="section-subtitle">FAQ</span>
                    <h2 class="section-title">You Might Want to Know</h2>
                    <p class="section-description">We've compiled some common customer questions to help you better understand our products and services.</p>
                </div>
                
                <div class="faq-container">
                    <div class="faq-item">
                        <div class="faq-question" role="button" tabindex="0" aria-expanded="false" aria-controls="faq-answer-1" id="faq-question-1">
                            <h3>Do you offer customization services for your products?</h3>
                            <span class="faq-toggle"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></span>
                        </div>
                        <div class="faq-answer" id="faq-answer-1" aria-hidden="true">
                            <p>Yes, we offer professional lighting customization services. We can tailor unique lighting solutions based on customer's space requirements, design style, and functional needs. The customization process includes requirement communication, solution design, sample confirmation, and production/installation.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question" role="button" tabindex="0" aria-expanded="false" aria-controls="faq-answer-2" id="faq-question-2">
                            <h3>What is the warranty policy for your products?</h3>
                            <span class="faq-toggle"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></span>
                        </div>
                        <div class="faq-answer" id="faq-answer-2" aria-hidden="true">
                            <p>Our standard products come with a 2-year warranty. For custom products, the warranty varies depending on the specific project. The warranty covers material and craftsmanship defects in the product itself but does not cover damage due to improper use, external factors, or self-repair. Please consult customer service for detailed warranty terms at the time of purchase.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question" role="button" tabindex="0" aria-expanded="false" aria-controls="faq-answer-3" id="faq-question-3">
                            <h3>Do you provide installation services?</h3>
                            <span class="faq-toggle"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></span>
                        </div>
                        <div class="faq-answer" id="faq-answer-3" aria-hidden="true">
                            <p>Yes, we provide professional installation services. For large fixtures or custom projects, we strongly recommend that our professional installation team handles the installation to ensure safety and optimal results. Installation fees vary depending on project complexity and location; please consult customer service for details.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question" role="button" tabindex="0" aria-expanded="false" aria-controls="faq-answer-4" id="faq-question-4">
                            <h3>How do I choose the right lighting for my space?</h3>
                            <span class="faq-toggle"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></span>
                        </div>
                        <div class="faq-answer" id="faq-answer-4" aria-hidden="true">
                            <p>Choosing the right lighting involves considering various factors, including space size, functional needs, decor style, and personal preferences. We recommend scheduling an on-site consultation with our lighting consultants or providing us with photos and dimensions of your space so we can offer professional advice and solutions.</p>
                        </div>
                    </div>
                    
                    <div class="faq-item">
                        <div class="faq-question" role="button" tabindex="0" aria-expanded="false" aria-controls="faq-answer-5" id="faq-question-5">
                            <h3>How long is the lifespan of your LED fixtures?</h3>
                            <span class="faq-toggle"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="12" y1="5" x2="12" y2="19"></line><line x1="5" y1="12" x2="19" y2="12"></line></svg></span>
                        </div>
                        <div class="faq-answer" id="faq-answer-5" aria-hidden="true">
                            <p>Under normal usage conditions, our high-quality LED light sources have a theoretical lifespan of 30,000 to 50,000 hours, equivalent to 10 to 15 years if used 8 hours a day. Actual lifespan is also affected by factors such as usage environment, temperature, and voltage stability.</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <?php
        // Allow additional content via block editor
        while (have_posts()) :
            the_post();
            the_content();
        endwhile;
        ?>
    </main>
</div>

<style>
/* Contact us page specific styles */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 3rem;
    margin-top: 2rem;
}

.contact-info {
    background-color: var(--color-surface);
    padding: 2.5rem;
    border-radius: 0; /* 方形设计，符合设计规范 */
    box-shadow: var(--shadow-soft);
    height: fit-content;
    border: 2px solid #000000; /* 添加黑色边框，符合设计规范 */
}

.contact-info__header h2 {
    margin-bottom: 1rem;
}

.contact-info__header p {
    color: var(--color-text-secondary);
    margin-bottom: 2rem;
}

.contact-info__details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-item__icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff; /* 白色背景 */
    border-radius: 0; /* 方形设计，符合设计规范 */
    border: 2px solid #000000; /* 黑色边框 */
    color: var(--color-accent); /* 金色图标 */
}

.contact-item__content h3 {
    font-size: 1.125rem;
    margin-bottom: 0.5rem;
}

.contact-item__content p {
    color: var(--color-text-secondary);
    margin-bottom: 0.25rem;
}

.social-links h3 {
    font-size: 1.125rem;
    margin-bottom: 1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-icon {
    width: 40px;
    height: 40px;
    border-radius: 0; /* 方形设计，符合设计规范 */
    background-color: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    display: flex;
    align-items: center;
    justify-content: center;
    color: #000000; /* 黑色图标 */
    transition: all var(--transition-medium);
}

.social-icon:hover {
    background-color: #000000; /* 黑色背景 */
    transform: translateY(-3px);
    color: var(--color-accent); /* 金色图标 */
    border-color: #000000;
}

/* Contact form */
.contact-form {
    background-color: var(--color-surface);
    padding: 2.5rem;
    border-radius: 0; /* 方形设计，符合设计规范 */
    box-shadow: var(--shadow-soft);
    border: 2px solid #000000; /* 添加黑色边框，符合设计规范 */
}

.form-header h2 {
    margin-bottom: 1rem;
}

.form-header p {
    color: var(--color-text-secondary);
    margin-bottom: 2rem;
}

.form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group.full-width {
    grid-column: span 2;
}

.form-submit {
    grid-column: span 2;
    margin-top: 1rem;
}

.required {
    color: #f44336;
}

/* 修复表单字段对齐问题 */
.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.9375rem 1.25rem; /* 统一内边距 */
    border: 2px solid #000000; /* 黑色边框 */
    background-color: #ffffff; /* 白色背景 */
    font-size: 0.875rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    color: var(--color-text-dark);
    transition: border-color var(--transition-medium);
    border-radius: 0; /* 方形设计 */
    box-sizing: border-box; /* 确保一致的盒模型 */
    height: auto; /* 让高度自适应 */
    min-height: 50px; /* 设置最小高度确保一致性 */
}

/* 确保select下拉框的特殊样式 */
.form-group select {
    appearance: none; /* 移除默认样式 */
    -webkit-appearance: none;
    -moz-appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23000000' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 16px;
    padding-right: 3rem; /* 为下拉箭头留出空间 */
}

/* 聚焦状态 */
.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="tel"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--color-accent); /* 金色聚焦边框 */
    box-shadow: 0 0 0 1px var(--color-accent);
}

/* 标签样式统一 */
.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Playfair Display', 'Georgia', serif;
    color: var(--color-text-dark);
}

/* Map area */
.map-container {
    position: relative;
    width: 100%;
    height: 450px;
    border-radius: 0; /* 方形设计，符合设计规范 */
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    margin-top: 3rem;
    border: 2px solid #000000; /* 添加黑色边框，符合设计规范 */
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.map-info {
    position: absolute;
    top: 2rem;
    left: 2rem;
    background-color: var(--color-surface);
    padding: 2rem;
    border-radius: 0; /* 方形设计，符合设计规范 */
    max-width: 350px;
    box-shadow: var(--shadow-medium);
    pointer-events: auto;
    border: 2px solid #000000; /* 添加黑色边框，符合设计规范 */
}

.map-info h3 {
    margin-bottom: 1.5rem;
}

.map-info p {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    color: var(--color-text-secondary);
}

.map-info p svg {
    color: var(--color-accent);
    flex-shrink: 0;
}

.map-info .btn {
    display: inline-flex;
    align-items: center;
    margin-top: 1rem;
}

.map-info .btn svg {
    margin-left: 0.5rem;
    transition: transform var(--transition-medium);
}

.map-info .btn:hover svg {
    transform: translateX(4px);
}

/* FAQ styles */
.faq-container {
    max-width: 900px;
    margin: 3rem auto 0;
}

.faq-item {
    margin-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.faq-question {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    cursor: pointer;
}

.faq-question h3 {
    font-size: 1.125rem;
    margin: 0;
}

.faq-toggle {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-background);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-dark);
    transition: all var(--transition-medium);
}

.faq-toggle svg {
    transition: transform var(--transition-medium);
}

.faq-item.active .faq-toggle {
    background-color: var(--color-accent);
    color: white;
}

.faq-item.active .faq-toggle svg {
    transform: rotate(45deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-slow), padding var(--transition-medium);
}

.faq-item.active .faq-answer {
    max-height: 300px;
    padding-bottom: 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 991px) {
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .map-info {
        position: relative;
        top: auto;
        left: auto;
        margin: 0 auto;
        max-width: 100%;
        transform: translateY(-50%);
    }
}

@media (max-width: 767px) {
    .form {
        grid-template-columns: 1fr;
    }

    .form-group.full-width,
    .form-submit {
        grid-column: 1;
    }

    .map-container {
        height: 500px;
    }

    /* 移动端表单字段调整 */
    .contact-form {
        padding: 2rem;
    }

    .contact-info {
        padding: 2rem;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* 移动端地图信息框调整 */
    .map-info {
        position: relative;
        top: auto;
        left: auto;
        margin: 1rem;
        max-width: none;
        transform: translateY(-50%);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // FAQ accordion effect
    const faqItems = document.querySelectorAll('.faq-item');
    
    if (faqItems.length) {
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            
            question.addEventListener('click', function() {
                // If this item is active, close it
                if (item.classList.contains('active')) {
                    item.classList.remove('active');
                    question.setAttribute('aria-expanded', 'false');
                    item.querySelector('.faq-answer').setAttribute('aria-hidden', 'true');
                } else {
                    // First close all open items
                    faqItems.forEach(faq => {
                        faq.classList.remove('active');
                        faq.querySelector('.faq-question').setAttribute('aria-expanded', 'false');
                        faq.querySelector('.faq-answer').setAttribute('aria-hidden', 'true');
                    });
                    
                    // Open current item
                    item.classList.add('active');
                    question.setAttribute('aria-expanded', 'true');
                    item.querySelector('.faq-answer').setAttribute('aria-hidden', 'false');
                }
            });
        });
    }
    
    // Form submission handler
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // In a real project, this would include form validation and submission logic
            alert('Thank you for your message. We will contact you shortly!');
            contactForm.reset();
        });
    }

    // Animation effect
    const animatedElements = document.querySelectorAll('.fade-in, .slide-up');
    
    if ('IntersectionObserver' in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        animatedElements.forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            observer.observe(el);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        animatedElements.forEach(el => {
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        });
    }
});
</script>

<?php
get_footer();
?> 