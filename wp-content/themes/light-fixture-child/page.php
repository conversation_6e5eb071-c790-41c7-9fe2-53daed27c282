<?php
/**
 * 页面模板
 * 确保所有页面都使用统一的头部和底部
 *
 * @package Light_Fixture_Child
 */

get_header(); ?>

<main id="primary" class="site-main">
    <?php while ( have_posts() ) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <div class="container">
                <div class="page-content">
                    <?php if ( has_post_thumbnail() ) : ?>
                        <div class="page-featured-image">
                            <?php the_post_thumbnail( 'large' ); ?>
                        </div>
                    <?php endif; ?>
                    
                    <header class="page-header">
                        <?php the_title( '<h1 class="page-title">', '</h1>' ); ?>
                    </header>
                    
                    <div class="page-content-wrapper">
                        <?php
                        the_content();
                        
                        wp_link_pages(
                            array(
                                'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'light-fixture-child' ),
                                'after'  => '</div>',
                            )
                        );
                        ?>
                    </div>
                </div>
            </div>
        </article>
        
        <?php
        // 如果启用了评论，显示评论
        if ( comments_open() || get_comments_number() ) :
            comments_template();
        endif;
        ?>
    <?php endwhile; ?>
</main>

<?php
get_footer();
