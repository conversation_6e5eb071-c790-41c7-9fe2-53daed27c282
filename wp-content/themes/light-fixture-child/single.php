<?php
/**
 * 单页面/文章模板
 * 确保所有单页面都使用统一的头部和底部
 *
 * @package Light_Fixture_Child
 */

get_header(); ?>

<main id="primary" class="site-main">
    <?php while ( have_posts() ) : the_post(); ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <div class="container">
                <div class="single-content">
                    <?php if ( has_post_thumbnail() ) : ?>
                        <div class="post-featured-image">
                            <?php the_post_thumbnail( 'large' ); ?>
                        </div>
                    <?php endif; ?>
                    
                    <header class="entry-header">
                        <?php the_title( '<h1 class="entry-title">', '</h1>' ); ?>
                        
                        <?php if ( 'post' === get_post_type() ) : ?>
                            <div class="entry-meta">
                                <span class="posted-on">
                                    <?php echo get_the_date(); ?>
                                </span>
                                <?php if ( get_the_author() ) : ?>
                                    <span class="byline">
                                        <?php esc_html_e( 'by', 'light-fixture-child' ); ?> 
                                        <?php the_author(); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </header>
                    
                    <div class="entry-content">
                        <?php
                        the_content();
                        
                        wp_link_pages(
                            array(
                                'before' => '<div class="page-links">' . esc_html__( 'Pages:', 'light-fixture-child' ),
                                'after'  => '</div>',
                            )
                        );
                        ?>
                    </div>
                    
                    <?php if ( 'post' === get_post_type() ) : ?>
                        <footer class="entry-footer">
                            <?php
                            $categories_list = get_the_category_list( esc_html__( ', ', 'light-fixture-child' ) );
                            if ( $categories_list ) {
                                printf( '<span class="cat-links">' . esc_html__( 'Posted in %1$s', 'light-fixture-child' ) . '</span>', $categories_list );
                            }
                            
                            $tags_list = get_the_tag_list( '', esc_html_x( ', ', 'list item separator', 'light-fixture-child' ) );
                            if ( $tags_list ) {
                                printf( '<span class="tags-links">' . esc_html__( 'Tagged %1$s', 'light-fixture-child' ) . '</span>', $tags_list );
                            }
                            ?>
                        </footer>
                    <?php endif; ?>
                </div>
            </div>
        </article>
        
        <?php
        // 文章导航
        if ( 'post' === get_post_type() ) :
            the_post_navigation(
                array(
                    'prev_text' => '<span class="nav-subtitle">' . esc_html__( 'Previous:', 'light-fixture-child' ) . '</span> <span class="nav-title">%title</span>',
                    'next_text' => '<span class="nav-subtitle">' . esc_html__( 'Next:', 'light-fixture-child' ) . '</span> <span class="nav-title">%title</span>',
                )
            );
        endif;
        
        // 如果启用了评论，显示评论
        if ( comments_open() || get_comments_number() ) :
            comments_template();
        endif;
        ?>
    <?php endwhile; ?>
</main>

<?php
get_footer();
