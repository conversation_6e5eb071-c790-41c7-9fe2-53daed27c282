<?php
/**
 * 测试工艺展示卡片区块
 * 
 * 这个文件用于测试新创建的工艺展示卡片区块是否正常工作
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        <div class="container" style="padding: 40px 0;">
            <h1>工艺展示卡片区块测试</h1>
            <p>这个页面用于测试新创建的工艺展示卡片区块。</p>
            
            <h2>测试区块渲染</h2>
            <?php
            // 测试区块渲染函数
            if (function_exists('light_fixture_render_craftsmanship_cards_block')) {
                echo light_fixture_render_craftsmanship_cards_block(array());
            } else {
                echo '<p style="color: red;">错误：渲染函数不存在</p>';
            }
            ?>
            
            <h2>测试自定义属性</h2>
            <?php
            // 测试自定义属性
            $custom_attributes = array(
                'title' => '测试标题',
                'subtitle' => 'Test Subtitle',
                'description' => '这是一个测试描述，用于验证区块是否能正确显示自定义内容。',
                'columns' => 3,
                'rows' => 2,
                'cards' => array(
                    array(
                        'number' => 'A',
                        'title' => '测试卡片1',
                        'description' => '这是第一个测试卡片的描述内容。',
                        'icon' => '',
                    ),
                    array(
                        'number' => 'B',
                        'title' => '测试卡片2',
                        'description' => '这是第二个测试卡片的描述内容。',
                        'icon' => '',
                    ),
                    array(
                        'number' => 'C',
                        'title' => '测试卡片3',
                        'description' => '这是第三个测试卡片的描述内容。',
                        'icon' => '',
                    ),
                ),
            );
            
            if (function_exists('light_fixture_render_craftsmanship_cards_block')) {
                echo light_fixture_render_craftsmanship_cards_block($custom_attributes);
            } else {
                echo '<p style="color: red;">错误：渲染函数不存在</p>';
            }
            ?>
            
            <h2>区块注册状态</h2>
            <?php
            // 检查区块是否已注册
            if (function_exists('get_dynamic_block_names')) {
                $registered_blocks = get_dynamic_block_names();
                if (in_array('light-fixture/craftsmanship-cards-block', $registered_blocks)) {
                    echo '<p style="color: green;">✓ 区块已成功注册</p>';
                } else {
                    echo '<p style="color: red;">✗ 区块未注册</p>';
                    echo '<p>已注册的区块：</p>';
                    echo '<ul>';
                    foreach ($registered_blocks as $block) {
                        if (strpos($block, 'light-fixture') !== false) {
                            echo '<li>' . esc_html($block) . '</li>';
                        }
                    }
                    echo '</ul>';
                }
            } else {
                echo '<p style="color: orange;">无法检查区块注册状态（WordPress版本过低）</p>';
            }
            ?>
            
            <h2>脚本和样式加载状态</h2>
            <?php
            // 检查脚本和样式是否已加载
            global $wp_scripts, $wp_styles;
            
            echo '<h3>JavaScript文件：</h3>';
            $js_files = array(
                'light-fixture-craftsmanship-cards-editor' => '/assets/js/blocks/craftsmanship-cards-block.js',
            );
            
            foreach ($js_files as $handle => $file) {
                if (isset($wp_scripts->registered[$handle])) {
                    echo '<p style="color: green;">✓ ' . $handle . ' 已注册</p>';
                } else {
                    echo '<p style="color: red;">✗ ' . $handle . ' 未注册</p>';
                }
            }
            
            echo '<h3>CSS文件：</h3>';
            $css_files = array(
                'light-fixture-craftsmanship-cards-editor' => '/assets/css/blocks/craftsmanship-cards-editor.css',
                'light-fixture-blocks' => '/assets/css/blocks/blocks.css',
            );
            
            foreach ($css_files as $handle => $file) {
                if (isset($wp_styles->registered[$handle])) {
                    echo '<p style="color: green;">✓ ' . $handle . ' 已注册</p>';
                } else {
                    echo '<p style="color: red;">✗ ' . $handle . ' 未注册</p>';
                }
            }
            ?>
            
            <h2>文件存在性检查</h2>
            <?php
            $files_to_check = array(
                'JavaScript文件' => get_stylesheet_directory() . '/assets/js/blocks/craftsmanship-cards-block.js',
                '编辑器CSS文件' => get_stylesheet_directory() . '/assets/css/blocks/craftsmanship-cards-editor.css',
                '前端CSS文件' => get_stylesheet_directory() . '/assets/css/blocks/blocks.css',
            );
            
            foreach ($files_to_check as $name => $file_path) {
                if (file_exists($file_path)) {
                    echo '<p style="color: green;">✓ ' . $name . ' 存在</p>';
                } else {
                    echo '<p style="color: red;">✗ ' . $name . ' 不存在：' . $file_path . '</p>';
                }
            }
            ?>
            
            <h2>使用说明</h2>
            <div style="background: #f9f9f9; padding: 20px; border-radius: 4px; margin: 20px 0;">
                <h3>如何使用工艺展示卡片区块：</h3>
                <ol>
                    <li>在WordPress后台编辑页面或文章</li>
                    <li>点击"+"按钮添加新区块</li>
                    <li>在"设计"分类下找到"工艺展示卡片"区块</li>
                    <li>添加区块后，可以在右侧面板中自定义：
                        <ul>
                            <li>区块标题和描述</li>
                            <li>布局（列数和行数）</li>
                            <li>每个卡片的内容和图标</li>
                        </ul>
                    </li>
                    <li>保存页面即可在前端看到效果</li>
                </ol>
                
                <h3>支持的布局：</h3>
                <ul>
                    <li>5列1行（默认，与原始设计相同）</li>
                    <li>4列2行</li>
                    <li>3列2行</li>
                    <li>2列3行</li>
                    <li>自定义列数和行数</li>
                </ul>
            </div>
        </div>
    </main>
</div>

<?php
get_footer();
?>
