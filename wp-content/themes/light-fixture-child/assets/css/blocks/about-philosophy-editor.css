/**
 * 关于我们的理念区块编辑器样式
 */

/* 区块编辑器容器 */
.about-philosophy-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.about-philosophy-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 1200px;
    margin: 0 auto;
}

/* 区块标题样式 */
.about-philosophy-block-preview .section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.about-philosophy-block-preview .section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #d6ad60;
    margin-bottom: 1rem;
    display: block;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.about-philosophy-block-preview .section-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 600;
    color: #000;
    line-height: 1.3;
}

/* 理念网格布局 */
.about-philosophy-block-preview .philosophy-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 3rem;
    min-width: 0; /* 防止网格项目溢出 */
}

/* 理念项目样式 */
.about-philosophy-block-preview .philosophy-item {
    background-color: #ffffff;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s ease;
    min-width: 0; /* 防止内容溢出 */
    overflow: hidden; /* 隐藏溢出内容 */
}

.about-philosophy-block-preview .philosophy-item:hover {
    transform: translateY(-5px);
}

/* 理念图标样式 */
.about-philosophy-block-preview .philosophy-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto 1.5rem;
    position: relative;
}

.about-philosophy-block-preview .philosophy-icon svg {
    color: #d6ad60;
    stroke-width: 1;
    width: 100%;
    height: 100%;
    display: block;
}

.about-philosophy-block-preview .philosophy-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.about-philosophy-block-preview .philosophy-icon div {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 理念标题样式 */
.about-philosophy-block-preview .philosophy-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #000;
    font-family: 'Playfair Display', 'Georgia', serif;
}

/* 理念描述样式 */
.about-philosophy-block-preview .philosophy-item p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #333;
    margin: 0;
}

/* 编辑器特定样式 */
.about-philosophy-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.about-philosophy-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.about-philosophy-block-editor .components-text-control__input,
.about-philosophy-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.about-philosophy-block-editor .editor-post-featured-image__toggle,
.about-philosophy-block-editor .editor-post-featured-image__preview {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    cursor: pointer;
    text-align: center;
    font-size: 13px;
}

.about-philosophy-block-editor .editor-post-featured-image__toggle:hover,
.about-philosophy-block-editor .editor-post-featured-image__preview:hover {
    background: #f0f0f0;
    border-color: #999;
}

/* 删除按钮样式 */
.about-philosophy-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
}

.about-philosophy-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 小按钮样式 */
.about-philosophy-block-editor .is-small {
    font-size: 11px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

/* 帮助文本样式 */
.about-philosophy-block-editor .components-base-control__help {
    font-size: 12px;
    color: #757575;
    margin-top: 8px;
}

/* 段落编辑区域 */
.about-philosophy-block-editor .components-textarea-control__input {
    min-height: 80px;
    resize: vertical;
}

/* 背景色 */
.about-philosophy-block-preview .section.bg-light {
    background-color: #f8f9fa;
    padding: 2rem;
    border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 1400px) {
    .about-philosophy-block-preview .philosophy-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .about-philosophy-block-preview .philosophy-item {
        padding: 1.5rem;
    }
}

@media (max-width: 900px) {
    .about-philosophy-block-preview .philosophy-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .about-philosophy-block-preview .philosophy-item {
        padding: 1rem;
    }
}

@media (max-width: 768px) {
    .about-philosophy-block-preview .philosophy-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .about-philosophy-block-preview .section-title {
        font-size: 1.5rem;
    }
    
    .about-philosophy-block-preview .philosophy-item {
        padding: 1.5rem;
    }
}

/* 预览区域边距 */
.about-philosophy-block-preview .section {
    padding: 2rem 0;
}

.about-philosophy-block-preview .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 动画延迟效果 */
.about-philosophy-block-preview .fade-in.delay-0 {
    animation-delay: 0s;
}

.about-philosophy-block-preview .fade-in.delay-1 {
    animation-delay: 0.1s;
}

.about-philosophy-block-preview .fade-in.delay-2 {
    animation-delay: 0.2s;
}

.about-philosophy-block-preview .fade-in.delay-3 {
    animation-delay: 0.3s;
}
