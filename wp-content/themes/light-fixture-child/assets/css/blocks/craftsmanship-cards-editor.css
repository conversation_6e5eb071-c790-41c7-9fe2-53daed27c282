/**
 * 工艺展示卡片区块编辑器样式
 */

/* 区块编辑器容器 */
.craftsmanship-cards-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.craftsmanship-cards-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 区块标题样式 */
.craftsmanship-cards-block-preview .section-header {
    margin-bottom: 30px;
    text-align: center;
}

.craftsmanship-cards-block-preview .section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #d6ad60;
    margin-bottom: 1rem;
    display: block;
}

.craftsmanship-cards-block-preview .section-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.craftsmanship-cards-block-preview .section-description {
    font-size: 1rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

/* 工艺流程网格 */
.craftsmanship-cards-block-preview .craftsmanship-process {
    display: grid;
    gap: 1.5rem;
    margin-top: 3rem;
}

/* 流程项目样式 */
.craftsmanship-cards-block-preview .process-item {
    position: relative;
    padding: 2rem;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
    border: 1px solid #f0f0f0;
}

.craftsmanship-cards-block-preview .process-item:hover {
    transform: translateY(-5px);
}

/* 数字标识 */
.craftsmanship-cards-block-preview .process-number {
    font-size: 3rem;
    font-weight: 200;
    color: #d6ad60;
    opacity: 0.3;
    position: absolute;
    top: 1rem;
    right: 1rem;
    line-height: 1;
    font-family: 'Playfair Display', 'Georgia', serif;
}

/* 图标样式 */
.craftsmanship-cards-block-preview .process-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 32px;
    height: 32px;
    z-index: 2;
}

.craftsmanship-cards-block-preview .process-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 标题样式 */
.craftsmanship-cards-block-preview .process-item h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 500;
    color: #000;
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* 标题淡出淡入动画效果 */
.craftsmanship-cards-block-preview .process-item:hover h3 {
    animation: titleFadeInOut 2s ease-in-out infinite;
}

@keyframes titleFadeInOut {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* 描述样式 */
.craftsmanship-cards-block-preview .process-item p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: #333;
    margin: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .craftsmanship-cards-block-preview .craftsmanship-process {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (max-width: 991px) {
    .craftsmanship-cards-block-preview .craftsmanship-process {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 767px) {
    .craftsmanship-cards-block-preview .craftsmanship-process {
        grid-template-columns: 1fr !important;
    }
    
    .craftsmanship-cards-block-preview .process-item {
        padding: 1.5rem;
    }
    
    .craftsmanship-cards-block-preview .process-number {
        font-size: 2.5rem;
    }
}

/* 编辑器特定样式 */
.craftsmanship-cards-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.craftsmanship-cards-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.craftsmanship-cards-block-editor .components-text-control__input,
.craftsmanship-cards-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.craftsmanship-cards-block-editor .editor-post-featured-image__toggle,
.craftsmanship-cards-block-editor .editor-post-featured-image__preview {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    cursor: pointer;
    text-align: center;
    font-size: 13px;
}

.craftsmanship-cards-block-editor .editor-post-featured-image__toggle:hover,
.craftsmanship-cards-block-editor .editor-post-featured-image__preview:hover {
    background: #f0f0f0;
    border-color: #999;
}

/* 删除按钮样式 */
.craftsmanship-cards-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
}

.craftsmanship-cards-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 布局选择器样式 */
.craftsmanship-cards-block-editor .components-select-control__input {
    font-size: 13px;
    padding: 6px 8px;
}

/* 范围控制器样式 */
.craftsmanship-cards-block-editor .components-range-control__wrapper {
    margin-bottom: 16px;
}

.craftsmanship-cards-block-editor .components-range-control__number {
    font-size: 13px;
}

/* 面板间距调整 */
.craftsmanship-cards-block-editor .components-panel__body {
    border-bottom: 1px solid #e2e4e7;
}

.craftsmanship-cards-block-editor .components-panel__body:last-child {
    border-bottom: none;
}

/* 预览区域边距 */
.craftsmanship-cards-block-preview .section {
    padding: 2rem 0;
}

.craftsmanship-cards-block-preview .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 背景图样式 */
.craftsmanship-cards-block-preview .section {
    position: relative;
    overflow: hidden;
}

.craftsmanship-cards-block-preview .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.craftsmanship-cards-block-preview .container {
    position: relative;
    z-index: 2;
}

/* 当有背景图时的样式调整 */
.craftsmanship-cards-block-preview .section[style*="background-image"] .section-title,
.craftsmanship-cards-block-preview .section[style*="background-image"] .section-subtitle,
.craftsmanship-cards-block-preview .section[style*="background-image"] .section-description {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.craftsmanship-cards-block-preview .section[style*="background-image"] .process-item {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.craftsmanship-cards-block-preview .section[style*="background-image"] .process-item:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 媒体上传按钮样式 */
.craftsmanship-cards-block-editor .editor-post-featured-image__toggle {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px 16px;
    border: 2px dashed #d6ad60;
    border-radius: 4px;
    background: #fefefe;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    color: #d6ad60;
    font-weight: 500;
    transition: all 0.3s ease;
}

.craftsmanship-cards-block-editor .editor-post-featured-image__toggle:hover {
    background: #f9f6f0;
    border-color: #c19a4a;
    color: #c19a4a;
}

.craftsmanship-cards-block-editor .editor-post-featured-image__preview {
    background: #d6ad60;
    color: #fff;
    border-color: #d6ad60;
}

.craftsmanship-cards-block-editor .editor-post-featured-image__preview:hover {
    background: #c19a4a;
    border-color: #c19a4a;
}
