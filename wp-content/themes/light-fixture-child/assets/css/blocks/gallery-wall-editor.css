/**
 * 图片画廊墙区块编辑器样式
 */

/* 区块编辑器容器 */
.gallery-wall-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.gallery-wall-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 画廊区域样式 */
.gallery-wall-block-preview .gallery-wall {
    padding: 2rem 0;
    text-align: center;
}

.gallery-wall-block-preview .gallery-wall.light {
    background-color: #f8f9fa;
}

.gallery-wall-block-preview .gallery-wall.white {
    background-color: #ffffff;
}

.gallery-wall-block-preview .gallery-wall.dark {
    background-color: #333333;
    color: #ffffff;
}

/* 容器样式 */
.gallery-wall-block-preview .center {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

/* 标题样式 */
.gallery-wall-block-preview .title {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 600;
    margin-bottom: 1rem;
    color: inherit;
}

.gallery-wall-block-preview .gallery-description {
    font-size: 1rem;
    line-height: 1.6;
    max-width: 600px;
    margin: 1rem auto;
}

/* 图片网格样式 */
.gallery-wall-block-preview .flexW {
    display: grid;
    gap: 20px;
    margin-top: 2rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

/* 图片项目样式 */
.gallery-wall-block-preview .item {
    position: relative;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.gallery-wall-block-preview .item:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 图片容器样式 */
.gallery-wall-block-preview .imgW {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}

.gallery-wall-block-preview .imgW img {
    transition: transform 0.3s ease;
    display: block;
}

/* 悬停效果 */
.gallery-wall-block-preview .hover-scale:hover img {
    transform: scale(1.05);
}

.gallery-wall-block-preview .hover-fade:hover img {
    opacity: 0.8;
}

.gallery-wall-block-preview .hover-moveUp:hover {
    transform: translateY(-5px);
}

/* 删除按钮样式 */
.gallery-wall-block-preview .gallery-remove-image {
    opacity: 0;
    transition: opacity 0.2s ease;
}

.gallery-wall-block-preview .item:hover .gallery-remove-image {
    opacity: 1;
}

.gallery-wall-block-preview .gallery-remove-image:hover {
    background: rgba(214, 54, 56, 0.9) !important;
    color: white !important;
}

/* 占位符样式 */
.gallery-wall-block-preview .gallery-placeholder {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
}

/* 按钮样式 */
.gallery-wall-block-preview .btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: #d6ad60;
    color: #ffffff;
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.gallery-wall-block-preview .btn:hover {
    background-color: #c19a4a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(214, 173, 96, 0.3);
}

/* 编辑器特定样式 */
.gallery-wall-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.gallery-wall-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.gallery-wall-block-editor .components-text-control__input,
.gallery-wall-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.gallery-wall-block-editor .editor-post-featured-image__toggle {
    width: 100%;
    margin-bottom: 10px;
    padding: 12px 16px;
    border: 2px dashed #d6ad60;
    border-radius: 4px;
    background: #fefefe;
    cursor: pointer;
    text-align: center;
    font-size: 14px;
    color: #d6ad60;
    font-weight: 500;
    transition: all 0.3s ease;
}

.gallery-wall-block-editor .editor-post-featured-image__toggle:hover {
    background: #f9f6f0;
    border-color: #c19a4a;
    color: #c19a4a;
}

/* 删除按钮样式 */
.gallery-wall-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
    background: transparent;
    padding: 8px 12px;
    font-size: 12px;
}

.gallery-wall-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 范围控制器样式 */
.gallery-wall-block-editor .components-range-control__wrapper {
    margin-bottom: 16px;
}

.gallery-wall-block-editor .components-range-control__number {
    font-size: 13px;
}

/* 选择控制器样式 */
.gallery-wall-block-editor .components-select-control__input {
    font-size: 13px;
    padding: 6px 8px;
}

/* 切换控制器样式 */
.gallery-wall-block-editor .components-toggle-control {
    margin-bottom: 16px;
}

/* Alt文本输入框样式 */
.gallery-wall-block-preview input[type="text"] {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.gallery-wall-block-preview input[type="text"]:focus {
    outline: none;
    border-color: #d6ad60;
    box-shadow: 0 0 0 1px #d6ad60;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .gallery-wall-block-preview .flexW {
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .gallery-wall-block-preview .flexW {
        gap: 10px;
    }
    
    .gallery-wall-block-preview .title {
        font-size: 28px !important;
    }
    
    .gallery-wall-block-preview .center {
        padding: 0 15px;
    }
}

/* 预览区域边距 */
.gallery-wall-block-preview .gallery-wall {
    margin: 0;
}

/* 确保预览区域有合适的最小高度 */
.gallery-wall-block-preview {
    min-height: 200px;
}

/* 图片加载状态 */
.gallery-wall-block-preview .imgW img {
    background-color: #f5f5f5;
}

.gallery-wall-block-preview .imgW img:not([src]) {
    opacity: 0;
}

/* 工具提示样式 */
.gallery-wall-block-editor .components-base-control__help {
    font-size: 12px;
    color: #757575;
    margin-top: 8px;
}

/* 面板间距调整 */
.gallery-wall-block-editor .components-panel__body {
    border-bottom: 1px solid #e2e4e7;
}

.gallery-wall-block-editor .components-panel__body:last-child {
    border-bottom: none;
}

/* 按钮组样式 */
.gallery-wall-block-editor .components-button-group {
    margin-bottom: 16px;
}

.gallery-wall-block-editor .components-button-group .components-button {
    font-size: 12px;
}

/* 颜色选择器样式 */
.gallery-wall-block-editor .components-color-palette {
    margin-bottom: 16px;
}

/* 确保编辑器中的图片正确显示 */
.gallery-wall-block-preview .imgW {
    background-color: #f8f9fa;
}

.gallery-wall-block-preview .imgW::before {
    content: '';
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 10px 10px;
    background-position: 0 0, 0 5px, 5px -5px, -5px 0px;
    opacity: 0.1;
    z-index: 0;
}

.gallery-wall-block-preview .imgW img {
    position: relative;
    z-index: 1;
}
