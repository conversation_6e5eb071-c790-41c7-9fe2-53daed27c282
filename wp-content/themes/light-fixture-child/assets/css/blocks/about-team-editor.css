/**
 * 关于设计团队区块编辑器样式
 */

/* 区块编辑器容器 */
.about-team-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.about-team-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 区块标题样式 */
.about-team-block-preview .section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.about-team-block-preview .section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #d6ad60;
    margin-bottom: 1rem;
    display: block;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.about-team-block-preview .section-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 600;
    color: #000;
}

.about-team-block-preview .section-description {
    font-size: 1rem;
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* 团队网格布局 */
.about-team-block-preview .team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

/* 团队成员样式 */
.about-team-block-preview .team-member {
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.about-team-block-preview .team-member:hover {
    transform: translateY(-5px);
}

/* 团队成员头像 */
.about-team-block-preview .team-member__image {
    position: relative;
    width: 100%;
    padding-bottom: 100%; /* 1:1 比例 */
    overflow: hidden;
    background-color: #f5f5f5;
}

.about-team-block-preview .team-member__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 团队成员信息 */
.about-team-block-preview .team-member__info {
    padding: 1.5rem;
}

.about-team-block-preview .team-member__name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #000;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.about-team-block-preview .team-member__title {
    font-size: 0.95rem;
    font-weight: 500;
    color: #d6ad60;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.about-team-block-preview .team-member__info p:last-child {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #333;
    margin: 0;
}

/* 编辑器特定样式 */
.about-team-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.about-team-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.about-team-block-editor .components-text-control__input,
.about-team-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.about-team-block-editor .editor-post-featured-image__toggle,
.about-team-block-editor .editor-post-featured-image__preview {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    cursor: pointer;
    text-align: center;
    font-size: 13px;
}

.about-team-block-editor .editor-post-featured-image__toggle:hover,
.about-team-block-editor .editor-post-featured-image__preview:hover {
    background: #f0f0f0;
    border-color: #999;
}

/* 删除按钮样式 */
.about-team-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
}

.about-team-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 小按钮样式 */
.about-team-block-editor .is-small {
    font-size: 11px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

/* 段落编辑区域 */
.about-team-block-editor .components-textarea-control__input {
    min-height: 80px;
    resize: vertical;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .about-team-block-preview .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .about-team-block-preview .team-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .about-team-block-preview .section-title {
        font-size: 1.5rem;
    }
    
    .about-team-block-preview .team-member__info {
        padding: 1.25rem;
    }
}

/* 预览区域边距 */
.about-team-block-preview .section {
    padding: 2rem 0;
}

.about-team-block-preview .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 动画延迟效果 */
.about-team-block-preview .fade-in.delay-0 {
    animation-delay: 0s;
}

.about-team-block-preview .fade-in.delay-1 {
    animation-delay: 0.1s;
}

.about-team-block-preview .fade-in.delay-2 {
    animation-delay: 0.2s;
}

.about-team-block-preview .fade-in.delay-3 {
    animation-delay: 0.3s;
}

/* 确保头像容器有合适的背景 */
.about-team-block-preview .team-member__image:empty {
    background-color: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.about-team-block-preview .team-member__image:empty::after {
    content: '暂无头像';
    color: #999;
    font-size: 14px;
}
