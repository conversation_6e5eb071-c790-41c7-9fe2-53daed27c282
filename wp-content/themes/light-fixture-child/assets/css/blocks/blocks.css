/**
 * 自定义区块样式
 */

/* 轮播图区块样式 */
.wp-block-light-fixture-slider-block {
    margin-bottom: 40px;
}

/* 使用现有的轮播图样式，这些样式已经在主题的 style.css 中定义 */

/* 产品列表区块样式 */
.products-list-block {
    margin-bottom: 60px;
    padding: 40px 0;
}

/* 产品网格布局 */
.products-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 25px;
    margin-bottom: 30px;
}

/* 根据列数调整网格 */
.products-grid--1 {
    grid-template-columns: repeat(1, 1fr);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.products-grid--2 {
    grid-template-columns: repeat(2, 1fr);
}

.products-grid--3 {
    grid-template-columns: repeat(3, 1fr);
}

.products-grid--4 {
    grid-template-columns: repeat(4, 1fr);
}

.products-grid--6 {
    grid-template-columns: repeat(6, 1fr);
    gap: 15px;
}

/* 产品项样式 */
.product-item {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
}

.product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

/* 不同显示模式的样式 */
.product-item.display-mode-below {
    display: flex;
    flex-direction: column;
}

.product-item.display-mode-below .product-item__image-container {
    flex: 0 0 auto;
}

.product-item.display-mode-below .product-item__content {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

/* 产品图片容器 */
.product-item__image-container {
    position: relative;
    padding-top: 100%; /* 1:1 比例，正方形 */
    background-color: #f5f5f5;
    overflow: hidden;
}

.product-item__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-item:hover .product-item__image {
    transform: scale(1.08);
}

/* 产品状态标签 */
.product-item__status-tag {
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 6px 12px;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    z-index: 2;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 产品悬停覆盖层 */
.product-item__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.4s ease;
    text-align: center;
}

/* 悬停模式下的覆盖层 */
.product-item.display-mode-hover:hover .product-item__overlay {
    opacity: 1;
}

/* 下方显示模式下的覆盖层 - 只显示按钮 */
.product-item.display-mode-below:hover .product-item__overlay {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.2); /* 稍微透明一些 */
}

/* 悬停内容样式 */
.product-item__hover-content {
    margin-bottom: 15px;
    max-width: 100%;
    transform: translateY(20px);
    transition: transform 0.5s ease;
    opacity: 0;
}

.product-item:hover .product-item__hover-content {
    transform: translateY(0);
    opacity: 1;
}

.product-item__overlay .product-item__title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 10px;
    color: #fff;
    line-height: 1.3;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.product-item__overlay .product-item__description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin: 0 0 15px;
    line-height: 1.5;
    max-height: 6em;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.product-item__overlay .product-item__meta {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 10px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    padding-top: 8px;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

.product-item__view-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #fff;
    color: #333;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transform: translateY(20px);
    transition: transform 0.4s ease, background-color 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    opacity: 0;
}

.product-item:hover .product-item__view-button {
    transform: translateY(0);
    opacity: 1;
}

/* 下方显示模式下的按钮样式 */
.product-item.display-mode-below .product-item__view-button {
    /* margin-top: auto; */
}

.product-item__view-button:hover {
    background-color: #000000;
}

/* 产品内容样式 */
.product-item__content {
    padding: 18px 20px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
}

/* 下方显示模式的内容样式 */
.product-item.display-mode-below .product-item__content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.product-item__title {
    font-size: 16px;
    margin: 0 0 10px;
    color: #333;
    font-weight: 600;
    line-height: 1.4;
}

.product-item__title a {
    color: inherit;
    text-decoration: none;
    transition: color 0.2s ease;
}

.product-item__title a:hover {
    color: #666;
}

.product-item__description {
    font-size: 14px;
    color: #666;
    margin: 0 0 12px;
    line-height: 1.5;
}

.product-item__meta {
    font-size: 13px;
    color: #999;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.products-list-block .text-center {
    text-align: center;
}

.products-list-block .mt-5 {
    margin-top: 40px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .products-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
}

@media (max-width: 992px) {
    .products-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .products-list-block {
        padding: 30px 0;
    }
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .product-item__content {
        padding: 15px;
    }
    
    .products-list-block {
        padding: 20px 0;
    }
}

@media (max-width: 480px) {
    .products-grid {
        grid-template-columns: repeat(1, 1fr);
        max-width: 320px;
        margin-left: auto;
        margin-right: auto;
    }
}

/* =========================================
   工艺展示卡片区块样式
   ========================================= */

/* 工艺展示卡片区块样式 */
.wp-block-light-fixture-craftsmanship-cards-block {
    margin-bottom: 40px;
}

/* 工艺流程网格 */
.craftsmanship-process {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5rem;
    margin-top: 3rem;
}

/* 流程项目样式 */
.process-item {
    position: relative;
    padding: 2rem;
    background-color: var(--color-surface);
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
    transition: transform var(--transition-medium);
}

.process-item:hover {
    transform: translateY(-5px);
}

/* 数字标识 */
.process-number {
    font-size: 3rem;
    font-weight: 200;
    color: var(--color-accent);
    opacity: 0.3;
    position: absolute;
    top: 1rem;
    right: 1rem;
    line-height: 1;
}

/* 图标样式 */
.process-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 32px;
    height: 32px;
    z-index: 2;
}

.process-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 标题样式 */
.process-item h3 {
    font-size: 1.25rem;
    margin-bottom: 1rem;
    font-weight: 500;
    color: var(--color-text-dark);
    opacity: 1;
    transition: opacity 0.3s ease;
}

/* 标题淡出淡入动画效果 */
.process-item:hover h3 {
    animation: titleFadeInOut 2s ease-in-out infinite;
}

@keyframes titleFadeInOut {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* 可选的其他动画效果（注释掉，可根据需要启用） */
/*
// 脉冲效果
.process-item:hover h3 {
    animation: titlePulse 1.5s ease-in-out infinite;
}

@keyframes titlePulse {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.05); }
    100% { opacity: 1; transform: scale(1); }
}

// 闪烁效果
.process-item:hover h3 {
    animation: titleBlink 1s ease-in-out infinite;
}

@keyframes titleBlink {
    0%, 50% { opacity: 1; }
    25%, 75% { opacity: 0.2; }
}

// 呼吸效果
.process-item:hover h3 {
    animation: titleBreathe 3s ease-in-out infinite;
}

@keyframes titleBreathe {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.4; }
}
*/

/* 工艺展示卡片区块背景图样式 */
.wp-block-light-fixture-craftsmanship-cards-block .section {
    position: relative;
    overflow: hidden;
}

.wp-block-light-fixture-craftsmanship-cards-block .background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.wp-block-light-fixture-craftsmanship-cards-block .container {
    position: relative;
    z-index: 2;
}

/* 当有背景图时，确保文字有足够的对比度 */
.wp-block-light-fixture-craftsmanship-cards-block .section[style*="background-image"] .section-title,
.wp-block-light-fixture-craftsmanship-cards-block .section[style*="background-image"] .section-subtitle,
.wp-block-light-fixture-craftsmanship-cards-block .section[style*="background-image"] .section-description {
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.wp-block-light-fixture-craftsmanship-cards-block .section[style*="background-image"] .process-item {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.wp-block-light-fixture-craftsmanship-cards-block .section[style*="background-image"] .process-item:hover {
    background-color: rgba(255, 255, 255, 1);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* =========================================
   关于页面区块样式
   ========================================= */

/* 关于英雄区域区块样式 */
.wp-block-light-fixture-about-hero-block {
    margin-bottom: 0;
}

.wp-block-light-fixture-about-hero-block .hero-section {
    position: relative;
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.wp-block-light-fixture-about-hero-block .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.wp-block-light-fixture-about-hero-block .hero-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.wp-block-light-fixture-about-hero-block .hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wp-block-light-fixture-about-hero-block .container {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
}

.wp-block-light-fixture-about-hero-block .hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.wp-block-light-fixture-about-hero-block .hero-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--color-accent);
    margin-bottom: 1rem;
    display: block;
}

.wp-block-light-fixture-about-hero-block .hero-title {
    font-size: 3rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    color: #fff;
}

.wp-block-light-fixture-about-hero-block .hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
}

/* 关于品牌故事区块样式 */
.wp-block-light-fixture-about-brand-story-block {
    margin-bottom: 40px;
}

.wp-block-light-fixture-about-brand-story-block .about-story {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 2rem;
}

.wp-block-light-fixture-about-brand-story-block .about-story__image img {
    width: 100%;
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
}

.wp-block-light-fixture-about-brand-story-block .about-story__content p {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--color-text-secondary);
    margin-bottom: 1.5rem;
}

.wp-block-light-fixture-about-brand-story-block .about-story__content p:last-child {
    margin-bottom: 0;
}

/* 关于我们的理念区块样式 */
.wp-block-light-fixture-about-philosophy-block {
    margin-bottom: 40px;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-item {
    background-color: var(--color-surface);
    padding: 2rem;
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
    text-align: center;
    transition: transform var(--transition-medium);
}

.wp-block-light-fixture-about-philosophy-block .philosophy-item:hover {
    transform: translateY(-5px);
}

.wp-block-light-fixture-about-philosophy-block .philosophy-icon {
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-icon svg {
    color: var(--color-accent);
    stroke-width: 1;
    width: 100%;
    height: 100%;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-icon img {
    object-fit: contain;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-icon div {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wp-block-light-fixture-about-philosophy-block .philosophy-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--color-text-dark);
}

.wp-block-light-fixture-about-philosophy-block .philosophy-item p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin: 0;
}

/* 关于设计团队区块样式 */
.wp-block-light-fixture-about-team-block {
    margin-bottom: 40px;
}

.wp-block-light-fixture-about-team-block .team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.wp-block-light-fixture-about-team-block .team-member {
    background-color: var(--color-surface);
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
    overflow: hidden;
    transition: transform var(--transition-medium);
}

.wp-block-light-fixture-about-team-block .team-member:hover {
    transform: translateY(-5px);
}

.wp-block-light-fixture-about-team-block .team-member__image {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    overflow: hidden;
    background-color: #f5f5f5;
}

.wp-block-light-fixture-about-team-block .team-member__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.wp-block-light-fixture-about-team-block .team-member__info {
    padding: 1.5rem;
}

.wp-block-light-fixture-about-team-block .team-member__name {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--color-text-dark);
}

.wp-block-light-fixture-about-team-block .team-member__title {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--color-accent);
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.wp-block-light-fixture-about-team-block .team-member__info p:last-child {
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin: 0;
}

/* 关于页面区块响应式样式 */
@media (max-width: 1200px) {
    .wp-block-light-fixture-about-philosophy-block .philosophy-grid,
    .wp-block-light-fixture-about-team-block .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 991px) {
    .wp-block-light-fixture-about-brand-story-block .about-story {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .wp-block-light-fixture-about-brand-story-block .about-story__image {
        order: 1;
    }

    .wp-block-light-fixture-about-brand-story-block .about-story__content {
        order: 2;
    }
}

@media (max-width: 768px) {
    .wp-block-light-fixture-about-hero-block .hero-title {
        font-size: 2rem;
    }

    .wp-block-light-fixture-about-hero-block .hero-description {
        font-size: 1rem;
    }

    .wp-block-light-fixture-about-philosophy-block .philosophy-grid,
    .wp-block-light-fixture-about-team-block .team-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .wp-block-light-fixture-about-philosophy-block .philosophy-item,
    .wp-block-light-fixture-about-team-block .team-member__info {
        padding: 1.5rem;
    }
}

/* =========================================
   图片画廊墙区块样式
   ========================================= */

/* 图片画廊墙区块样式 */
.wp-block-light-fixture-gallery-wall-block {
    margin-bottom: 40px;
}

.wp-block-light-fixture-gallery-wall-block .gallery-wall {
    padding: 3rem 0;
    text-align: center;
}

.wp-block-light-fixture-gallery-wall-block .gallery-wall.light {
    background-color: var(--color-background-light);
}

.wp-block-light-fixture-gallery-wall-block .gallery-wall.white {
    background-color: var(--color-surface);
}

.wp-block-light-fixture-gallery-wall-block .gallery-wall.dark {
    background-color: var(--color-text-dark);
    color: var(--color-surface);
}

/* 容器样式 */
.wp-block-light-fixture-gallery-wall-block .center {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

/* 标题样式 */
.wp-block-light-fixture-gallery-wall-block .title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: inherit;
    line-height: 1.2;
}

.wp-block-light-fixture-gallery-wall-block .gallery-description {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 1rem auto;
}

.wp-block-light-fixture-gallery-wall-block .gallery-wall.dark .gallery-description {
    color: rgba(255, 255, 255, 0.8);
}

/* 图片网格样式 */
.wp-block-light-fixture-gallery-wall-block .flexW {
    display: grid;
    gap: 1.5rem;
    margin-top: 2rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

/* 图片项目样式 */
.wp-block-light-fixture-gallery-wall-block .item {
    position: relative;
    background-color: var(--color-surface);
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
    overflow: hidden;
    transition: all var(--transition-medium);
}

.wp-block-light-fixture-gallery-wall-block .item:hover {
    box-shadow: var(--shadow-hover);
}

/* 图片容器样式 */
.wp-block-light-fixture-gallery-wall-block .imgW {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}

.wp-block-light-fixture-gallery-wall-block .imgW img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-medium);
    display: block;
}

/* 悬停效果 */
.wp-block-light-fixture-gallery-wall-block .hover-scale:hover img {
    transform: scale(1.05);
}

.wp-block-light-fixture-gallery-wall-block .hover-fade:hover img {
    opacity: 0.8;
}

.wp-block-light-fixture-gallery-wall-block .hover-moveUp:hover {
    transform: translateY(-5px);
}

/* 按钮样式 */
.wp-block-light-fixture-gallery-wall-block .btnBox {
    margin-top: 2rem;
    z-index: 10;
    position: relative;
}

.wp-block-light-fixture-gallery-wall-block .btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: var(--color-accent);
    color: var(--color-surface);
    text-decoration: none;
    border-radius: 4px;
    font-weight: 500;
    transition: all var(--transition-medium);
    border: none;
    cursor: pointer;
    font-size: 1rem;
}

.wp-block-light-fixture-gallery-wall-block .btn:hover {
    background-color: var(--color-accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(214, 173, 96, 0.3);
    color: var(--color-surface);
    text-decoration: none;
}

/* 图片比例样式 */
.wp-block-light-fixture-gallery-wall-block .ratio-square {
    padding-top: 100%;
}

.wp-block-light-fixture-gallery-wall-block .ratio-landscape {
    padding-top: 75%;
}

.wp-block-light-fixture-gallery-wall-block .ratio-portrait {
    padding-top: 133.33%;
}

.wp-block-light-fixture-gallery-wall-block .ratio-wide {
    padding-top: 56.25%;
}

.wp-block-light-fixture-gallery-wall-block .ratio-original {
    padding-top: auto;
}

/* 图片容器定位 */
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-square,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-landscape,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-portrait,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-wide {
    position: relative;
}

.wp-block-light-fixture-gallery-wall-block .imgW.ratio-square img,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-landscape img,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-portrait img,
.wp-block-light-fixture-gallery-wall-block .imgW.ratio-wide img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.wp-block-light-fixture-gallery-wall-block .imgW.ratio-original img {
    position: static;
    width: 100%;
    height: auto;
}

/* 响应式样式 */
@media (max-width: 1200px) {
    .wp-block-light-fixture-gallery-wall-block .flexW {
        gap: 1.25rem;
    }
}

@media (max-width: 991px) {
    .wp-block-light-fixture-gallery-wall-block .flexW {
        gap: 1rem;
    }

    .wp-block-light-fixture-gallery-wall-block .title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .wp-block-light-fixture-gallery-wall-block .flexW {
        gap: 0.75rem;
    }

    .wp-block-light-fixture-gallery-wall-block .title {
        font-size: 1.75rem;
    }

    .wp-block-light-fixture-gallery-wall-block .gallery-wall {
        padding: 2rem 0;
    }
}

@media (max-width: 480px) {
    .wp-block-light-fixture-gallery-wall-block .flexW {
        gap: 0.5rem;
    }

    .wp-block-light-fixture-gallery-wall-block .title {
        font-size: 1.5rem;
    }
}

/* 描述样式 */
.process-item p {
    font-size: 0.95rem;
    line-height: 1.6;
    color: var(--color-text-secondary);
    margin: 0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
    .craftsmanship-process {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) {
    .craftsmanship-process {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .craftsmanship-process {
        grid-template-columns: 1fr;
    }

    .process-item {
        padding: 1.5rem;
    }

    .process-number {
        font-size: 2.5rem;
    }
}