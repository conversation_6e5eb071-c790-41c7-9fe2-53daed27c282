/**
 * 产品列表区块编辑器样式
 */

/* 区块编辑器容器 */
.products-list-block-editor {
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

/* 区块预览容器 */
.products-list-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 区块标题样式 */
.products-list-block-preview .section-header {
    margin-bottom: 30px;
    text-align: center;
}

.products-list-block-preview .section-subtitle {
    display: block;
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.products-list-block-preview .section-title {
    font-size: 28px;
    margin: 0 0 10px;
    color: #333;
}

.products-list-block-preview .section-description {
    font-size: 16px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

/* 产品网格布局 */
.products-list-block-preview .products-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

/* 根据列数调整网格 */
.products-list-block-preview .products-grid--1 {
    grid-template-columns: repeat(1, 1fr);
}

.products-list-block-preview .products-grid--2 {
    grid-template-columns: repeat(2, 1fr);
}

.products-list-block-preview .products-grid--3 {
    grid-template-columns: repeat(3, 1fr);
}

.products-list-block-preview .products-grid--4 {
    grid-template-columns: repeat(4, 1fr);
}

.products-list-block-preview .products-grid--6 {
    grid-template-columns: repeat(6, 1fr);
}

/* 产品项样式 */
.products-list-block-preview .product-item {
    background-color: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.products-list-block-preview .product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 不同显示模式的样式 */
.products-list-block-preview .product-item.display-mode-below {
    display: flex;
    flex-direction: column;
}

.products-list-block-preview .product-item.display-mode-below .product-item__image-container {
    flex: 0 0 auto;
}

.products-list-block-preview .product-item.display-mode-below .product-item__content {
    flex: 1 1 auto;
    display: flex;
    flex-direction: column;
}

/* 产品图片容器 */
.products-list-block-preview .product-item__image-container {
    position: relative;
    padding-top: 75%; /* 4:3 比例 */
    background-color: #f0f0f0;
    overflow: hidden;
}

.products-list-block-preview .product-item__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.products-list-block-preview .product-item:hover .product-item__image {
    transform: scale(1.05);
}

.products-list-block-preview .product-item__placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
    color: #999;
    font-size: 14px;
}

/* 产品覆盖层样式 */
.products-list-block-preview .product-item__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

/* 悬停模式下的覆盖层 */
.products-list-block-preview .product-item.display-mode-hover:hover .product-item__overlay {
    opacity: 1;
}

/* 下方显示模式下的覆盖层 - 只显示按钮 */
.products-list-block-preview .product-item.display-mode-below:hover .product-item__overlay {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.2);
}

/* 悬停内容样式 */
.products-list-block-preview .product-item__hover-content {
    margin-bottom: 15px;
    padding: 10px;
    text-align: center;
}

.products-list-block-preview .product-item__overlay .product-item__title {
    color: #fff;
    font-size: 16px;
    margin: 0 0 8px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.products-list-block-preview .product-item__overlay .product-item__description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    margin: 0;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

/* 查看按钮样式 */
.products-list-block-preview .product-item__view-button {
    display: inline-block;
    padding: 8px 15px;
    background-color: #fff;
    color: #333;
    border-radius: 3px;
    text-decoration: none;
    font-size: 13px;
    transition: background-color 0.3s ease;
}

.products-list-block-preview .product-item__view-button:hover {
    background-color: #f0f0f0;
}

/* 产品内容样式 */
.products-list-block-preview .product-item__content {
    padding: 15px;
}

.products-list-block-preview .product-item__title {
    font-size: 16px;
    margin: 0 0 8px;
    color: #333;
}

.products-list-block-preview .product-item__description {
    font-size: 14px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* 按钮样式 */
.products-list-block-preview .btn {
    display: inline-block;
    padding: 10px 20px;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    text-decoration: none;
    font-size: 14px;
    transition: background-color 0.3s ease;
}

.products-list-block-preview .btn-secondary {
    background-color: #666;
}

.products-list-block-preview .btn:hover {
    background-color: #444;
}

.products-list-block-preview .text-center {
    text-align: center;
}

.products-list-block-preview .mt-5 {
    margin-top: 30px;
}

/* 加载状态样式 */
.products-list-block-preview .loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.products-list-block-preview .no-products-message {
    padding: 30px;
    text-align: center;
    background-color: #f9f9f9;
    border-radius: 4px;
    color: #666;
}

/* 自定义产品编辑器样式 */
.custom-products-editor {
    margin-top: 20px;
}

.product-editor {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.product-editor h3 {
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.product-image-control {
    margin: 15px 0;
}

.image-preview {
    margin-top: 10px;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    margin-bottom: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.image-preview button {
    margin-right: 10px;
}

/* 响应式调整 */
@media (max-width: 782px) {
    .products-list-block-preview .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .products-list-block-preview .products-grid--1 {
        grid-template-columns: repeat(1, 1fr);
    }
} 