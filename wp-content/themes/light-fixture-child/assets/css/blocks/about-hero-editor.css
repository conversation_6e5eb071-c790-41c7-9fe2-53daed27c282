/**
 * 关于英雄区域区块编辑器样式
 */

/* 区块编辑器容器 */
.about-hero-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.about-hero-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
}

/* 英雄区域样式 */
.about-hero-block-preview .hero-section {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 4px;
}

.about-hero-block-preview .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.about-hero-block-preview .hero-background::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.about-hero-block-preview .hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 内容样式 */
.about-hero-block-preview .container {
    position: relative;
    z-index: 2;
    text-align: center;
    color: #fff;
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

.about-hero-block-preview .hero-content {
    padding: 2rem;
}

.about-hero-block-preview .hero-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #d6ad60;
    margin-bottom: 1rem;
    display: block;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.about-hero-block-preview .hero-title {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    font-family: 'Playfair Display', 'Georgia', serif;
    color: #fff;
}

.about-hero-block-preview .hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 编辑器特定样式 */
.about-hero-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.about-hero-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.about-hero-block-editor .components-text-control__input,
.about-hero-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.about-hero-block-editor .editor-post-featured-image__toggle,
.about-hero-block-editor .editor-post-featured-image__preview {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    cursor: pointer;
    text-align: center;
    font-size: 13px;
}

.about-hero-block-editor .editor-post-featured-image__toggle:hover,
.about-hero-block-editor .editor-post-featured-image__preview:hover {
    background: #f0f0f0;
    border-color: #999;
}

/* 删除按钮样式 */
.about-hero-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
}

.about-hero-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 帮助文本样式 */
.about-hero-block-editor .components-base-control__help {
    font-size: 12px;
    color: #757575;
    margin-top: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .about-hero-block-preview .hero-title {
        font-size: 2rem;
    }
    
    .about-hero-block-preview .hero-description {
        font-size: 1rem;
    }
    
    .about-hero-block-preview .hero-content {
        padding: 1.5rem;
    }
}

/* 预览区域边距 */
.about-hero-block-preview .hero-section {
    margin: 0;
}

/* 确保预览区域有合适的最小高度 */
.about-hero-block-preview {
    min-height: 400px;
}
