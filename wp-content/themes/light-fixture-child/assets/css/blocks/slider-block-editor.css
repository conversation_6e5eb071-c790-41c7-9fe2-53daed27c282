/**
 * 轮播图区块编辑器样式
 */

/* 区块编辑器容器 */
.light-fixture-slider-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块编辑器标题 */
.slider-block-editor h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    color: #23282d;
    text-align: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #e2e4e7;
}

/* 幻灯片预览区域 */
.slider-previews {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 30px;
}

/* 单个幻灯片预览 */
.slide-preview {
    flex: 0 0 calc(33.333% - 10px);
    min-width: 250px;
    background-color: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

@media (max-width: 782px) {
    .slide-preview {
        flex: 0 0 100%;
    }
}

/* 幻灯片预览头部 */
.slide-preview-header {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e2e4e7;
}

.slide-preview-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

/* 幻灯片预览内容 */
.slide-preview-content {
    height: 150px;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f0f0f0;
}

/* 幻灯片预览图片 */
.slide-preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 幻灯片预览占位符 */
.slide-preview-placeholder {
    color: #888;
    text-align: center;
    font-size: 14px;
}

/* 幻灯片编辑区域 */
.slider-editors {
    margin-top: 30px;
}

/* 单个幻灯片编辑器 */
.slide-editor {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.slide-editor h3 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e4e7;
}

/* 图片上传区域 */
.slide-image-upload {
    margin-bottom: 20px;
}

.slide-image-upload label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 13px;
}

/* 图片预览 */
.image-preview {
    margin-bottom: 10px;
    max-width: 300px;
    max-height: 200px;
    overflow: hidden;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

.image-preview img {
    width: 100%;
    height: auto;
    display: block;
}

/* 按钮控制区域 */
.button-controls {
    margin-top: 20px;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.button-controls h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

/* 分隔线 */
.slide-editor hr {
    margin: 20px 0;
    border: 0;
    border-top: 1px solid #e2e4e7;
}

/* 控制按钮样式 */
.components-button + .components-button {
    margin-left: 8px;
} 