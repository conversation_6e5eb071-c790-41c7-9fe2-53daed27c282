/**
 * 关于品牌故事区块编辑器样式
 */

/* 区块编辑器容器 */
.about-brand-story-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块预览容器 */
.about-brand-story-block-preview {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 区块标题样式 */
.about-brand-story-block-preview .section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 3rem;
}

.about-brand-story-block-preview .section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #d6ad60;
    margin-bottom: 1rem;
    display: block;
    font-family: 'Playfair Display', 'Georgia', serif;
}

.about-brand-story-block-preview .section-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 600;
    color: #000;
}

/* 品牌故事布局 */
.about-brand-story-block-preview .about-story {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    margin-top: 2rem;
}

.about-brand-story-block-preview .about-story__image {
    position: relative;
}

.about-brand-story-block-preview .about-story__image img {
    width: 100%;
    border-radius: 4px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
    object-fit: cover;
    height: auto;
}

.about-brand-story-block-preview .about-story__content {
    padding: 0;
}

.about-brand-story-block-preview .about-story__content p {
    font-size: 1rem;
    line-height: 1.7;
    color: #333;
    margin-bottom: 1.5rem;
}

.about-brand-story-block-preview .about-story__content p:last-child {
    margin-bottom: 0;
}

/* 编辑器特定样式 */
.about-brand-story-block-editor .components-panel__body-title {
    font-size: 13px;
    font-weight: 500;
}

.about-brand-story-block-editor .components-base-control__label {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    color: #1e1e1e;
    margin-bottom: 8px;
}

.about-brand-story-block-editor .components-text-control__input,
.about-brand-story-block-editor .components-textarea-control__input {
    font-size: 13px;
}

/* 媒体上传按钮样式 */
.about-brand-story-block-editor .editor-post-featured-image__toggle,
.about-brand-story-block-editor .editor-post-featured-image__preview {
    width: 100%;
    margin-bottom: 10px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #f7f7f7;
    cursor: pointer;
    text-align: center;
    font-size: 13px;
}

.about-brand-story-block-editor .editor-post-featured-image__toggle:hover,
.about-brand-story-block-editor .editor-post-featured-image__preview:hover {
    background: #f0f0f0;
    border-color: #999;
}

/* 删除按钮样式 */
.about-brand-story-block-editor .is-destructive {
    color: #cc1818;
    border-color: #cc1818;
}

.about-brand-story-block-editor .is-destructive:hover {
    background: #cc1818;
    color: #fff;
}

/* 添加按钮样式 */
.about-brand-story-block-editor .is-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.about-brand-story-block-editor .is-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

/* 小按钮样式 */
.about-brand-story-block-editor .is-small {
    font-size: 11px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
}

/* 段落编辑区域 */
.about-brand-story-block-editor .components-textarea-control__input {
    min-height: 80px;
    resize: vertical;
}

/* 响应式调整 */
@media (max-width: 991px) {
    .about-brand-story-block-preview .about-story {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-brand-story-block-preview .about-story__image {
        order: 1;
    }
    
    .about-brand-story-block-preview .about-story__content {
        order: 2;
    }
}

@media (max-width: 768px) {
    .about-brand-story-block-preview .section-title {
        font-size: 1.5rem;
    }
    
    .about-brand-story-block-preview .about-story {
        gap: 1.5rem;
    }
}

/* 预览区域边距 */
.about-brand-story-block-preview .section {
    padding: 2rem 0;
}

.about-brand-story-block-preview .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}
