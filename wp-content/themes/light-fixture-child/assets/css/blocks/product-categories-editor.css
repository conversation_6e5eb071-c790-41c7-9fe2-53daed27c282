/**
 * 产品类目导航区块编辑器样式
 */

/* 区块编辑器容器 */
.product-categories-block-editor {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
}

/* 区块编辑器标题 */
.product-categories-preview .section-header {
    margin-bottom: 30px;
    text-align: center;
}

.product-categories-preview .section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: #0073aa;
    margin-bottom: 1rem;
    display: block;
}

.product-categories-preview .section-title {
    font-size: 1.75rem;
    margin-bottom: 1rem;
}

.product-categories-preview .section-description {
    font-size: 1rem;
    color: #666;
    max-width: 800px;
    margin: 0 auto;
}

/* 产品网格预览 */
.products-grid-preview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

/* 产品卡片预览 */
.product-card-preview {
    background-color: #fff;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.product-card__image-container-preview {
    position: relative;
    height: 200px;
    background-color: #eee;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-card__view-button-preview {
    padding: 8px 16px;
    background-color: #fff;
    color: #333;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.product-card__content-preview {
    padding: 16px;
}

.product-card__title-preview {
    font-size: 1.125rem;
    margin-bottom: 8px;
}

.product-card__description-preview {
    font-size: 0.875rem;
    color: #666;
}

/* 自定义类目编辑器 */
.custom-category-preview {
    margin-bottom: 30px;
}

.category-editor {
    background-color: #fff;
    padding: 20px;
    border: 1px solid #e2e4e7;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.category-editor h3 {
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e2e4e7;
}

.category-image-control {
    margin-bottom: 20px;
}

.category-image-control label {
    display: block;
    margin-bottom: 8px;
}

.image-preview {
    margin-top: 10px;
}

.image-preview img {
    max-width: 100%;
    max-height: 200px;
    margin-bottom: 10px;
    border: 1px solid #e2e4e7;
    padding: 5px;
    background-color: #f8f9fa;
}

/* 加载状态 */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
}

.loading-spinner p {
    margin-top: 10px;
    color: #666;
}

/* 查看全部按钮预览 */
.view-all-button-preview {
    text-align: center;
    margin-top: 30px;
}

.btn.btn-secondary {
    display: inline-block;
    padding: 10px 20px;
    background-color: transparent;
    border: 1px solid #0073aa;
    color: #0073aa;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.05em;
    transition: all 0.3s ease;
    cursor: pointer;
}

/* 响应式布局 */
@media (max-width: 991px) {
    .products-grid-preview {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .products-grid-preview {
        grid-template-columns: 1fr;
    }
} 