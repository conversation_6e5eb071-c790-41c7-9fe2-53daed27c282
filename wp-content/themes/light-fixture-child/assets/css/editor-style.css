/**
 * <PERSON><PERSON><PERSON>编辑器样式
 * 确保编辑器中的样式与前端保持一致
 */

/* 导入主题字体 */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500&display=swap');

/* 编辑器基础样式 */
.editor-styles-wrapper {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-size: 17px;
    line-height: 1.7;
    color: #000000;
}

/* 编辑器中的标题样式 */
.editor-styles-wrapper h1,
.editor-styles-wrapper h2,
.editor-styles-wrapper h3,
.editor-styles-wrapper h4,
.editor-styles-wrapper h5,
.editor-styles-wrapper h6,
.editor-styles-wrapper .wp-block-heading {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 600;
    line-height: 1.2;
    margin-top: 0;
    margin-bottom: 1rem;
}

.editor-styles-wrapper h1,
.editor-styles-wrapper .wp-block-heading h1 {
    font-size: 2.5rem;
}

.editor-styles-wrapper h2,
.editor-styles-wrapper .wp-block-heading h2 {
    font-size: 2rem;
}

.editor-styles-wrapper h3,
.editor-styles-wrapper .wp-block-heading h3 {
    font-size: 1.75rem;
}

.editor-styles-wrapper h4,
.editor-styles-wrapper .wp-block-heading h4 {
    font-size: 1.5rem;
}

.editor-styles-wrapper h5,
.editor-styles-wrapper .wp-block-heading h5 {
    font-size: 1.25rem;
}

.editor-styles-wrapper h6,
.editor-styles-wrapper .wp-block-heading h6 {
    font-size: 1.125rem;
}

/* 标题区块文本对齐样式 */
.editor-styles-wrapper .wp-block-heading.has-text-align-left,
.editor-styles-wrapper h1.has-text-align-left,
.editor-styles-wrapper h2.has-text-align-left,
.editor-styles-wrapper h3.has-text-align-left,
.editor-styles-wrapper h4.has-text-align-left,
.editor-styles-wrapper h5.has-text-align-left,
.editor-styles-wrapper h6.has-text-align-left {
    text-align: left;
}

.editor-styles-wrapper .wp-block-heading.has-text-align-center,
.editor-styles-wrapper h1.has-text-align-center,
.editor-styles-wrapper h2.has-text-align-center,
.editor-styles-wrapper h3.has-text-align-center,
.editor-styles-wrapper h4.has-text-align-center,
.editor-styles-wrapper h5.has-text-align-center,
.editor-styles-wrapper h6.has-text-align-center {
    text-align: center;
}

.editor-styles-wrapper .wp-block-heading.has-text-align-right,
.editor-styles-wrapper h1.has-text-align-right,
.editor-styles-wrapper h2.has-text-align-right,
.editor-styles-wrapper h3.has-text-align-right,
.editor-styles-wrapper h4.has-text-align-right,
.editor-styles-wrapper h5.has-text-align-right,
.editor-styles-wrapper h6.has-text-align-right {
    text-align: right;
}

/* 段落区块文本对齐样式 */
.editor-styles-wrapper .wp-block-paragraph.has-text-align-left {
    text-align: left;
}

.editor-styles-wrapper .wp-block-paragraph.has-text-align-center {
    text-align: center;
}

.editor-styles-wrapper .wp-block-paragraph.has-text-align-right {
    text-align: right;
}

/* 通用对齐样式 */
.editor-styles-wrapper .has-text-align-left {
    text-align: left;
}

.editor-styles-wrapper .has-text-align-center {
    text-align: center;
}

.editor-styles-wrapper .has-text-align-right {
    text-align: right;
}

/* 颜色样式 */
.editor-styles-wrapper .has-primary-gold-color {
    color: #d6ad60;
}

.editor-styles-wrapper .has-primary-gold-background-color {
    background-color: #d6ad60;
}

.editor-styles-wrapper .has-light-gold-color {
    color: #e4c078;
}

.editor-styles-wrapper .has-dark-gold-color {
    color: #c19a4a;
}

.editor-styles-wrapper .has-pure-black-color {
    color: #000000;
}

.editor-styles-wrapper .has-pure-white-color {
    color: #ffffff;
}

.editor-styles-wrapper .has-dark-gray-color {
    color: #333333;
}

/* 字体大小样式 */
.editor-styles-wrapper .has-small-font-size {
    font-size: 14px;
}

.editor-styles-wrapper .has-normal-font-size {
    font-size: 17px;
}

.editor-styles-wrapper .has-medium-font-size {
    font-size: 20px;
}

.editor-styles-wrapper .has-large-font-size {
    font-size: 24px;
}

.editor-styles-wrapper .has-extra-large-font-size {
    font-size: 32px;
}

.editor-styles-wrapper .has-huge-font-size {
    font-size: 48px;
}

/* 确保编辑器中的容器宽度 */
.editor-styles-wrapper .wp-block {
    max-width: 1200px;
}

.editor-styles-wrapper .wp-block[data-align="wide"] {
    max-width: 1400px;
}

.editor-styles-wrapper .wp-block[data-align="full"] {
    max-width: none;
}

/* 编辑器中的段落样式 */
.editor-styles-wrapper p {
    font-size: 17px;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

/* 编辑器中的链接样式 */
.editor-styles-wrapper a {
    color: #d6ad60;
    text-decoration: none;
    transition: color 0.3s ease;
}

.editor-styles-wrapper a:hover {
    color: #c19a4a;
    text-decoration: underline;
}

/* 编辑器中的列表样式 */
.editor-styles-wrapper ul,
.editor-styles-wrapper ol {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.editor-styles-wrapper li {
    margin-bottom: 0.5rem;
}

/* 编辑器中的引用样式 */
.editor-styles-wrapper blockquote {
    border-left: 4px solid #d6ad60;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #333333;
}

/* 编辑器中的代码样式 */
.editor-styles-wrapper code {
    background-color: #f5f5f5;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.editor-styles-wrapper pre {
    background-color: #f5f5f5;
    padding: 1rem;
    border-radius: 4px;
    overflow-x: auto;
    margin: 1.5rem 0;
}

/* 编辑器中的表格样式 */
.editor-styles-wrapper table {
    width: 100%;
    border-collapse: collapse;
    margin: 1.5rem 0;
}

.editor-styles-wrapper th,
.editor-styles-wrapper td {
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    text-align: left;
}

.editor-styles-wrapper th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 编辑器中的图片样式 */
.editor-styles-wrapper img {
    max-width: 100%;
    height: auto;
}

.editor-styles-wrapper .wp-block-image {
    margin: 1.5rem 0;
}

/* 编辑器中的按钮样式 */
.editor-styles-wrapper .wp-block-button__link {
    background-color: #d6ad60;
    color: #ffffff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    text-decoration: none;
    display: inline-block;
    transition: background-color 0.3s ease;
}

.editor-styles-wrapper .wp-block-button__link:hover {
    background-color: #c19a4a;
}
