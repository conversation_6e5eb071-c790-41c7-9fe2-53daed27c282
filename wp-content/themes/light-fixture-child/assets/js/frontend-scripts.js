/**
 * light-fixture-child-theme/assets/js/frontend-scripts.js
 * 网站前端通用脚本
 * 包含通用动画观察器、左侧滑出菜单功能等。
 */
document.addEventListener('DOMContentLoaded', function() {
    // 通用动画观察器
    const animateOnScroll = function() {
        const animatedElements = document.querySelectorAll('.fade-in:not(.animated), .slide-up:not(.animated)');
        
        if (animatedElements.length > 0) {
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animated');
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.1
            });
            
            animatedElements.forEach(el => {
                observer.observe(el);
            });
        }
    };
    
    // 运行动画
    animateOnScroll();
    
    // 对AJAX加载的内容重新应用动画
    document.addEventListener('ajaxComplete', function() {
        animateOnScroll();
    });

    // 左侧滑出菜单功能
    const bodyElement = document.body;
    const leftPanelToggle = document.querySelector('.left-panel-toggle');
    const leftPanelContainer = document.getElementById('left-panel-menu-container');
    const leftPanelOverlay = document.querySelector('.left-panel-overlay');
    const leftPanelMenuItems = leftPanelContainer ? leftPanelContainer.querySelectorAll('a') : [];

    // 创建关闭按钮
    if (leftPanelContainer) {
        const closeButton = document.createElement('button');
        closeButton.className = 'left-panel-close';
        closeButton.setAttribute('aria-label', '关闭菜单');
        closeButton.innerHTML = '<span class=\"screen-reader-text\">关闭菜单</span>';
        leftPanelContainer.insertBefore(closeButton, leftPanelContainer.firstChild);

        // 添加关闭按钮点击事件
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Close button clicked');
            deactivateLeftPanel();
        });
    }

    // Activate left panel
    function activateLeftPanel() {
        if (!leftPanelContainer || !leftPanelToggle) return;

        // Show overlay immediately
        bodyElement.classList.add('left-panel-open');

        // Slightly delay panel display for better visual effect
        setTimeout(() => {
            leftPanelContainer.classList.add('active');
            leftPanelToggle.setAttribute('aria-expanded', 'true');
            leftPanelContainer.focus();
        }, 50); // 50ms delay to let overlay show first

        // Debug information
        console.log('Panel activated');
        console.log('Body classes:', bodyElement.className);
        console.log('Panel classes:', leftPanelContainer.className);
    }

    // Close left panel
    function deactivateLeftPanel() {
        if (!leftPanelContainer || !leftPanelToggle) return;
        
        leftPanelContainer.classList.remove('active');
        leftPanelToggle.setAttribute('aria-expanded', 'false');
        leftPanelToggle.classList.remove('active');
        bodyElement.classList.remove('left-panel-open');
        leftPanelToggle.focus();
        
        // 调试信息
        console.log('Panel deactivated');
        console.log('Body classes:', bodyElement.className);
        console.log('Panel classes:', leftPanelContainer.className);
    }

    // 切换按钮点击事件
    if (leftPanelToggle) {
        leftPanelToggle.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Toggle clicked');
            if (bodyElement.classList.contains('left-panel-open')) {
                deactivateLeftPanel();
            } else {
                activateLeftPanel();
                this.classList.add('active');
            }
        });
    }

    // 遮罩点击事件
    if (leftPanelOverlay) {
        leftPanelOverlay.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Overlay clicked');
            deactivateLeftPanel();
        });
    }

    // 键盘事件
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && bodyElement.classList.contains('left-panel-open')) {
            console.log('Escape pressed');
            deactivateLeftPanel();
        }
    });

    // 菜单项点击事件（已优化，支持子菜单展开）
    if (leftPanelMenuItems.length) {
        leftPanelMenuItems.forEach(function(item) {
            item.addEventListener('click', function(e) {
                const parentLi = item.closest('.menu-item');

                // Check if clicked menu item contains submenu
                if (parentLi && parentLi.classList.contains('menu-item-has-children')) {
                    // If it's a parent menu item, execute expand/collapse logic
                    e.preventDefault(); // Prevent link navigation

                    parentLi.classList.toggle('open');
                    const subMenu = parentLi.querySelector('.sub-menu');

                    if (subMenu) {
                        if (parentLi.classList.contains('open')) {
                            subMenu.style.maxHeight = subMenu.scrollHeight + 'px';
                        } else {
                            subMenu.style.maxHeight = '0';
                        }
                    }
                } else {
                    // 如果是普通菜单项，则执行关闭面板的默认行为
                    console.log('Regular menu item clicked, closing panel.');
                    deactivateLeftPanel();
                }
            });
        });
    }

    // Find links pointing to specific product pages (assuming they're in the navigation menu)
    // Note: This uses href attribute matching. If your URLs contain Chinese characters, ensure they are URL encoded/decoded properly in the DOM.
    // For better compatibility, we use attribute contains selector (*=)
    const productPageLinkToOpenPanel = document.querySelector('a[href*="/product/"]'); 
    // Please choose the most accurate matching method based on actual href values in HTML. Here we use encoded form for better compatibility.

    if (productPageLinkToOpenPanel) {
        productPageLinkToOpenPanel.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default page navigation behavior
            console.log('Product page link clicked, opening left panel.');
            activateLeftPanel(); // Call existing function to activate left panel
            // Ensure left panel toggle button state is synchronized if it exists (optional but recommended)
            if (leftPanelToggle && !leftPanelToggle.classList.contains('active')) {
                leftPanelToggle.classList.add('active');
            }
        });
    }
}); 