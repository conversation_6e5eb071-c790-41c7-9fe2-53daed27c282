/**
 * 关于英雄区域区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/about-hero-block', {
        title: '关于英雄区域',
        icon: 'cover-image',
        category: 'design',
        description: '添加一个关于页面的英雄区域，包含背景图片和内容层。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            backgroundImage: {
                type: 'string',
                default: 'https://images.unsplash.com/photo-1600508774634-4e11d34730e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
            },
            subtitle: {
                type: 'string',
                default: 'About Us',
            },
            title: {
                type: 'string',
                default: 'Crafted with Ingenuity<br>The Art of Light & Shadow',
            },
            description: {
                type: 'string',
                default: 'We are dedicated to providing high-quality, artistic lighting solutions, reshaping spatial experiences with the art of light and shadow.',
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var blockProps = useBlockProps({
                className: 'about-hero-block-editor',
            });

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '背景设置', initialOpen: true }, [
                        el(MediaUploadCheck, {}, [
                            el(MediaUpload, {
                                onSelect: function(media) {
                                    setAttributes({ backgroundImage: media.url });
                                },
                                allowedTypes: ['image'],
                                value: attributes.backgroundImage,
                                render: function(obj) {
                                    return el(Button, {
                                        className: attributes.backgroundImage ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open,
                                    }, attributes.backgroundImage ? '更换背景图片' : '选择背景图片');
                                },
                            }),
                        ]),
                        attributes.backgroundImage && el(Button, {
                            onClick: function() {
                                setAttributes({ backgroundImage: '' });
                            },
                            isDestructive: true,
                        }, '移除背景图片'),
                    ]),
                    el(PanelBody, { title: '内容设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                setAttributes({ subtitle: value });
                            },
                        }),
                        el(TextareaControl, {
                            label: '主标题（支持HTML）',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                            help: '可以使用 <br> 标签换行',
                        }),
                        el(TextareaControl, {
                            label: '描述',
                            value: attributes.description,
                            onChange: function(value) {
                                setAttributes({ description: value });
                            },
                        }),
                    ]),
                ]),

                // 预览区域
                el('div', { className: 'about-hero-block-preview' }, [
                    el('section', { className: 'hero-section about-hero' }, [
                        el('div', { 
                            className: 'hero-background',
                            style: {
                                position: 'relative',
                                height: '400px',
                                overflow: 'hidden',
                                borderRadius: '4px',
                            }
                        }, [
                            attributes.backgroundImage && el('img', {
                                src: attributes.backgroundImage,
                                alt: 'Background',
                                style: {
                                    width: '100%',
                                    height: '100%',
                                    objectFit: 'cover',
                                },
                            }),
                        ]),
                        el('div', { 
                            className: 'container',
                            style: {
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                width: '100%',
                                textAlign: 'center',
                                color: '#fff',
                                zIndex: 2,
                            }
                        }, [
                            el('div', { className: 'hero-content' }, [
                                attributes.subtitle && el('span', { 
                                    className: 'hero-subtitle',
                                    style: { color: '#d6ad60' }
                                }, attributes.subtitle),
                                attributes.title && el('h1', { 
                                    className: 'hero-title',
                                    dangerouslySetInnerHTML: { __html: attributes.title }
                                }),
                                attributes.description && el('p', { 
                                    className: 'hero-description' 
                                }, attributes.description),
                            ]),
                        ]),
                    ]),
                ]),
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
