/**
 * 图片画廊墙区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var RangeControl = components.RangeControl;
    var SelectControl = components.SelectControl;
    var ToggleControl = components.ToggleControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/gallery-wall-block', {
        title: '图片画廊墙',
        icon: 'images-alt2',
        category: 'design',
        description: '创建一个可自定义的图片画廊墙，支持多种布局和悬停效果。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            title: {
                type: 'string',
                default: 'Factory Display',
            },
            description: {
                type: 'string',
                default: '',
            },
            images: {
                type: 'array',
                default: [],
                items: {
                    type: 'object',
                    properties: {
                        id: {
                            type: 'number',
                        },
                        url: {
                            type: 'string',
                        },
                        alt: {
                            type: 'string',
                        },
                        caption: {
                            type: 'string',
                        },
                    },
                },
            },
            columns: {
                type: 'number',
                default: 3,
            },
            imageRatio: {
                type: 'string',
                default: 'square',
            },
            hoverEffect: {
                type: 'string',
                default: 'scale',
            },
            spacing: {
                type: 'number',
                default: 20,
            },
            marginLeft: {
                type: 'number',
                default: 40,
            },
            marginRight: {
                type: 'number',
                default: 40,
            },
            frontendMarginLeft: {
                type: 'number',
                default: 300,
            },
            frontendMarginRight: {
                type: 'number',
                default: 300,
            },
            showButton: {
                type: 'boolean',
                default: false,
            },
            buttonText: {
                type: 'string',
                default: '查看更多',
            },
            buttonUrl: {
                type: 'string',
                default: '',
            },
            backgroundColor: {
                type: 'string',
                default: 'light',
            },
            titleSize: {
                type: 'number',
                default: 40,
            },
            titleSpacing: {
                type: 'number',
                default: 30,
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var blockProps = useBlockProps({
                className: 'gallery-wall-block-editor',
            });

            // 添加图片
            function onSelectImages(newImages) {
                // 确保newImages是数组
                if (!Array.isArray(newImages)) {
                    newImages = [newImages];
                }

                var processedImages = newImages.map(function(image) {
                    return {
                        id: image.id,
                        url: image.sizes && image.sizes.medium ? image.sizes.medium.url : image.url,
                        alt: image.alt || '',
                        caption: image.caption || '',
                    };
                });

                // 替换所有图片
                setAttributes({ images: processedImages });
            }

            // 添加更多图片
            function onAddMoreImages(newImages) {
                // 确保newImages是数组
                if (!Array.isArray(newImages)) {
                    newImages = [newImages];
                }

                var processedImages = newImages.map(function(image) {
                    return {
                        id: image.id,
                        url: image.sizes && image.sizes.medium ? image.sizes.medium.url : image.url,
                        alt: image.alt || '',
                        caption: image.caption || '',
                    };
                });

                // 追加到现有图片
                var existingImages = attributes.images || [];
                var allImages = existingImages.concat(processedImages);
                setAttributes({ images: allImages });
            }

            // 移除图片
            function removeImage(index) {
                var newImages = [...attributes.images];
                newImages.splice(index, 1);
                setAttributes({ images: newImages });
            }

            // 更新图片Alt文本
            function updateImageAlt(index, alt) {
                var newImages = [...attributes.images];
                newImages[index].alt = alt;
                setAttributes({ images: newImages });
            }

            // 计算网格样式
            function getGridStyle() {
                return {
                    display: 'grid',
                    gridTemplateColumns: 'repeat(' + attributes.columns + ', 1fr)',
                    gap: attributes.spacing + 'px',
                };
            }

            // 获取图片容器样式
            function getImageContainerStyle() {
                var paddingTop = '75%'; // 默认4:3比例
                
                switch (attributes.imageRatio) {
                    case 'square':
                        paddingTop = '100%';
                        break;
                    case 'landscape':
                        paddingTop = '75%';
                        break;
                    case 'portrait':
                        paddingTop = '133.33%';
                        break;
                    case 'wide':
                        paddingTop = '56.25%'; // 16:9
                        break;
                    case 'original':
                        paddingTop = 'auto';
                        break;
                }
                
                return {
                    position: 'relative',
                    paddingTop: paddingTop,
                    overflow: 'hidden',
                    borderRadius: '4px',
                    backgroundColor: '#f5f5f5',
                };
            }

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '标题设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '画廊标题',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                        }),
                        el(RangeControl, {
                            label: '标题字体大小',
                            value: attributes.titleSize,
                            onChange: function(value) {
                                setAttributes({ titleSize: value });
                            },
                            min: 20,
                            max: 60,
                            step: 2,
                        }),
                        el(RangeControl, {
                            label: '标题与图片间距',
                            value: attributes.titleSpacing,
                            onChange: function(value) {
                                setAttributes({ titleSpacing: value });
                            },
                            min: 10,
                            max: 100,
                            step: 5,
                            help: '调整标题下方到图片网格的距离',
                        }),
                        el(TextareaControl, {
                            label: '画廊描述',
                            value: attributes.description,
                            onChange: function(value) {
                                setAttributes({ description: value });
                            },
                        }),
                    ]),
                    el(PanelBody, { title: '图片设置', initialOpen: true }, [
                        el(MediaUploadCheck, {}, [
                            el(MediaUpload, {
                                onSelect: onSelectImages,
                                allowedTypes: ['image'],
                                multiple: true,
                                gallery: true,
                                value: attributes.images.map(function(img) { return img.id; }),
                                render: function(obj) {
                                    return el(Button, {
                                        className: 'editor-post-featured-image__toggle',
                                        onClick: obj.open,
                                        isPrimary: attributes.images.length === 0,
                                        isSecondary: attributes.images.length > 0,
                                    }, attributes.images.length > 0 ? '更换图片（支持多选）' : '选择图片（支持多选）');
                                },
                            }),
                        ]),
                        attributes.images.length > 0 && el('div', { style: { marginTop: '10px' } }, [
                            el(MediaUploadCheck, {}, [
                                el(MediaUpload, {
                                    onSelect: onAddMoreImages,
                                    allowedTypes: ['image'],
                                    multiple: true,
                                    gallery: true,
                                    render: function(obj) {
                                        return el(Button, {
                                            className: 'editor-post-featured-image__toggle',
                                            onClick: obj.open,
                                            isSecondary: true,
                                        }, '添加更多图片');
                                    },
                                }),
                            ]),
                            el(Button, {
                                onClick: function() {
                                    setAttributes({ images: [] });
                                },
                                isDestructive: true,
                                style: { marginTop: '10px' }
                            }, '清空所有图片'),
                        ]),
                    ]),
                    el(PanelBody, { title: '布局设置', initialOpen: true }, [
                        el(RangeControl, {
                            label: '列数',
                            value: attributes.columns,
                            onChange: function(value) {
                                setAttributes({ columns: value });
                            },
                            min: 1,
                            max: 6,
                            step: 1,
                        }),
                        el(SelectControl, {
                            label: '图片比例',
                            value: attributes.imageRatio,
                            options: [
                                { label: '正方形 (1:1)', value: 'square' },
                                { label: '横向矩形 (4:3)', value: 'landscape' },
                                { label: '纵向矩形 (3:4)', value: 'portrait' },
                                { label: '宽屏 (16:9)', value: 'wide' },
                                { label: '原始比例', value: 'original' },
                            ],
                            onChange: function(value) {
                                setAttributes({ imageRatio: value });
                            },
                        }),
                        el(RangeControl, {
                            label: '图片间距',
                            value: attributes.spacing,
                            onChange: function(value) {
                                setAttributes({ spacing: value });
                            },
                            min: 5,
                            max: 50,
                            step: 5,
                        }),
                        el(RangeControl, {
                            label: '编辑器左边距',
                            value: attributes.marginLeft,
                            onChange: function(value) {
                                setAttributes({ marginLeft: value });
                            },
                            min: 0,
                            max: 200,
                            step: 10,
                            help: '仅在编辑器中显示的左边距，用于预览对齐效果',
                        }),
                        el(RangeControl, {
                            label: '编辑器右边距',
                            value: attributes.marginRight,
                            onChange: function(value) {
                                setAttributes({ marginRight: value });
                            },
                            min: 0,
                            max: 200,
                            step: 10,
                            help: '仅在编辑器中显示的右边距，用于预览对齐效果',
                        }),
                        el(RangeControl, {
                            label: '前端左边距',
                            value: attributes.frontendMarginLeft,
                            onChange: function(value) {
                                setAttributes({ frontendMarginLeft: value });
                            },
                            min: 0,
                            max: 500,
                            step: 10,
                            help: '仅在前端页面生效的额外左边距',
                        }),
                        el(RangeControl, {
                            label: '前端右边距',
                            value: attributes.frontendMarginRight,
                            onChange: function(value) {
                                setAttributes({ frontendMarginRight: value });
                            },
                            min: 0,
                            max: 500,
                            step: 10,
                            help: '仅在前端页面生效的额外右边距',
                        }),
                    ]),
                    el(PanelBody, { title: '悬停效果', initialOpen: false }, [
                        el(SelectControl, {
                            label: '悬停动画',
                            value: attributes.hoverEffect,
                            options: [
                                { label: '缩放', value: 'scale' },
                                { label: '淡入淡出', value: 'fade' },
                                { label: '向上移动', value: 'moveUp' },
                                { label: '无效果', value: 'none' },
                            ],
                            onChange: function(value) {
                                setAttributes({ hoverEffect: value });
                            },
                        }),
                    ]),
                    el(PanelBody, { title: '样式设置', initialOpen: false }, [
                        el(SelectControl, {
                            label: '背景样式',
                            value: attributes.backgroundColor,
                            options: [
                                { label: '浅色背景', value: 'light' },
                                { label: '白色背景', value: 'white' },
                                { label: '深色背景', value: 'dark' },
                            ],
                            onChange: function(value) {
                                setAttributes({ backgroundColor: value });
                            },
                        }),
                    ]),
                    el(PanelBody, { title: '按钮设置', initialOpen: false }, [
                        el(ToggleControl, {
                            label: '显示按钮',
                            checked: attributes.showButton,
                            onChange: function(value) {
                                setAttributes({ showButton: value });
                            },
                        }),
                        attributes.showButton && el(TextControl, {
                            label: '按钮文字',
                            value: attributes.buttonText,
                            onChange: function(value) {
                                setAttributes({ buttonText: value });
                            },
                        }),
                        attributes.showButton && el(TextControl, {
                            label: '按钮链接',
                            value: attributes.buttonUrl,
                            onChange: function(value) {
                                setAttributes({ buttonUrl: value });
                            },
                        }),
                    ]),
                ]),

                // 预览区域
                el('div', { className: 'gallery-wall-block-preview' }, [
                    el('section', { 
                        className: 'gallery-wall pb-30 text-center ' + attributes.backgroundColor + ' pt-50',
                        'data-group': 'Gallery'
                    }, [
                        el('div', {
                            className: 'center',
                            style: {
                                paddingLeft: attributes.marginLeft + 'px',
                                paddingRight: attributes.marginRight + 'px'
                            }
                        }, [
                            // 标题
                            attributes.title && el('h1', { 
                                className: 'title',
                                style: { fontSize: attributes.titleSize + 'px' }
                            }, attributes.title),
                            
                            // 描述
                            attributes.description && el('p', { 
                                className: 'gallery-description',
                                style: { marginTop: '1rem', color: '#666' }
                            }, attributes.description),
                            
                            // 图片网格
                            attributes.images.length > 0 ? el('div', {
                                className: 'flexW',
                                style: Object.assign(getGridStyle(), {
                                    marginTop: attributes.titleSpacing + 'px'
                                })
                            },
                                attributes.images.map(function(image, index) {
                                    return el('div', {
                                        key: index,
                                        className: 'item content-box mb-20 hover-' + attributes.hoverEffect,
                                    }, [
                                        el('div', {
                                            className: 'imgW',
                                            style: getImageContainerStyle()
                                        }, [
                                            el('img', {
                                                src: image.url,
                                                alt: image.alt,
                                                style: attributes.imageRatio === 'original' ? {
                                                    width: '100%',
                                                    height: 'auto',
                                                    display: 'block'
                                                } : {
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    width: '100%',
                                                    height: '100%',
                                                    objectFit: 'cover'
                                                }
                                            }),
                                            // 删除按钮
                                            el('button', {
                                                className: 'gallery-remove-image',
                                                onClick: function() {
                                                    removeImage(index);
                                                },
                                                style: {
                                                    position: 'absolute',
                                                    top: '5px',
                                                    right: '5px',
                                                    background: 'rgba(255,255,255,0.8)',
                                                    border: 'none',
                                                    borderRadius: '50%',
                                                    width: '24px',
                                                    height: '24px',
                                                    cursor: 'pointer',
                                                    fontSize: '12px',
                                                    color: '#d63638'
                                                }
                                            }, '×'),
                                        ]),
                                        // Alt文本编辑
                                        el('div', {
                                            style: {
                                                marginTop: '8px',
                                                fontSize: '12px'
                                            }
                                        }, [
                                            el('input', {
                                                type: 'text',
                                                placeholder: 'Alt文本',
                                                value: image.alt,
                                                onChange: function(e) {
                                                    updateImageAlt(index, e.target.value);
                                                },
                                                style: {
                                                    width: '100%',
                                                    padding: '4px',
                                                    border: '1px solid #ddd',
                                                    borderRadius: '2px',
                                                    fontSize: '11px'
                                                }
                                            })
                                        ])
                                    ]);
                                })
                            ) : el('div', {
                                className: 'gallery-placeholder',
                                style: {
                                    padding: '60px 20px',
                                    border: '2px dashed #ddd',
                                    borderRadius: '4px',
                                    textAlign: 'center',
                                    color: '#999',
                                    marginTop: attributes.titleSpacing + 'px'
                                }
                            }, '点击右侧设置面板中的"选择图片"按钮添加图片到画廊'),
                            
                            // 按钮
                            attributes.showButton && attributes.buttonText && el('div', { 
                                className: 'btnBox',
                                style: { zIndex: 10, marginTop: '30px' }
                            }, [
                                el('a', {
                                    href: attributes.buttonUrl || '#',
                                    className: 'btn btn-primary',
                                    style: {
                                        display: 'inline-block',
                                        padding: '12px 24px',
                                        backgroundColor: '#d6ad60',
                                        color: '#fff',
                                        textDecoration: 'none',
                                        borderRadius: '4px',
                                        transition: 'background-color 0.3s ease'
                                    }
                                }, attributes.buttonText)
                            ])
                        ])
                    ])
                ])
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
