/**
 * 轮播图区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var ToggleControl = components.ToggleControl;
    var RangeControl = components.RangeControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/slider-block', {
        title: '轮播图区块',
        icon: 'slides',
        category: 'design',
        description: '添加一个轮播图区块，可以显示多张幻灯片。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            sliderCount: {
                type: 'number',
                default: 3,
            },
            slides: {
                type: 'array',
                default: [
                    {
                        image: '',
                        title: '轮播图标题',
                        subtitle: '轮播图副标题',
                        description: '轮播图描述文本',
                        buttonText: '按钮文字',
                        buttonUrl: '',
                        button2Text: '第二按钮',
                        button2Url: '',
                    },
                    {
                        image: '',
                        title: '第二张轮播图',
                        subtitle: '副标题示例',
                        description: '这是第二张轮播图的描述文本',
                        buttonText: '了解更多',
                        buttonUrl: '',
                        button2Text: '联系我们',
                        button2Url: '',
                    },
                    {
                        image: '',
                        title: '第三张轮播图',
                        subtitle: '副标题示例',
                        description: '这是第三张轮播图的描述文本',
                        buttonText: '浏览产品',
                        buttonUrl: '',
                        button2Text: '',
                        button2Url: '',
                    }
                ],
            },
            autoplay: {
                type: 'boolean',
                default: true,
            },
            autoplaySpeed: {
                type: 'number',
                default: 5000,
            },
            showDots: {
                type: 'boolean',
                default: true,
            },
            showArrows: {
                type: 'boolean',
                default: true,
            },
            fade: {
                type: 'boolean',
                default: true,
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var slides = attributes.slides;
            var blockProps = useBlockProps({
                className: 'light-fixture-slider-block-editor',
            });

            // 更新幻灯片数量
            function updateSliderCount(count) {
                var newCount = parseInt(count);
                var currentSlides = [...slides];
                
                // 如果新数量大于当前幻灯片数量，添加新幻灯片
                if (newCount > currentSlides.length) {
                    for (var i = currentSlides.length; i < newCount; i++) {
                        currentSlides.push({
                            image: '',
                            title: '新轮播图 #' + (i + 1),
                            subtitle: '副标题',
                            description: '描述文本',
                            buttonText: '按钮文字',
                            buttonUrl: '',
                            button2Text: '',
                            button2Url: '',
                        });
                    }
                } 
                // 如果新数量小于当前幻灯片数量，移除多余幻灯片
                else if (newCount < currentSlides.length) {
                    currentSlides = currentSlides.slice(0, newCount);
                }
                
                setAttributes({ 
                    sliderCount: newCount,
                    slides: currentSlides
                });
            }

            // 更新单个幻灯片属性
            function updateSlideAttribute(index, attribute, value) {
                var newSlides = [...slides];
                if (!newSlides[index]) {
                    return;
                }
                
                newSlides[index] = {
                    ...newSlides[index],
                    [attribute]: value
                };
                
                setAttributes({ slides: newSlides });
            }

            // 渲染幻灯片编辑器
            function renderSlideEditor(slide, index) {
                return el('div', { className: 'slide-editor', key: 'slide-' + index },
                    el('h3', {}, '幻灯片 #' + (index + 1)),
                    
                    // 图片上传
                    el('div', { className: 'slide-image-upload' },
                        el('label', {}, '幻灯片图片'),
                        el(MediaUploadCheck, {},
                            el(MediaUpload, {
                                onSelect: function(media) {
                                    updateSlideAttribute(index, 'image', media.url);
                                },
                                allowedTypes: ['image'],
                                value: slide.image,
                                render: function(obj) {
                                    return el('div', {},
                                        slide.image && el('div', { className: 'image-preview' },
                                            el('img', { src: slide.image, alt: 'Preview' })
                                        ),
                                        el(Button, {
                                            isPrimary: true,
                                            onClick: obj.open
                                        }, slide.image ? '更换图片' : '选择图片'),
                                        slide.image && el(Button, {
                                            isSecondary: true,
                                            onClick: function() {
                                                updateSlideAttribute(index, 'image', '');
                                            }
                                        }, '移除图片')
                                    );
                                }
                            })
                        )
                    ),
                    
                    // 标题
                    el(TextControl, {
                        label: '标题',
                        value: slide.title,
                        onChange: function(value) {
                            updateSlideAttribute(index, 'title', value);
                        }
                    }),
                    
                    // 副标题
                    el(TextControl, {
                        label: '副标题',
                        value: slide.subtitle,
                        onChange: function(value) {
                            updateSlideAttribute(index, 'subtitle', value);
                        }
                    }),
                    
                    // 描述
                    el(TextControl, {
                        label: '描述',
                        value: slide.description,
                        onChange: function(value) {
                            updateSlideAttribute(index, 'description', value);
                        }
                    }),
                    
                    // 第一个按钮
                    el('div', { className: 'button-controls' },
                        el('h4', {}, '第一个按钮'),
                        el(TextControl, {
                            label: '按钮文字',
                            value: slide.buttonText,
                            onChange: function(value) {
                                updateSlideAttribute(index, 'buttonText', value);
                            }
                        }),
                        el(TextControl, {
                            label: '按钮链接',
                            value: slide.buttonUrl,
                            onChange: function(value) {
                                updateSlideAttribute(index, 'buttonUrl', value);
                            }
                        })
                    ),
                    
                    // 第二个按钮
                    el('div', { className: 'button-controls' },
                        el('h4', {}, '第二个按钮'),
                        el(TextControl, {
                            label: '按钮文字',
                            value: slide.button2Text,
                            onChange: function(value) {
                                updateSlideAttribute(index, 'button2Text', value);
                            }
                        }),
                        el(TextControl, {
                            label: '按钮链接',
                            value: slide.button2Url,
                            onChange: function(value) {
                                updateSlideAttribute(index, 'button2Url', value);
                            }
                        })
                    ),
                    
                    // 分隔线
                    el('hr', {})
                );
            }

            // 渲染侧边栏控制面板
            var inspectorControls = el(InspectorControls, {},
                el(PanelBody, { title: '轮播图设置', initialOpen: true },
                    el(RangeControl, {
                        label: '幻灯片数量',
                        value: attributes.sliderCount,
                        onChange: updateSliderCount,
                        min: 1,
                        max: 10
                    }),
                    el(ToggleControl, {
                        label: '自动播放',
                        checked: attributes.autoplay,
                        onChange: function(value) {
                            setAttributes({ autoplay: value });
                        }
                    }),
                    attributes.autoplay && el(RangeControl, {
                        label: '自动播放速度 (毫秒)',
                        value: attributes.autoplaySpeed,
                        onChange: function(value) {
                            setAttributes({ autoplaySpeed: value });
                        },
                        min: 1000,
                        max: 10000,
                        step: 500
                    }),
                    el(ToggleControl, {
                        label: '显示导航点',
                        checked: attributes.showDots,
                        onChange: function(value) {
                            setAttributes({ showDots: value });
                        }
                    }),
                    el(ToggleControl, {
                        label: '显示箭头',
                        checked: attributes.showArrows,
                        onChange: function(value) {
                            setAttributes({ showArrows: value });
                        }
                    }),
                    el(ToggleControl, {
                        label: '淡入淡出效果',
                        checked: attributes.fade,
                        onChange: function(value) {
                            setAttributes({ fade: value });
                        }
                    })
                )
            );

            // 渲染幻灯片预览
            function renderSlidePreview(slide, index) {
                return el('div', { className: 'slide-preview', key: 'preview-' + index },
                    el('div', { className: 'slide-preview-header' },
                        el('h3', {}, '幻灯片 #' + (index + 1) + ': ' + slide.title)
                    ),
                    el('div', { className: 'slide-preview-content' },
                        slide.image && el('img', { 
                            src: slide.image, 
                            alt: slide.title,
                            className: 'slide-preview-image'
                        }),
                        !slide.image && el('div', { className: 'slide-preview-placeholder' }, '无图片')
                    )
                );
            }

            // 主编辑界面
            return el('div', blockProps,
                inspectorControls,
                el('div', { className: 'slider-block-editor' },
                    el('h2', {}, '轮播图区块'),
                    el('div', { className: 'slider-previews' },
                        slides.map(function(slide, index) {
                            return renderSlidePreview(slide, index);
                        })
                    ),
                    el('div', { className: 'slider-editors' },
                        slides.map(function(slide, index) {
                            return renderSlideEditor(slide, index);
                        })
                    )
                )
            );
        },

        // 保存函数 - 由于使用了render_callback，这里返回null
        save: function() {
            return null;
        }
    });
})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
); 