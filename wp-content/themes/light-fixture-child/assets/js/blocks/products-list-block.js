/**
 * 产品列表区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n, apiFetch) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var ToggleControl = components.ToggleControl;
    var RangeControl = components.RangeControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;
    var SelectControl = components.SelectControl;
    var Placeholder = components.Placeholder;
    var Spinner = components.Spinner;
    var RadioControl = components.RadioControl;
    var CheckboxControl = components.CheckboxControl;
    var useState = element.useState;
    var useEffect = element.useEffect;

    // 注册区块
    blocks.registerBlockType('light-fixture/products-list-block', {
        title: '产品列表区块',
        icon: 'grid-view',
        category: 'design',
        description: '添加一个产品列表区块，可以显示产品列表。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            title: {
                type: 'string',
                default: 'Featured Products',
            },
            subtitle: {
                type: 'string',
                default: 'Our Products',
            },
            description: {
                type: 'string',
                default: 'Carefully selected lighting products that bring unique lighting experiences to your space.',
            },
            columns: {
                type: 'number',
                default: 5,
            },
            rows: {
                type: 'number',
                default: 2,
            },
            dataSource: {
                type: 'string',
                default: 'category', // 'category' or 'custom'
            },
            selectedCategory: {
                type: 'number',
                default: 0,
            },
            customProducts: {
                type: 'array',
                default: [],
            },
            showViewAllButton: {
                type: 'boolean',
                default: true,
            },
            viewAllButtonText: {
                type: 'string',
                default: 'View More Products',
            },
            viewAllButtonLink: {
                type: 'string',
                default: '/index.php/productslist/',
            },
            showProductTitle: {
                type: 'boolean',
                default: true,
            },
            showProductDescription: {
                type: 'boolean',
                default: true,
            },
            displayMode: {
                type: 'string',
                default: 'hover', // 'hover' 或 'below'
            },
        },

        // 编辑器界面
        edit: function(props) {
            var attributes = props.attributes;
            var blockProps = useBlockProps({
                className: 'products-list-block-editor',
            });

            // 使用 React Hooks 管理状态
            var [productCategories, setProductCategories] = useState([]);
            var [isLoadingCategories, setIsLoadingCategories] = useState(false);
            var [error, setError] = useState(null);
            var [previewProducts, setPreviewProducts] = useState([]);
            var [isLoadingProducts, setIsLoadingProducts] = useState(false);

            // 使用 useEffect 加载产品分类数据
            useEffect(function() {
                setIsLoadingCategories(true);
                setError(null);
                
                // 使用 wp.apiFetch 从 REST API 获取产品分类数据
                apiFetch({ 
                    path: '/wp/v2/product-category?per_page=100'
                }).then(function(categories) {
                    setProductCategories(categories);
                    setIsLoadingCategories(false);
                }).catch(function(err) {
                    console.error('获取产品分类失败:', err);
                    setError('获取产品分类失败: ' + (err.message || '未知错误'));
                    setIsLoadingCategories(false);
                });
            }, []); // 只在组件挂载时获取一次数据

            // 使用 useEffect 加载产品预览数据
            useEffect(function() {
                if (attributes.dataSource === 'category') {
                    setIsLoadingProducts(true);
                    
                    var path = '/wp/v2/product?per_page=' + (attributes.rows * attributes.columns);
                    
                    // 如果选择了特定分类
                    if (attributes.selectedCategory > 0) {
                        path += '&product_category=' + attributes.selectedCategory;
                    }
                    
                    apiFetch({ path: path })
                        .then(function(products) {
                            setPreviewProducts(products);
                            setIsLoadingProducts(false);
                        })
                        .catch(function(err) {
                            console.error('获取产品失败:', err);
                            setPreviewProducts([]);
                            setIsLoadingProducts(false);
                        });
                }
            }, [attributes.dataSource, attributes.selectedCategory, attributes.rows, attributes.columns]);

            // 处理添加自定义产品
            function addCustomProduct() {
                var customProducts = attributes.customProducts.slice();
                customProducts.push({
                    id: Date.now(),
                    name: '',
                    title: '',
                    description: '',
                    image: '',
                    link: '',
                    buttonText: '查看详情',
                });
                props.setAttributes({ customProducts: customProducts });
            }

            // 处理移除自定义产品
            function removeCustomProduct(index) {
                var customProducts = attributes.customProducts.slice();
                customProducts.splice(index, 1);
                props.setAttributes({ customProducts: customProducts });
            }

            // 处理更新自定义产品
            function updateCustomProduct(index, key, value) {
                var customProducts = attributes.customProducts.slice();
                customProducts[index][key] = value;
                props.setAttributes({ customProducts: customProducts });
            }

            // 处理选择图片
            function onSelectImage(index, media) {
                var customProducts = attributes.customProducts.slice();
                customProducts[index].image = media.url;
                props.setAttributes({ customProducts: customProducts });
            }

            // 渲染自定义产品编辑器
            function renderCustomProductEditor(product, index) {
                return el('div', { className: 'product-editor', key: product.id },
                    el('h3', {}, '产品 #' + (index + 1)),
                    el(TextControl, {
                        label: '产品名称',
                        value: product.name,
                        onChange: function(value) {
                            updateCustomProduct(index, 'name', value);
                        }
                    }),
                    el(TextControl, {
                        label: '产品标题',
                        value: product.title,
                        onChange: function(value) {
                            updateCustomProduct(index, 'title', value);
                        }
                    }),
                    el(TextControl, {
                        label: '产品描述',
                        value: product.description,
                        onChange: function(value) {
                            updateCustomProduct(index, 'description', value);
                        }
                    }),
                    el('div', { className: 'product-image-control' },
                        el('label', {}, '产品图片'),
                        el(MediaUploadCheck, {},
                            el(MediaUpload, {
                                onSelect: function(media) {
                                    onSelectImage(index, media);
                                },
                                allowedTypes: ['image'],
                                value: product.image,
                                render: function(obj) {
                                    return el('div', {},
                                        product.image ?
                                            el('div', { className: 'image-preview' },
                                                el('img', { src: product.image }),
                                                el(Button, {
                                                    isSecondary: true,
                                                    onClick: function() {
                                                        updateCustomProduct(index, 'image', '');
                                                    }
                                                }, '移除图片'),
                                                el(Button, {
                                                    isPrimary: true,
                                                    onClick: obj.open
                                                }, '更换图片')
                                            ) :
                                            el(Button, {
                                                isPrimary: true,
                                                onClick: obj.open
                                            }, '选择图片')
                                    );
                                }
                            })
                        )
                    ),
                    el(TextControl, {
                        label: '链接地址',
                        value: product.link,
                        onChange: function(value) {
                            updateCustomProduct(index, 'link', value);
                        }
                    }),
                    el(TextControl, {
                        label: 'Button Text',
                        value: product.buttonText,
                        onChange: function(value) {
                            updateCustomProduct(index, 'buttonText', value);
                        }
                    }),
                    el(Button, {
                        isDestructive: true,
                        onClick: function() {
                            removeCustomProduct(index);
                        }
                    }, 'Remove Product')
                );
            }

            // 渲染产品预览
            function renderProductPreview() {
                var gridClass = 'products-grid';
                if (attributes.columns !== 5) {
                    gridClass += ' products-grid--' + attributes.columns;
                }

                if (attributes.dataSource === 'category' && isLoadingProducts) {
                    return el('div', { className: 'loading-placeholder' },
                        el(Spinner),
                        el('p', {}, 'Loading products...')
                    );
                }

                if (attributes.dataSource === 'category' && previewProducts.length === 0) {
                    return el('div', { className: 'no-products-message' },
                        el('p', {}, 'No products found. Please select a category that contains products, or switch to custom product mode.')
                    );
                }

                var productsToShow = [];
                var maxItems = attributes.rows * attributes.columns;

                if (attributes.dataSource === 'category') {
                    productsToShow = previewProducts.slice(0, maxItems).map(function(product) {
                        return {
                            title: product.title.rendered,
                            image: product.featured_media_url || '',
                            description: product.excerpt.rendered,
                        };
                    });
                } else {
                    productsToShow = attributes.customProducts.slice(0, maxItems);
                }

                // 如果没有产品，显示占位符
                if (productsToShow.length === 0) {
                    var placeholders = [];
                    for (var i = 0; i < maxItems; i++) {
                        placeholders.push({
                            title: '产品 ' + (i + 1),
                            description: '产品描述示例',
                            image: '',
                        });
                    }
                    productsToShow = placeholders;
                }

                return el('div', { className: gridClass },
                    productsToShow.map(function(product, index) {
                        return el('div', { className: 'product-item display-mode-' + attributes.displayMode, key: index },
                            el('div', { className: 'product-item__image-container' },
                                product.image ?
                                    el('img', { 
                                        src: product.image,
                                        className: 'product-item__image',
                                        alt: product.title || product.name || '产品图片'
                                    }) :
                                    el('div', { className: 'product-item__placeholder' }, '产品图片'),
                                el('div', { className: 'product-item__overlay' },
                                    (attributes.displayMode === 'hover' && (attributes.showProductTitle || (attributes.showProductDescription && product.description))) ?
                                    el('div', { className: 'product-item__hover-content' },
                                        attributes.showProductTitle ?
                                            el('h3', { className: 'product-item__title' }, product.title || product.name || '产品标题') :
                                            null,
                                        attributes.showProductDescription && product.description ?
                                            el('p', { className: 'product-item__description' }, product.description) :
                                            null
                                    ) : null,
                                    el('a', { 
                                        href: product.link || '#',
                                        className: 'product-item__view-button'
                                    }, product.buttonText || '查看详情')
                                )
                            ),
                            (attributes.displayMode === 'below' && (attributes.showProductTitle || (attributes.showProductDescription && product.description))) ?
                            el('div', { className: 'product-item__content' },
                                attributes.showProductTitle ?
                                    el('h3', { className: 'product-item__title' },
                                        el('a', { href: product.link || '#' }, product.title || product.name || '产品标题')
                                    ) :
                                    null,
                                attributes.showProductDescription && product.description ?
                                    el('p', { className: 'product-item__description' }, product.description) :
                                    null
                            ) : null
                        );
                    })
                );
            }

            // 返回区块编辑器界面
            return el('div', blockProps,
                // 侧边栏控制面板
                el(InspectorControls, {},
                    el(PanelBody, { title: '基本设置', initialOpen: true },
                        el(TextControl, {
                            label: '标题',
                            value: attributes.title,
                            onChange: function(value) {
                                props.setAttributes({ title: value });
                            }
                        }),
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                props.setAttributes({ subtitle: value });
                            }
                        }),
                        el(TextControl, {
                            label: '描述',
                            value: attributes.description,
                            onChange: function(value) {
                                props.setAttributes({ description: value });
                            }
                        })
                    ),
                    el(PanelBody, { title: '布局设置', initialOpen: true },
                        el(RangeControl, {
                            label: '列数',
                            value: attributes.columns,
                            min: 1,
                            max: 6,
                            onChange: function(value) {
                                props.setAttributes({ columns: value });
                            }
                        }),
                        el(RangeControl, {
                            label: '行数',
                            value: attributes.rows,
                            min: 1,
                            max: 10,
                            onChange: function(value) {
                                props.setAttributes({ rows: value });
                            }
                        }),
                        el(ToggleControl, {
                            label: '显示产品标题',
                            checked: attributes.showProductTitle,
                            onChange: function(value) {
                                props.setAttributes({ showProductTitle: value });
                            }
                        }),
                        el(ToggleControl, {
                            label: '显示产品描述',
                            checked: attributes.showProductDescription,
                            onChange: function(value) {
                                props.setAttributes({ showProductDescription: value });
                            }
                        }),
                        el(SelectControl, {
                            label: '标题和描述显示位置',
                            value: attributes.displayMode,
                            options: [
                                { label: '悬停在图片上显示', value: 'hover' },
                                { label: '显示在图片下方', value: 'below' },
                            ],
                            onChange: function(value) {
                                props.setAttributes({ displayMode: value });
                            }
                        })
                    ),
                    el(PanelBody, { title: '数据源设置', initialOpen: true },
                        el(RadioControl, {
                            label: '数据来源',
                            selected: attributes.dataSource,
                            options: [
                                { label: '从产品分类获取', value: 'category' },
                                { label: '自定义产品', value: 'custom' }
                            ],
                            onChange: function(value) {
                                props.setAttributes({ dataSource: value });
                            }
                        }),
                        attributes.dataSource === 'category' ?
                            el('div', { className: 'category-selector' },
                                isLoadingCategories ?
                                    el(Spinner) :
                                    el(SelectControl, {
                                        label: '选择产品分类',
                                        value: attributes.selectedCategory,
                                        options: [
                                            { label: '所有分类', value: 0 }
                                        ].concat(productCategories.map(function(category) {
                                            return {
                                                label: category.name,
                                                value: category.id
                                            };
                                        })),
                                        onChange: function(value) {
                                            props.setAttributes({ selectedCategory: parseInt(value) });
                                        }
                                    })
                            ) :
                            el('div', { className: 'custom-products-editor' },
                                attributes.customProducts.map(function(product, index) {
                                    return renderCustomProductEditor(product, index);
                                }),
                                el(Button, {
                                    isPrimary: true,
                                    onClick: addCustomProduct
                                }, '添加产品')
                            )
                    ),
                    el(PanelBody, { title: '按钮设置', initialOpen: true },
                        el(ToggleControl, {
                            label: '显示"查看更多"按钮',
                            checked: attributes.showViewAllButton,
                            onChange: function(value) {
                                props.setAttributes({ showViewAllButton: value });
                            }
                        }),
                        attributes.showViewAllButton ?
                            el('div', {},
                                el(TextControl, {
                                    label: '按钮文字',
                                    value: attributes.viewAllButtonText,
                                    onChange: function(value) {
                                        props.setAttributes({ viewAllButtonText: value });
                                    }
                                }),
                                el(TextControl, {
                                    label: '按钮链接',
                                    value: attributes.viewAllButtonLink,
                                    onChange: function(value) {
                                        props.setAttributes({ viewAllButtonLink: value });
                                    }
                                })
                            ) :
                            null
                    )
                ),
                // 区块内容预览
                el('div', { className: 'products-list-block-preview' },
                    el('div', { className: 'section-header text-center' },
                        attributes.subtitle ?
                            el('span', { className: 'section-subtitle' }, attributes.subtitle) :
                            null,
                        attributes.title ?
                            el('h2', { className: 'section-title' }, attributes.title) :
                            null,
                        attributes.description ?
                            el('p', { className: 'section-description' }, attributes.description) :
                            null
                    ),
                    renderProductPreview(),
                    attributes.showViewAllButton ?
                        el('div', { className: 'text-center mt-5' },
                            el('a', {
                                className: 'btn btn-secondary',
                                href: '#'
                            }, attributes.viewAllButtonText)
                        ) :
                        null
                )
            );
        },

        // 保存函数 - 因为我们使用服务器端渲染，所以这里返回 null
        save: function() {
            return null;
        }
    });
})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n,
    window.wp.apiFetch
); 