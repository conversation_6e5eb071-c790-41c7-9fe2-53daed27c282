/**
 * 关于品牌故事区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/about-brand-story-block', {
        title: '关于品牌故事',
        icon: 'format-image',
        category: 'design',
        description: '添加一个品牌故事区块，包含图片和文本内容的左右布局。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            subtitle: {
                type: 'string',
                default: 'Our Story',
            },
            title: {
                type: 'string',
                default: 'Born from Craftsmanship, Pursuing Excellence',
            },
            image: {
                type: 'string',
                default: 'https://images.unsplash.com/photo-1544457070-4cd773b4d71e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
            },
            imageAlt: {
                type: 'string',
                default: 'Founder at Work',
            },
            paragraphs: {
                type: 'array',
                default: [
                    'Our story began in 2008, founded by Li Ming, a designer passionate about the art of lighting. Having worked for years at internationally renowned lighting brands, Li Ming deeply understood the significant impact of light on spatial ambiance.',
                    'With a profound understanding of light and shadow artistry and an unyielding pursuit of quality, we have grown from a small design studio into a comprehensive lighting enterprise with our own R&D, production, and sales teams.',
                    'For fifteen years, we have consistently adhered to the design philosophy of "people-oriented, light as a medium," perfectly integrating artistic design with practical functionality to provide each client with a unique lighting experience.',
                ],
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var blockProps = useBlockProps({
                className: 'about-brand-story-block-editor',
            });

            // 更新段落
            function updateParagraph(index, value) {
                var newParagraphs = [...attributes.paragraphs];
                newParagraphs[index] = value;
                setAttributes({ paragraphs: newParagraphs });
            }

            // 添加段落
            function addParagraph() {
                var newParagraphs = [...attributes.paragraphs, '新段落内容'];
                setAttributes({ paragraphs: newParagraphs });
            }

            // 删除段落
            function removeParagraph(index) {
                var newParagraphs = attributes.paragraphs.filter((_, i) => i !== index);
                setAttributes({ paragraphs: newParagraphs });
            }

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '标题设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                setAttributes({ subtitle: value });
                            },
                        }),
                        el(TextControl, {
                            label: '主标题',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                        }),
                    ]),
                    el(PanelBody, { title: '图片设置', initialOpen: true }, [
                        el(MediaUploadCheck, {}, [
                            el(MediaUpload, {
                                onSelect: function(media) {
                                    setAttributes({ 
                                        image: media.url,
                                        imageAlt: media.alt || attributes.imageAlt
                                    });
                                },
                                allowedTypes: ['image'],
                                value: attributes.image,
                                render: function(obj) {
                                    return el(Button, {
                                        className: attributes.image ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open,
                                    }, attributes.image ? '更换图片' : '选择图片');
                                },
                            }),
                        ]),
                        el(TextControl, {
                            label: '图片Alt文本',
                            value: attributes.imageAlt,
                            onChange: function(value) {
                                setAttributes({ imageAlt: value });
                            },
                        }),
                        attributes.image && el(Button, {
                            onClick: function() {
                                setAttributes({ image: '', imageAlt: '' });
                            },
                            isDestructive: true,
                        }, '移除图片'),
                    ]),
                    el(PanelBody, { title: '内容段落', initialOpen: true }, [
                        attributes.paragraphs.map(function(paragraph, index) {
                            return el('div', { key: index, style: { marginBottom: '15px' } }, [
                                el(TextareaControl, {
                                    label: '段落 ' + (index + 1),
                                    value: paragraph,
                                    onChange: function(value) {
                                        updateParagraph(index, value);
                                    },
                                }),
                                attributes.paragraphs.length > 1 && el(Button, {
                                    onClick: function() {
                                        removeParagraph(index);
                                    },
                                    isDestructive: true,
                                    isSmall: true,
                                }, '删除段落'),
                            ]);
                        }),
                        el(Button, {
                            onClick: addParagraph,
                            isPrimary: true,
                            isSmall: true,
                        }, '添加段落'),
                    ]),
                ]),

                // 预览区域
                el('div', { className: 'about-brand-story-block-preview' }, [
                    el('section', { className: 'section' }, [
                        el('div', { className: 'container' }, [
                            // 区块头部
                            (attributes.subtitle || attributes.title) && el('div', { className: 'section-header' }, [
                                attributes.subtitle && el('span', { className: 'section-subtitle' }, attributes.subtitle),
                                attributes.title && el('h2', { className: 'section-title' }, attributes.title),
                            ]),
                            
                            // 故事内容
                            el('div', { className: 'about-story' }, [
                                // 图片
                                el('div', { className: 'about-story__image' }, [
                                    attributes.image && el('img', {
                                        src: attributes.image,
                                        alt: attributes.imageAlt,
                                    }),
                                ]),
                                
                                // 文本内容
                                el('div', { className: 'about-story__content' }, 
                                    attributes.paragraphs.map(function(paragraph, index) {
                                        return el('p', { key: index }, paragraph);
                                    })
                                ),
                            ]),
                        ]),
                    ]),
                ]),
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
