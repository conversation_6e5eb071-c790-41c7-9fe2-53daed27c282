/**
 * 工艺展示卡片区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var RangeControl = components.RangeControl;
    var SelectControl = components.SelectControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;
    var useState = element.useState;

    // 注册区块
    blocks.registerBlockType('light-fixture/craftsmanship-cards-block', {
        title: '关于工艺展示卡片',
        icon: 'grid-view',
        category: 'design',
        description: '添加一个工艺展示卡片区块，展示工艺流程或特色服务。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            title: {
                type: 'string',
                default: '原创匠心，精工细作',
            },
            subtitle: {
                type: 'string',
                default: 'Our Craftsmanship',
            },
            description: {
                type: 'string',
                default: '每一件灯具，从设计到成品，都经过严格的工艺流程和质量把控，确保卓越品质。',
            },
            columns: {
                type: 'number',
                default: 5,
            },
            rows: {
                type: 'number',
                default: 1,
            },
            cards: {
                type: 'array',
                default: [
                    {
                        number: '01',
                        title: '概念构思',
                        description: '设计师从自然、建筑、艺术中汲取灵感，发展创意想法，绘制概念草图，确定设计方向。',
                        icon: '',
                    },
                    {
                        number: '02',
                        title: '3D建模与原型制作',
                        description: '运用3D建模软件将设计转化为数字模型，并打印原型进行评估和完善，确保设计的可行性和美观性。',
                        icon: '',
                    },
                    {
                        number: '03',
                        title: '材料选择',
                        description: '严格筛选优质金属、进口亚克力、水晶玻璃等高品质材料，确保产品质感和耐用性。',
                        icon: '',
                    },
                    {
                        number: '04',
                        title: '精密制造',
                        description: '经验丰富的工匠使用精密工具进行切割、焊接、抛光等工艺，关注每一个细节。',
                        icon: '',
                    },
                    {
                        number: '05',
                        title: '质量检验',
                        description: '成品经过严格的光电参数测试和外观质量检查，确保每一件灯具都符合高标准。',
                        icon: '',
                    },
                ],
            },
            backgroundImage: {
                type: 'string',
                default: '',
            },
            backgroundImageId: {
                type: 'number',
                default: 0,
            },
            backgroundOverlay: {
                type: 'number',
                default: 0.3,
            },
            backgroundPosition: {
                type: 'string',
                default: 'center center',
            },
            backgroundSize: {
                type: 'string',
                default: 'cover',
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var cards = attributes.cards;
            var blockProps = useBlockProps({
                className: 'craftsmanship-cards-block-editor',
            });

            // 更新卡片数量
            function updateCardCount() {
                var totalCards = attributes.columns * attributes.rows;
                var newCards = [...cards];
                
                // 如果需要更多卡片，添加默认卡片
                while (newCards.length < totalCards) {
                    newCards.push({
                        number: String(newCards.length + 1).padStart(2, '0'),
                        title: '新卡片标题',
                        description: '请输入卡片描述内容。',
                        icon: '',
                    });
                }
                
                // 如果卡片太多，截取需要的数量
                if (newCards.length > totalCards) {
                    newCards = newCards.slice(0, totalCards);
                }
                
                setAttributes({ cards: newCards });
            }

            // 更新单个卡片
            function updateCard(index, field, value) {
                var newCards = [...cards];
                newCards[index] = { ...newCards[index], [field]: value };
                setAttributes({ cards: newCards });
            }

            // 布局选项
            var layoutOptions = [
                { label: '5列1行', value: '5x1', columns: 5, rows: 1 },
                { label: '4列2行', value: '4x2', columns: 4, rows: 2 },
                { label: '3列2行', value: '3x2', columns: 3, rows: 2 },
                { label: '2列3行', value: '2x3', columns: 2, rows: 3 },
                { label: '自定义', value: 'custom', columns: attributes.columns, rows: attributes.rows },
            ];

            var currentLayout = layoutOptions.find(option => 
                option.columns === attributes.columns && option.rows === attributes.rows
            );
            var layoutValue = currentLayout ? currentLayout.value : 'custom';

            // 处理布局变化
            function handleLayoutChange(value) {
                var selectedLayout = layoutOptions.find(option => option.value === value);
                if (selectedLayout && value !== 'custom') {
                    setAttributes({
                        columns: selectedLayout.columns,
                        rows: selectedLayout.rows,
                    });
                }
            }

            // 当列数或行数改变时更新卡片
            element.useEffect(() => {
                updateCardCount();
            }, [attributes.columns, attributes.rows]);

            var totalCards = attributes.columns * attributes.rows;
            var gridClass = 'craftsmanship-process';

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '区块设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                setAttributes({ subtitle: value });
                            },
                        }),
                        el(TextControl, {
                            label: '主标题',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                        }),
                        el(TextareaControl, {
                            label: '描述',
                            value: attributes.description,
                            onChange: function(value) {
                                setAttributes({ description: value });
                            },
                        }),
                    ]),
                    el(PanelBody, { title: '布局设置', initialOpen: true }, [
                        el(SelectControl, {
                            label: '布局预设',
                            value: layoutValue,
                            options: layoutOptions.map(option => ({
                                label: option.label,
                                value: option.value,
                            })),
                            onChange: handleLayoutChange,
                        }),
                        layoutValue === 'custom' && el(RangeControl, {
                            label: '列数',
                            value: attributes.columns,
                            onChange: function(value) {
                                setAttributes({ columns: value });
                            },
                            min: 1,
                            max: 6,
                        }),
                        layoutValue === 'custom' && el(RangeControl, {
                            label: '行数',
                            value: attributes.rows,
                            onChange: function(value) {
                                setAttributes({ rows: value });
                            },
                            min: 1,
                            max: 5,
                        }),
                    ]),
                    el(PanelBody, { title: '背景设置', initialOpen: false }, [
                        el(MediaUploadCheck, {}, [
                            el(MediaUpload, {
                                onSelect: function(media) {
                                    setAttributes({
                                        backgroundImage: media.url,
                                        backgroundImageId: media.id,
                                    });
                                },
                                allowedTypes: ['image'],
                                value: attributes.backgroundImageId,
                                render: function(obj) {
                                    return el(Button, {
                                        className: attributes.backgroundImage ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                        onClick: obj.open,
                                    }, attributes.backgroundImage ? '更换背景图片' : '选择背景图片');
                                },
                            }),
                        ]),
                        attributes.backgroundImage && el(Button, {
                            onClick: function() {
                                setAttributes({
                                    backgroundImage: '',
                                    backgroundImageId: 0,
                                });
                            },
                            isDestructive: true,
                        }, '移除背景图片'),
                        attributes.backgroundImage && el(RangeControl, {
                            label: '背景遮罩透明度',
                            value: attributes.backgroundOverlay,
                            onChange: function(value) {
                                setAttributes({ backgroundOverlay: value });
                            },
                            min: 0,
                            max: 1,
                            step: 0.1,
                            help: '调整背景图片上的深色遮罩透明度，提高文字可读性',
                        }),
                        attributes.backgroundImage && el(SelectControl, {
                            label: '背景位置',
                            value: attributes.backgroundPosition,
                            options: [
                                { label: '居中', value: 'center center' },
                                { label: '顶部居中', value: 'center top' },
                                { label: '底部居中', value: 'center bottom' },
                                { label: '左上角', value: 'left top' },
                                { label: '右上角', value: 'right top' },
                                { label: '左下角', value: 'left bottom' },
                                { label: '右下角', value: 'right bottom' },
                            ],
                            onChange: function(value) {
                                setAttributes({ backgroundPosition: value });
                            },
                        }),
                        attributes.backgroundImage && el(SelectControl, {
                            label: '背景尺寸',
                            value: attributes.backgroundSize,
                            options: [
                                { label: '覆盖 (Cover)', value: 'cover' },
                                { label: '包含 (Contain)', value: 'contain' },
                                { label: '拉伸 (100% 100%)', value: '100% 100%' },
                                { label: '原始尺寸', value: 'auto' },
                            ],
                            onChange: function(value) {
                                setAttributes({ backgroundSize: value });
                            },
                        }),
                    ]),
                    // 卡片设置面板
                    cards.slice(0, totalCards).map(function(card, index) {
                        return el(PanelBody, {
                            key: index,
                            title: '卡片 ' + (index + 1) + ': ' + card.title,
                            initialOpen: false,
                        }, [
                            el(TextControl, {
                                label: '数字标识',
                                value: card.number,
                                onChange: function(value) {
                                    updateCard(index, 'number', value);
                                },
                            }),
                            el(TextControl, {
                                label: '标题',
                                value: card.title,
                                onChange: function(value) {
                                    updateCard(index, 'title', value);
                                },
                            }),
                            el(TextareaControl, {
                                label: '描述',
                                value: card.description,
                                onChange: function(value) {
                                    updateCard(index, 'description', value);
                                },
                            }),
                            el(MediaUploadCheck, {}, [
                                el(MediaUpload, {
                                    onSelect: function(media) {
                                        updateCard(index, 'icon', media.url);
                                    },
                                    allowedTypes: ['image'],
                                    value: card.icon,
                                    render: function(obj) {
                                        return el(Button, {
                                            className: card.icon ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                            onClick: obj.open,
                                        }, card.icon ? '更换图标' : '选择图标');
                                    },
                                }),
                            ]),
                            card.icon && el(Button, {
                                onClick: function() {
                                    updateCard(index, 'icon', '');
                                },
                                isDestructive: true,
                            }, '移除图标'),
                        ]);
                    }),
                ]),

                // 预览区域
                el('div', { className: 'craftsmanship-cards-block-preview' }, [
                    el('div', {
                        className: 'section',
                        style: attributes.backgroundImage ? {
                            backgroundImage: 'url(' + attributes.backgroundImage + ')',
                            backgroundPosition: attributes.backgroundPosition,
                            backgroundSize: attributes.backgroundSize,
                            backgroundRepeat: 'no-repeat',
                            position: 'relative',
                        } : {}
                    }, [
                        // 背景遮罩层
                        attributes.backgroundImage && el('div', {
                            className: 'background-overlay',
                            style: {
                                position: 'absolute',
                                top: 0,
                                left: 0,
                                right: 0,
                                bottom: 0,
                                backgroundColor: 'rgba(0, 0, 0, ' + attributes.backgroundOverlay + ')',
                                zIndex: 1,
                            }
                        }),
                        el('div', {
                            className: 'container',
                            style: {
                                position: 'relative',
                                zIndex: 2,
                            }
                        }, [
                            // 区块头部
                            (attributes.subtitle || attributes.title || attributes.description) && el('header', { className: 'section-header' }, [
                                attributes.subtitle && el('span', { className: 'section-subtitle' }, attributes.subtitle),
                                attributes.title && el('h2', { className: 'section-title' }, attributes.title),
                                attributes.description && el('p', { className: 'section-description' }, attributes.description),
                            ]),
                            
                            // 卡片网格
                            el('div', {
                                className: gridClass,
                                style: {
                                    gridTemplateColumns: 'repeat(' + attributes.columns + ', 1fr)',
                                },
                            }, cards.slice(0, totalCards).map(function(card, index) {
                                return el('div', {
                                    key: index,
                                    className: 'process-item',
                                }, [
                                    // 图标（如果有）
                                    card.icon && el('div', {
                                        className: 'process-icon',
                                        style: {
                                            position: 'absolute',
                                            top: '1rem',
                                            left: '1rem',
                                            width: '32px',
                                            height: '32px',
                                            zIndex: 2,
                                        },
                                    }, [
                                        el('img', {
                                            src: card.icon,
                                            alt: card.title,
                                            style: {
                                                width: '100%',
                                                height: '100%',
                                                objectFit: 'contain',
                                            },
                                        }),
                                    ]),
                                    // 数字标识
                                    el('span', { className: 'process-number' }, card.number),
                                    // 标题
                                    el('h3', {}, card.title),
                                    // 描述
                                    el('p', {}, card.description),
                                ]);
                            })),
                        ]),
                    ]),
                ]),
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
