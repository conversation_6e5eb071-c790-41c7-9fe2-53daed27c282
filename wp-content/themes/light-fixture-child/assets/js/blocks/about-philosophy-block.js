/**
 * 关于我们的理念区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var RangeControl = components.RangeControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/about-philosophy-block', {
        title: '关于我们的理念',
        icon: 'lightbulb',
        category: 'design',
        description: '添加一个理念展示区块，包含4个理念卡片的网格布局。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            subtitle: {
                type: 'string',
                default: 'Our Philosophy',
            },
            title: {
                type: 'string',
                default: 'Light is More Than Illumination<br>It\'s the Art of Living',
            },
            iconSize: {
                type: 'number',
                default: 48,
            },
            philosophies: {
                type: 'array',
                default: [
                    {
                        icon: '',
                        title: 'Artistic Expression',
                        description: 'We believe that lighting fixtures are not just tools for illumination but also works of art for the space. Each product is meticulously designed, perfectly combining aesthetics and functionality to add an artistic touch to any environment.',
                        svgIcon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>',
                    },
                    {
                        icon: '',
                        title: 'Eco-Friendly & Sustainable',
                        description: 'We are committed to using environmentally friendly materials and energy-efficient light sources, reducing energy consumption and environmental impact, contributing to sustainable development.',
                        svgIcon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line></svg>',
                    },
                    {
                        icon: '',
                        title: 'Exquisite Craftsmanship',
                        description: 'Every product is meticulously crafted by experienced artisans. From material selection to final assembly, every detail is strictly controlled to ensure exceptional quality.',
                        svgIcon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle><path d="M15.5 15.5L22 22"></path><circle cx="16.5" cy="7.5" r="2.5"></circle><circle cx="7.5" cy="16.5" r="2.5"></circle></svg>',
                    },
                    {
                        icon: '',
                        title: 'User Experience',
                        description: 'We deeply understand customer needs to create lighting solutions that are both beautiful and practical, focusing on light comfort and ease of operation to enhance users\' quality of life.',
                        svgIcon: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>',
                    },
                ],
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var blockProps = useBlockProps({
                className: 'about-philosophy-block-editor',
            });

            // 更新理念项目
            function updatePhilosophy(index, field, value) {
                var newPhilosophies = [...attributes.philosophies];
                newPhilosophies[index] = { ...newPhilosophies[index], [field]: value };
                setAttributes({ philosophies: newPhilosophies });
            }

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '标题设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                setAttributes({ subtitle: value });
                            },
                        }),
                        el(TextareaControl, {
                            label: '主标题（支持HTML）',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                            help: '可以使用 <br> 标签换行',
                        }),
                        el(RangeControl, {
                            label: '图标大小（像素）',
                            value: attributes.iconSize,
                            onChange: function(value) {
                                setAttributes({ iconSize: value });
                            },
                            min: 24,
                            max: 96,
                            step: 4,
                            help: '调整理念卡片中图标的大小',
                        }),
                    ]),
                    // 理念项目设置面板
                    attributes.philosophies.map(function(philosophy, index) {
                        return el(PanelBody, {
                            key: index,
                            title: '理念 ' + (index + 1) + ': ' + philosophy.title,
                            initialOpen: false,
                        }, [
                            el(TextControl, {
                                label: '标题',
                                value: philosophy.title,
                                onChange: function(value) {
                                    updatePhilosophy(index, 'title', value);
                                },
                            }),
                            el(TextareaControl, {
                                label: '描述',
                                value: philosophy.description,
                                onChange: function(value) {
                                    updatePhilosophy(index, 'description', value);
                                },
                            }),
                            el(MediaUploadCheck, {}, [
                                el(MediaUpload, {
                                    onSelect: function(media) {
                                        updatePhilosophy(index, 'icon', media.url);
                                    },
                                    allowedTypes: ['image'],
                                    value: philosophy.icon,
                                    render: function(obj) {
                                        return el(Button, {
                                            className: philosophy.icon ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                            onClick: obj.open,
                                        }, philosophy.icon ? '更换图标' : '选择图标');
                                    },
                                }),
                            ]),
                            philosophy.icon && el(Button, {
                                onClick: function() {
                                    updatePhilosophy(index, 'icon', '');
                                },
                                isDestructive: true,
                                isSmall: true,
                            }, '移除图标'),
                            el('p', { style: { fontSize: '12px', color: '#666', marginTop: '10px' } }, 
                                '如果没有上传图标，将使用默认的SVG图标。'
                            ),
                        ]);
                    }),
                ]),

                // 预览区域
                el('div', { className: 'about-philosophy-block-preview' }, [
                    el('section', { className: 'section bg-light' }, [
                        el('div', { className: 'container' }, [
                            // 区块头部
                            (attributes.subtitle || attributes.title) && el('div', { className: 'section-header' }, [
                                attributes.subtitle && el('span', { className: 'section-subtitle' }, attributes.subtitle),
                                attributes.title && el('h2', { 
                                    className: 'section-title',
                                    dangerouslySetInnerHTML: { __html: attributes.title }
                                }),
                            ]),
                            
                            // 理念网格
                            el('div', { className: 'philosophy-grid' }, 
                                attributes.philosophies.map(function(philosophy, index) {
                                    var delay = 'delay-' + index;
                                    return el('div', {
                                        key: index,
                                        className: 'philosophy-item fade-in ' + delay,
                                    }, [
                                        // 图标
                                        el('div', {
                                            className: 'philosophy-icon',
                                            style: {
                                                height: attributes.iconSize + 'px',
                                                width: attributes.iconSize + 'px',
                                                margin: '0 auto 1.5rem'
                                            }
                                        }, [
                                            philosophy.icon ?
                                                el('img', {
                                                    src: philosophy.icon,
                                                    alt: philosophy.title,
                                                    style: {
                                                        width: '100%',
                                                        height: '100%',
                                                        objectFit: 'contain'
                                                    }
                                                }) :
                                                el('div', {
                                                    style: {
                                                        width: '100%',
                                                        height: '100%'
                                                    },
                                                    dangerouslySetInnerHTML: {
                                                        __html: philosophy.svgIcon
                                                    }
                                                })
                                        ]),
                                        // 标题
                                        el('h3', {}, philosophy.title),
                                        // 描述
                                        el('p', {}, philosophy.description),
                                    ]);
                                })
                            ),
                        ]),
                    ]),
                ]),
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
