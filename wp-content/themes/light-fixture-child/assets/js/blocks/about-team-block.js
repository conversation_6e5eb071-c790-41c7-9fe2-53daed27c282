/**
 * 关于设计团队区块的编辑器脚本
 */
(function(blocks, element, blockEditor, components, i18n) {
    var el = element.createElement;
    var __ = i18n.__;
    var useBlockProps = blockEditor.useBlockProps;
    var InspectorControls = blockEditor.InspectorControls;
    var PanelBody = components.PanelBody;
    var TextControl = components.TextControl;
    var TextareaControl = components.TextareaControl;
    var Button = components.Button;
    var MediaUpload = blockEditor.MediaUpload;
    var MediaUploadCheck = blockEditor.MediaUploadCheck;

    // 注册区块
    blocks.registerBlockType('light-fixture/about-team-block', {
        title: '关于设计团队',
        icon: 'groups',
        category: 'design',
        description: '添加一个设计团队展示区块，包含团队成员的网格布局。',
        supports: {
            html: false,
            align: ['wide', 'full'],
        },
        attributes: {
            subtitle: {
                type: 'string',
                default: 'Our Team',
            },
            title: {
                type: 'string',
                default: 'A Creative Team Full of Passion',
            },
            description: {
                type: 'string',
                default: 'Our team consists of professional designers and engineers from diverse backgrounds, each member dedicated to transforming creative ideas into outstanding lighting products.',
            },
            teamMembers: {
                type: 'array',
                default: [
                    {
                        image: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                        name: 'Li Ming',
                        title: 'Founder & Chief Designer',
                        description: 'With 20 years of experience in lighting design and multiple international design awards, Li focuses on integrating light art with modern technology to create unique lighting experiences.',
                        alt: 'Li Ming - Founder & Chief Designer',
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                        name: 'Zhang Lin',
                        title: 'Product Design Director',
                        description: 'With a background in industrial design, Zhang excels at transforming artistic inspiration into viable product designs, focusing on detail and user experience, and is responsible for the design and development of the company\'s core product lines.',
                        alt: 'Zhang Lin - Product Design Director',
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                        name: 'Wang Jian',
                        title: 'R&D Director',
                        description: 'With a background in electronic engineering, Wang focuses on LED lighting technology and smart control system R&D, committed to enhancing product performance and user interaction experience.',
                        alt: 'Wang Jian - R&D Director',
                    },
                    {
                        image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                        name: 'Emma Chen',
                        title: 'Marketing Director',
                        description: 'With extensive experience in international marketing, Emma oversees our brand development and global outreach initiatives, ensuring our innovative designs reach audiences worldwide.',
                        alt: 'Emma Chen - Marketing Director',
                    },
                ],
            },
        },

        // 编辑器中的渲染函数
        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;
            var blockProps = useBlockProps({
                className: 'about-team-block-editor',
            });

            // 更新团队成员
            function updateTeamMember(index, field, value) {
                var newTeamMembers = [...attributes.teamMembers];
                newTeamMembers[index] = { ...newTeamMembers[index], [field]: value };
                setAttributes({ teamMembers: newTeamMembers });
            }

            return el('div', blockProps, [
                // 侧边栏控制
                el(InspectorControls, {}, [
                    el(PanelBody, { title: '标题设置', initialOpen: true }, [
                        el(TextControl, {
                            label: '副标题',
                            value: attributes.subtitle,
                            onChange: function(value) {
                                setAttributes({ subtitle: value });
                            },
                        }),
                        el(TextControl, {
                            label: '主标题',
                            value: attributes.title,
                            onChange: function(value) {
                                setAttributes({ title: value });
                            },
                        }),
                        el(TextareaControl, {
                            label: '描述',
                            value: attributes.description,
                            onChange: function(value) {
                                setAttributes({ description: value });
                            },
                        }),
                    ]),
                    // 团队成员设置面板
                    attributes.teamMembers.map(function(member, index) {
                        return el(PanelBody, {
                            key: index,
                            title: '成员 ' + (index + 1) + ': ' + member.name,
                            initialOpen: false,
                        }, [
                            el(TextControl, {
                                label: '姓名',
                                value: member.name,
                                onChange: function(value) {
                                    updateTeamMember(index, 'name', value);
                                },
                            }),
                            el(TextControl, {
                                label: '职位',
                                value: member.title,
                                onChange: function(value) {
                                    updateTeamMember(index, 'title', value);
                                },
                            }),
                            el(TextareaControl, {
                                label: '描述',
                                value: member.description,
                                onChange: function(value) {
                                    updateTeamMember(index, 'description', value);
                                },
                            }),
                            el(MediaUploadCheck, {}, [
                                el(MediaUpload, {
                                    onSelect: function(media) {
                                        updateTeamMember(index, 'image', media.url);
                                        updateTeamMember(index, 'alt', media.alt || member.alt);
                                    },
                                    allowedTypes: ['image'],
                                    value: member.image,
                                    render: function(obj) {
                                        return el(Button, {
                                            className: member.image ? 'editor-post-featured-image__preview' : 'editor-post-featured-image__toggle',
                                            onClick: obj.open,
                                        }, member.image ? '更换头像' : '选择头像');
                                    },
                                }),
                            ]),
                            el(TextControl, {
                                label: '图片Alt文本',
                                value: member.alt,
                                onChange: function(value) {
                                    updateTeamMember(index, 'alt', value);
                                },
                            }),
                            member.image && el(Button, {
                                onClick: function() {
                                    updateTeamMember(index, 'image', '');
                                },
                                isDestructive: true,
                                isSmall: true,
                            }, '移除头像'),
                        ]);
                    }),
                ]),

                // 预览区域
                el('div', { className: 'about-team-block-preview' }, [
                    el('section', { className: 'section' }, [
                        el('div', { className: 'container' }, [
                            // 区块头部
                            el('div', { className: 'section-header' }, [
                                attributes.subtitle && el('span', { className: 'section-subtitle' }, attributes.subtitle),
                                attributes.title && el('h2', { className: 'section-title' }, attributes.title),
                                attributes.description && el('p', { className: 'section-description' }, attributes.description),
                            ]),
                            
                            // 团队网格
                            el('div', { className: 'team-grid' }, 
                                attributes.teamMembers.map(function(member, index) {
                                    var delay = 'delay-' + index;
                                    return el('div', {
                                        key: index,
                                        className: 'team-member fade-in ' + delay,
                                    }, [
                                        // 头像
                                        el('div', { className: 'team-member__image' }, [
                                            member.image && el('img', {
                                                src: member.image,
                                                alt: member.alt,
                                            }),
                                        ]),
                                        // 信息
                                        el('div', { className: 'team-member__info' }, [
                                            el('h3', { className: 'team-member__name' }, member.name),
                                            el('p', { className: 'team-member__title' }, member.title),
                                            el('p', {}, member.description),
                                        ]),
                                    ]);
                                })
                            ),
                        ]),
                    ]),
                ]),
            ]);
        },

        // 保存时的渲染（前端显示时使用PHP渲染）
        save: function() {
            return null;
        },
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.blockEditor,
    window.wp.components,
    window.wp.i18n
);
