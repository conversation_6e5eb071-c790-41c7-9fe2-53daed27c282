document.addEventListener('DOMContentLoaded', function() {
    // Menu Toggle
    const menuToggle = document.querySelector('.menu-toggle');
    const primaryMenu = document.getElementById('primary-menu'); // Changed selector
    
    if (menuToggle && primaryMenu) {
        menuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            primaryMenu.classList.toggle('active');
            const isExpanded = this.getAttribute('aria-expanded') === 'true' || false;
            this.setAttribute('aria-expanded', !isExpanded);
            
            // Control body scroll by toggling a class on the body
            document.body.classList.toggle('mobile-menu-open');
        });
    }
    
    // Search Toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchForm = document.querySelector('.search-form-container');
    const searchClose = document.querySelector('.search-close');
    
    if (searchToggle && searchForm && searchClose) {
        searchToggle.addEventListener('click', function(e) {
            e.preventDefault();
            searchForm.style.display = 'block';
            document.querySelector('.search-field').focus();
        });
        
        searchClose.addEventListener('click', function() {
            searchForm.style.display = 'none';
        });
    }
});

// Scripts from page-contact-us.php
document.addEventListener('DOMContentLoaded', function() {
    // FAQ Accordion
    const faqItems = document.querySelectorAll('.contact-page .faq-item'); 
    
    if (faqItems.length > 0) {
        faqItems.forEach(item => {
            const question = item.querySelector('.faq-question');
            const answer = item.querySelector('.faq-answer'); // Get the answer element

            if (question && answer) { // Check if both question and answer elements exist
                // Set initial aria-hidden based on whether 'active' class is present (though CSS handles display)
                answer.setAttribute('aria-hidden', !item.classList.contains('active'));

                question.addEventListener('click', function() {
                    const isActive = item.classList.contains('active');
                    
                    // If you want only one item open at a time, uncomment and adjust this block
                    // faqItems.forEach(otherItem => {
                    //     if (otherItem !== item) {
                    //         otherItem.classList.remove('active');
                    //         otherItem.querySelector('.faq-question').setAttribute('aria-expanded', 'false');
                    //         otherItem.querySelector('.faq-answer').setAttribute('aria-hidden', 'true');
                    //     }
                    // });
                    
                    if (isActive) {
                        item.classList.remove('active');
                        question.setAttribute('aria-expanded', 'false');
                        answer.setAttribute('aria-hidden', 'true');
                    } else {
                        item.classList.add('active');
                        question.setAttribute('aria-expanded', 'true');
                        answer.setAttribute('aria-hidden', 'false');
                    }
                });

                // Add keyboard support for Enter and Space keys
                question.addEventListener('keydown', function(event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        this.click(); // Trigger the click event handler
                    }
                });
            }
        });
    }
    
    // Contact Form Submission Alert
    const contactForm = document.getElementById('contactForm');
    
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // In a real project, this would involve form validation and AJAX submission
            alert('Thank you for your message. We will get back to you soon!');
            contactForm.reset();
        });
    }
});

// Scripts from page-single-product.php
document.addEventListener('DOMContentLoaded', function() {
    // Thumbnail gallery functionality
    const mainImage = document.getElementById('mainProductImage');
    const thumbnails = document.querySelectorAll('.thumbnail-item');
    
    if (mainImage && thumbnails.length > 0) {
        thumbnails.forEach(thumb => {
            thumb.addEventListener('click', function() {
                const imgSrc = this.getAttribute('data-src');
                if (imgSrc) {
                    mainImage.setAttribute('src', imgSrc);
                }
                
                thumbnails.forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });
    }
    
    // Product option button functionality
    const optionGroups = document.querySelectorAll('.product-customization .option-group');
    
    if (optionGroups.length > 0) {
        optionGroups.forEach(group => {
            const optionButtonsInGroup = group.querySelectorAll('.option-button'); // More specific selector for buttons within this group
            if (optionButtonsInGroup.length > 0) {
                optionButtonsInGroup.forEach(button => {
                    button.addEventListener('click', function() {
                        // Remove 'active' and set aria-pressed to false for sibling buttons in the same group
                        optionButtonsInGroup.forEach(btn => {
                            btn.classList.remove('active');
                            btn.setAttribute('aria-pressed', 'false');
                        });
                        // Add 'active' and set aria-pressed to true to the clicked button
                        this.classList.add('active');
                        this.setAttribute('aria-pressed', 'true');
                        
                        // Optional: Store selected option or trigger further actions
                        // const selectedValue = this.getAttribute('data-value');
                        // const optionType = this.closest('.option-group').querySelector('label').textContent;
                        // console.log(optionType, selectedValue);
                    });
                });
            }
        });
    }
});
/**
 * Light Fixture - Custom JavaScript
 */
(function($) {
    'use strict';

    // 等待DOM完全加载
    $(document).ready(function() {
        
        // jQuery menu toggle logic removed to avoid conflict with vanilla JS version above.
        // If other specific jQuery functionalities for menu are needed, they should be carefully integrated.
        
        // 色温调节器功能（如果存在）
        const cctSlider = $('#cctSlider');
        if(cctSlider.length) {
            cctSlider.on('input', function() {
                const value = $(this).val();
                const images = $('.cct-image');
                
                // 隐藏所有图片
                images.removeClass('active');
                
                // 根据值显示对应图片
                if(value <= 33) {
                    $('.cct-image[data-cct="warm"]').addClass('active');
                } else if(value <= 66) {
                    $('.cct-image[data-cct="neutral"]').addClass('active');
                } else {
                    $('.cct-image[data-cct="cool"]').addClass('active');
                }
            });
        }
        
        // 产品配置器（如果存在）
        const configOptions = $('.config-option');
        if(configOptions.length) {
            configOptions.on('click', function() {
                const type = $(this).data('type');
                const value = $(this).data('value');
                
                // 切换活动状态
                $(this).siblings('[data-type="' + type + '"]').removeClass('active');
                $(this).addClass('active');
                
                // 更新产品图片
                if(type === 'color') {
                    $('#configurableProductImage').attr('src', $('#configurableProductImage').data('base') + '-' + value + '.jpg');
                }
            });
        }
        
        // 平滑滚动到锚点
        $('a[href*="#"]:not([href="#"])').on('click', function() {
            if (location.pathname.replace(/^\//, '') === this.pathname.replace(/^\//, '') && location.hostname === this.hostname) {
                let target = $(this.hash);
                target = target.length ? target : $('[name=' + this.hash.slice(1) + ']');
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                    return false;
                }
            }
        });
        
        // 图片懒加载
        if ('loading' in HTMLImageElement.prototype) {
            // 如果浏览器支持原生懒加载
            const images = document.querySelectorAll('img[loading="lazy"]');
            images.forEach(img => {
                img.src = img.dataset.src;
            });
        } else {
            // 回退方案 - 滚动时检测
            const lazyImages = [].slice.call(document.querySelectorAll('img.lazy'));
            
            if (lazyImages.length) {
                const lazyLoad = function() {
                    const scrollTop = window.pageYOffset;
                    lazyImages.forEach(function(img) {
                        if (img.offsetTop < window.innerHeight + scrollTop) {
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                        }
                    });
                    if (lazyImages.length === 0) {
                        document.removeEventListener('scroll', lazyLoad);
                        window.removeEventListener('resize', lazyLoad);
                    }
                };
                
                document.addEventListener('scroll', lazyLoad);
                window.addEventListener('resize', lazyLoad);
            }
        }
        
        // 视差滚动效果
        $(window).on('scroll', function() {
            const scrolled = $(window).scrollTop();
            $('.parallax-bg').css('transform', 'translateY(' + (scrolled * 0.3) + 'px)');
        });
        
    });
    
})(jQuery); 

// Scripts from front-page.php
document.addEventListener('DOMContentLoaded', function() {
    // Color Temperature Adjustment Function
    // Ensure this only runs if cctSlider exists to prevent errors on other pages
    const cctSlider = document.getElementById('cctSlider');
    const cctImages = document.querySelectorAll('.cct-image');
    
    if (cctSlider && cctImages.length) {
        cctSlider.addEventListener('input', function() {
            const value = this.value;
            
            // Remove all active states
            cctImages.forEach(img => img.classList.remove('active'));
            
            // Display corresponding image based on slider value
            if (value <= 33) {
                // Warm color temperature (0-33)
                document.querySelector('.cct-image[data-cct="warm"]').classList.add('active');
            } else if (value <= 66) {
                // Neutral color temperature (34-66)
                document.querySelector('.cct-image[data-cct="neutral"]').classList.add('active');
            } else {
                // Cool color temperature (67-100)
                document.querySelector('.cct-image[data-cct="cool"]').classList.add('active');
            }
        });
    }
    
    // Simple parallax scrolling effect
    const parallaxImage = document.querySelector('.parallax-image');
    
    if (parallaxImage) {
        window.addEventListener('scroll', function() {
            // Ensure parentElement exists before trying to get its BoundingClientRect
            if (parallaxImage.parentElement) {
                const scrollPosition = window.pageYOffset;
                // Check if parallaxImage.parentElement is still in the DOM and visible
                const parentRect = parallaxImage.parentElement.getBoundingClientRect();
                if (parentRect.width > 0 || parentRect.height > 0) { // Check if element is visible
                    const parentPosition = parentRect.top + scrollPosition;
                    const offset = (scrollPosition - parentPosition) * 0.2;
                
                    if (scrollPosition > parentPosition - window.innerHeight && 
                        scrollPosition < parentPosition + parallaxImage.parentElement.offsetHeight) {
                        parallaxImage.style.transform = `translateY(${offset}px)`;
                    }
                }
            }
        });
    }
});

// Scripts from page-products-list.php
document.addEventListener('DOMContentLoaded', function() {
    // Product Category Filter
    const filterButtons = document.querySelectorAll('.filter-option');
    const productCategories = document.querySelectorAll('.product-category');
    
    if (filterButtons.length && productCategories.length) {
        // Function to apply filter
        function applyFilter(filterValue) {
            productCategories.forEach(category => {
                if (filterValue === 'all' || category.id === filterValue) {
                    category.classList.add('active');
                } else {
                    category.classList.remove('active');
                }
            });
        }

        // Add click event listeners to filter buttons
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                filterButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-pressed', 'false');
                });
                this.classList.add('active');
                this.setAttribute('aria-pressed', 'true');
                const filter = this.getAttribute('data-filter');
                applyFilter(filter);
            });
        });

        // Initial filter application on page load
        const initialActiveButton = document.querySelector('.filter-option.active');
        if (initialActiveButton) {
            const initialFilter = initialActiveButton.getAttribute('data-filter');
            applyFilter(initialFilter);
        } else {
            // If no button is active by default, make 'all' active and apply it
            const allButton = document.querySelector('.filter-option[data-filter="all"]');
            if (allButton) {
                allButton.classList.add('active');
                applyFilter('all');
            } else if (filterButtons.length > 0) {
                // Fallback: if 'all' doesn't exist, activate the first button
                filterButtons[0].classList.add('active');
                applyFilter(filterButtons[0].getAttribute('data-filter'));
            }
        }
    }
});

/**
 * Light Fixture Child Theme Custom JavaScript
 * 提供全站通用的交互效果和动画
 */

(function($) {
    'use strict';
    
    // 存储通用函数
    var LF = {
        // 初始化函数
        init: function() {
            this.setupHeader();
            this.setupAnimations();
            this.setupProductInteractions();
            this.setupPhotoSwipe();
        },
        
        // 头部导航交互
        setupHeader: function() {
            var $header = $('.site-header');
            var $menuToggle = $('.menu-toggle');
            var $primaryMenu = $('#primary-menu');
            
            // 滚动时改变header样式
            $(window).on('scroll', function() {
                if ($(window).scrollTop() > 50) {
                    $header.addClass('scrolled');
                } else {
                    $header.removeClass('scrolled');
                }
            });
            
            // 初始化加载时检查滚动位置
            if ($(window).scrollTop() > 50) {
                $header.addClass('scrolled');
            }
            
            // 移动导航菜单交互
            $menuToggle.on('click', function() {
                $(this).toggleClass('active');
                $primaryMenu.toggleClass('active');
                $('body').toggleClass('mobile-menu-open');
            });
        },
        
        // 页面滚动动画
        setupAnimations: function() {
            // 使用Intersection Observer API监视元素进入视口
            if ('IntersectionObserver' in window) {
                var animatedElements = document.querySelectorAll('.fade-in, .slide-up');
                
                var observer = new IntersectionObserver(function(entries) {
                    entries.forEach(function(entry) {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animated');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.1
                });
                
                animatedElements.forEach(function(el) {
                    observer.observe(el);
                });
            } else {
                // 降级方案：简单显示所有元素
                $('.fade-in, .slide-up').addClass('animated');
            }
            
            // 平滑滚动到锚点
            $('a[href^="#"]:not([href="#"])').on('click', function(e) {
                var target = $(this.hash);
                if (target.length) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 800);
                }
            });
        },
        
        // 产品相关交互
        setupProductInteractions: function() {
            // 产品过滤器交互
            $('.filter-option').on('click', function() {
                var $this = $(this);
                var filter = $this.data('filter');
                
                // 更新活动状态
                $('.filter-option').removeClass('active').attr('aria-pressed', 'false');
                $this.addClass('active').attr('aria-pressed', 'true');
                
                // 显示相应类别
                if (filter === 'all') {
                    $('.product-category').first().addClass('active').siblings().removeClass('active');
                } else {
                    $('.product-category').removeClass('active');
                    $('#' + filter).addClass('active');
                }
                
                // 添加动画效果
                setTimeout(function() {
                    $('.product-category.active .product-card').each(function(index) {
                        var $card = $(this);
                        $card.removeClass('slide-up delay-1 delay-2 delay-3 delay-4 delay-5');
                        
                        setTimeout(function() {
                            $card.addClass('slide-up');
                            if (index % 5 === 0) $card.addClass('delay-1');
                            if (index % 5 === 1) $card.addClass('delay-2');
                            if (index % 5 === 2) $card.addClass('delay-3');
                            if (index % 5 === 3) $card.addClass('delay-4');
                            if (index % 5 === 4) $card.addClass('delay-5');
                        }, 10);
                    });
                }, 100);
            });
            
            // 产品缩略图交互
            $('.thumbnail-item').on('click', function() {
                var $this = $(this);
                var imageSrc = $this.data('src');
                var fallbackSrc = $this.data('src-fallback');
                var $mainImage = $('#mainProductImage');
                
                // 更新活动状态
                $('.thumbnail-item').removeClass('active');
                $this.addClass('active');
                
                // 添加过渡效果
                $mainImage.css('opacity', '0');
                
                // 检查图像可用性
                var tempImg = new Image();
                tempImg.onload = function() {
                    setTimeout(function() {
                        $mainImage.attr('src', imageSrc).css('opacity', '1');
                    }, 300);
                };
                tempImg.onerror = function() {
                    setTimeout(function() {
                        $mainImage.attr('src', fallbackSrc).css('opacity', '1');
                    }, 300);
                };
                tempImg.src = imageSrc;
            });
            
            // 产品选项按钮交互
            $('.option-button').on('click', function() {
                var $this = $(this);
                var $group = $this.closest('.option-group');
                
                // 更新活动状态
                $group.find('.option-button').removeClass('active').attr('aria-pressed', 'false');
                $this.addClass('active').attr('aria-pressed', 'true');
                
                // 显示选择反馈
                var optionValue = $this.data('value');
                var optionType = $group.find('label').text();
                LF.showSelectionFeedback(optionType, optionValue);
            });
        },
        
        // 选择反馈效果
        showSelectionFeedback: function(type, value) {
            var $feedback = $('.selection-feedback');
            if ($feedback.length === 0) {
                $feedback = $('<div class="selection-feedback"></div>');
                $('.product-actions').before($feedback);
            }
            
            $feedback.html('<span>' + type + ' ' + value + '</span>');
            $feedback.addClass('active');
            
            setTimeout(function() {
                $feedback.removeClass('active');
            }, 2000);
        },
        
        // 图片灯箱效果
        setupPhotoSwipe: function() {
            $(document).on('click', '.product-main-image, .showcase-gallery img, .temp-comparison-grid img', function() {
                var $this = $(this);
                var src = $this.attr('src');
                var alt = $this.attr('alt');
                
                // 创建灯箱元素
                var $overlay = $('<div class="image-overlay"></div>');
                var $container = $('<div class="image-overlay-container"></div>');
                var $largeImage = $('<img src="' + src + '" alt="' + alt + '">');
                var $closeBtn = $('<button class="overlay-close" aria-label="关闭">&times;</button>');
                
                $container.append($largeImage, $closeBtn);
                $overlay.append($container);
                $('body').append($overlay);
                
                // 显示灯箱
                setTimeout(function() {
                    $overlay.addClass('active');
                }, 10);
                
                // 禁止滚动
                $('body').css('overflow', 'hidden');
                
                // 关闭灯箱
                function closeOverlay() {
                    $overlay.removeClass('active');
                    setTimeout(function() {
                        $overlay.remove();
                        $('body').css('overflow', '');
                    }, 300);
                }
                
                $closeBtn.on('click', closeOverlay);
                $overlay.on('click', function(e) {
                    if (e.target === this) {
                        closeOverlay();
                    }
                });
                
                // ESC键关闭
                $(document).on('keydown.photoswipe', function(e) {
                    if (e.key === 'Escape') {
                        closeOverlay();
                        $(document).off('keydown.photoswipe');
                    }
                });
            });
        }
    };
    
    // DOM就绪后初始化
    $(document).ready(function() {
        LF.init();
    });
    
})(jQuery);