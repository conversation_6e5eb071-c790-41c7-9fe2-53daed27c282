/**
 * 首页轮播图脚本
 */
(function($) {
    'use strict';

    // 当文档加载完成后执行
    $(document).ready(function() {
        // 仅当 Slick 已存在且轮播图尚未初始化时才初始化
        if (typeof $.fn.slick !== 'undefined' && !$('.hero-slider').hasClass('slick-initialized')) {
            initHomeSlider();
        }
    });

    // 初始化首页轮播图
    function initHomeSlider() {
        var $slider = $('.hero-slider');
        
        if ($slider.length === 0) {
            return;
        }

        // 如 Slick 未加载，直接返回，由外部脚本稍后触发此函数
        if (typeof $.fn.slick === 'undefined') {
            return;
        }

        // 确保轮播图的DOM结构正确
        ensureSliderStructure($slider);

        // 在初始化前重置可能的样式
        resetSliderStyles($slider);

        // 设置轮播图选项
        try {
            $slider.slick({
                dots: true,             // 显示导航点
                // arrows: true,           // 显示箭头
                infinite: true,         // 无限循环
                speed: 500,             // 动画速度
                fade: true,             // 使用淡入淡出效果
                cssEase: 'linear',      // CSS动画效果
                autoplay: true,         // 自动播放
                autoplaySpeed: 5000,    // 自动播放间隔时间
                pauseOnHover: true,     // 鼠标悬停时暂停
                adaptiveHeight: false,  // 不使用自适应高度
                slidesToShow: 1,        // 一次显示一张幻灯片
                slidesToScroll: 1,      // 一次滚动一张幻灯片
                // prevArrow: '<button type="button" class="slick-prev"></button>',
                // nextArrow: '<button type="button" class="slick-next"></button>',
                responsive: [
                    {
                        breakpoint: 991,
                        settings: {
                            arrows: false // 在小屏幕上隐藏箭头
                        }
                    }
                ]
            });
            
            // 修复可能的样式问题
            fixSliderStyles();
        } catch (error) {
            // 使用备用轮播图解决方案
            useFallbackSlider($slider);
            return;
        }

        // 在轮播图切换时触发动画
        $slider.on('beforeChange', function(event, slick, currentSlide, nextSlide) {
            // 隐藏当前幻灯片的内容
            var $currentSlide = $(slick.$slides[currentSlide]);
            $currentSlide.find('.hero-content').removeClass('animated');
            
            // 延迟显示下一张幻灯片的内容，等待幻灯片切换完成
            setTimeout(function() {
                var $nextSlide = $(slick.$slides[nextSlide]);
                $nextSlide.find('.hero-content').addClass('animated');
            }, 500);
        });

        // 确保第一张幻灯片的内容显示动画
        setTimeout(function() {
            $slider.find('.slick-current .hero-content').addClass('animated');
        }, 100);
    }

    // 重置轮播图样式
    function resetSliderStyles($slider) {
        // 移除可能影响轮播图的样式
        $slider.find('.hero-slide').css({
            'position': 'relative',
            'display': 'block',
            'float': 'none',
            'width': '100%',
            'height': '100%',
            'opacity': '1',
            'visibility': 'visible'
        });

        // 重置Slick相关元素的样式
        $slider.find('.slick-track').css({
            'width': '100%',
            'height': '100%',
            'transform': 'translate3d(0, 0, 0)'
        });

        $slider.find('.slick-list').css({
            'height': '100%',
            'overflow': 'hidden'
        });
    }

    // 修复轮播图样式
    function fixSliderStyles() {
        // 添加必要的样式修复
        var style = document.createElement('style');
        style.innerHTML = `
            .slick-list, .slick-track {
                height: 100% !important;
                width: 100% !important;
            }
            .slick-track {
                transform: translate3d(0, 0, 0) !important;
            }
            .slick-slide {
                position: absolute !important;
                top: 0;
                left: 0;
                width: 100% !important;
                height: 100% !important;
                opacity: 0 !important;
                transition: opacity 0.5s ease;
                z-index: 0;
                visibility: hidden;
                float: none !important;
            }
            .slick-slide.slick-active {
                opacity: 1 !important;
                z-index: 1;
                visibility: visible;
            }
            .slick-slide > div {
                height: 100%;
                width: 100%;
            }
            .hero-slide {
                height: 100%;
                width: 100%;
            }
        `;
        document.head.appendChild(style);

        // 监听窗口大小变化，重新调整轮播图
        $(window).on('resize', function() {
            setTimeout(function() {
                $('.hero-slider').slick('setPosition');
            }, 200);
        });
    }

    // 确保轮播图的DOM结构正确
    function ensureSliderStructure($slider) {
        // 检查是否有直接的div子元素
        var $slides = $slider.children('.hero-slide');
        
        // 如果没有直接的div子元素，将所有内容包装在一个div中
        if ($slides.length === 0) {
            var $contents = $slider.contents();
            $slider.empty();
            $slider.append($('<div class="hero-slide"></div>').append($contents));
        }

        // 确保每个幻灯片都有正确的结构
        $slides.each(function() {
            var $slide = $(this);
            // 检查是否有hero-background
            if ($slide.find('.hero-background').length === 0) {
                var $contents = $slide.contents();
                $slide.empty();
                $slide.append($('<div class="hero-background"></div>').append($contents));
            }
            
            // 检查是否有hero-image
            var $background = $slide.find('.hero-background');
            if ($background.find('img.hero-image').length === 0) {
                $background.prepend('<img src="https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80" alt="Placeholder" class="hero-image">');
            }
        });
    }

    // 将 initHomeSlider 暴露到全局
    window.initHomeSlider = initHomeSlider;
})(jQuery); 