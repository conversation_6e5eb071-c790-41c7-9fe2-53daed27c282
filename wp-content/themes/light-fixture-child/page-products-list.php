<?php
/**
 * Template Name: Product List Page
 *
 * @package Light_Fixture_Child
 */

get_header();
?>

<main id="main" class="site-main">
    <!-- Product List Section -->
    <section class="section">
        <div class="container">
            <!-- Category Filter -->
            <div class="filter-categories fade-in">
                <div class="filter-title">
                    <h2>Product Collections</h2>
                </div>
                <div class="filter-options">
                    <button class="filter-option active" data-filter="all" aria-pressed="true">All</button>
                    <button class="filter-option" data-filter="ceiling" aria-pressed="false">Pendant Lights</button>
                    <button class="filter-option" data-filter="wall" aria-pressed="false">Wall Lights</button>
                    <button class="filter-option" data-filter="table" aria-pressed="false">Table Lamps</button>
                    <button class="filter-option" data-filter="floor" aria-pressed="false">Floor Lamps</button>
                    <button class="filter-option" data-filter="outdoor" aria-pressed="false">Outdoor Lights</button>
                </div>
            </div>

            <!-- Product Display Grid -->
            <div class="product-categories">
                <!-- Pendant Collection -->
                <div class="product-category active" id="ceiling">
                    <div class="category-header">
                        <h3>Pendant Light Collection</h3>
                        <p>Elegant suspended fixtures that become the focal point of any space, from minimalist to luxurious styles.</p>
                    </div>
                    <div class="products-grid">
                        <!-- Product 1 -->
                        <div class="product-card slide-up delay-1">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/pendant-1.jpg" alt="Nordic Minimalist Pendant Light" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1540932239986-30128078f3c5?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">Nordic Minimalist Pendant Light</h4>
                                <p class="product-card__description">Minimalist design, perfect for modern interiors.</p>
                            </div>
                        </div>
                        
                        <!-- Product 2 -->
                        <div class="product-card slide-up delay-2">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/pendant-2.jpg" alt="Crystal Chandelier" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1513506003901-1e6a229e2d15?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">Crystal Chandelier</h4>
                                <p class="product-card__description">Luxurious elegance that adds sophistication to any space.</p>
                            </div>
                        </div>
                        
                        <!-- Product 3 -->
                        <div class="product-card slide-up delay-3">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/pendant-3.jpg" alt="Industrial Pendant Light" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1536728757731-ce7bbfdf2e7c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">Industrial Pendant Light</h4>
                                <p class="product-card__description">Unique industrial design blending fashion and vintage elements.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wall Lamp Collection -->
                <div class="product-category" id="wall">
                    <div class="category-header">
                        <h3>Wall Light Collection</h3>
                        <p>Cleverly utilizing wall space to create soft illumination, perfect for areas with limited space.</p>
                    </div>
                    <div class="products-grid">
                        <!-- Product 1 -->
                        <div class="product-card slide-up">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/wall-1.jpg" alt="Modern Smart Wall Light" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1507473885765-e6ed057f782c?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">Modern Smart Wall Light</h4>
                                <p class="product-card__description">Smart control, adjustable brightness, ideal for modern homes.</p>
                            </div>
                        </div>
                        
                        <!-- Product 2 -->
                        <div class="product-card slide-up">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/wall-2.jpg" alt="European Style Wall Light" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1583847268964-b28dc8f51f92?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">European Style Wall Light</h4>
                                <p class="product-card__description">Classic elegance adding warmth to your space.</p>
                            </div>
                        </div>
                        
                        <!-- Product 3 -->
                        <div class="product-card slide-up">
                            <div class="product-card__image-container">
                                <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/wall-3.jpg" alt="Reading Wall Light" class="product-card__image" onerror="this.src='https://images.unsplash.com/photo-1581330513839-1c11783f0760?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url(home_url('/index.php/single-product/')); ?>" class="product-card__view-button">View Details</a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h4 class="product-card__title">Reading Wall Light</h4>
                                <p class="product-card__description">Designed for reading, eye-friendly comfort for enhanced reading experience.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="pagination fade-in">
                <a href="#" class="pagination__prev disabled">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="15 18 9 12 15 6"></polyline></svg>
                </a>
                <span class="pagination__current">1</span>
                <a href="#" class="pagination__page">2</a>
                <a href="#" class="pagination__page">3</a>
                <span class="pagination__dots">...</span>
                <a href="#" class="pagination__page">7</a>
                <a href="#" class="pagination__next">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="9 18 15 12 9 6"></polyline></svg>
                </a>
            </div>

            <!-- Custom Design CTA -->
            <div class="cta-box fade-in">
                <h3>Haven't Found Your Perfect Light?</h3>
                <p>We offer professional custom design services to create unique lighting solutions that perfectly match your space and requirements.</p>
                <a href="<?php echo esc_url(home_url('/index.php/contact/')); ?>" class="btn btn-primary">Inquire About Custom Services</a>
            </div>
        </div>
    </section>

    <?php
    // 支持WordPress编辑器内容
    while (have_posts()) :
        the_post();
        if (get_the_content()) {
            echo '<div class="container">';
            the_content();
            echo '</div>';
        }
    endwhile;
    ?>

</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面载入动画
    const animatedElements = document.querySelectorAll('.fade-in, .slide-up');
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animated');
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1
    });
    
    animatedElements.forEach(el => {
        observer.observe(el);
    });
    
    // 过滤功能
    const filterOptions = document.querySelectorAll('.filter-option');
    const productCategories = document.querySelectorAll('.product-category');
    
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 移除所有active类
            filterOptions.forEach(opt => {
                opt.classList.remove('active');
                opt.setAttribute('aria-pressed', 'false');
            });
            
            // 给当前点击的选项添加active类
            this.classList.add('active');
            this.setAttribute('aria-pressed', 'true');
            
            const filter = this.getAttribute('data-filter');
            
            if (filter === 'all') {
                // 显示第一个类别，隐藏其他
                productCategories.forEach((category, index) => {
                    if (index === 0) {
                        category.classList.add('active');
                        // 添加动画效果
                        animateProductCards(category);
                    } else {
                        category.classList.remove('active');
                    }
                });
            } else {
                // 隐藏所有类别
                productCategories.forEach(category => {
                    category.classList.remove('active');
                });
                
                // 显示对应类别
                const targetCategory = document.getElementById(filter);
                if (targetCategory) {
                    targetCategory.classList.add('active');
                    // 添加动画效果
                    animateProductCards(targetCategory);
                }
            }
        });
    });
    
    // 为产品卡片添加动画效果
    function animateProductCards(container) {
        const cards = container.querySelectorAll('.product-card');
        cards.forEach((card, index) => {
            // 移除所有动画相关类
            card.classList.remove('slide-up', 'delay-1', 'delay-2', 'delay-3', 'delay-4', 'delay-5');
            
            // 重新添加动画类
            setTimeout(() => {
                card.classList.add('slide-up');
                if (index % 5 === 0) card.classList.add('delay-1');
                if (index % 5 === 1) card.classList.add('delay-2');
                if (index % 5 === 2) card.classList.add('delay-3');
                if (index % 5 === 3) card.classList.add('delay-4');
                if (index % 5 === 4) card.classList.add('delay-5');
            }, 10);
        });
    }
    
    // 分页交互
    const paginationLinks = document.querySelectorAll('.pagination__page, .pagination__next, .pagination__prev');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            // 这里可以添加分页逻辑，当前仅做演示
            
            // 显示加载效果
            document.body.classList.add('page-loading');
            
            // 模拟页面加载延迟
            setTimeout(() => {
                document.body.classList.remove('page-loading');
                
                // 更新当前页码
                document.querySelector('.pagination__current').textContent = this.textContent;
                
                // 刷新产品卡片动画
                const activeCategory = document.querySelector('.product-category.active');
                if (activeCategory) {
                    animateProductCards(activeCategory);
                }
                
                // 滚动到页面顶部
                window.scrollTo({
                    top: document.querySelector('.filter-categories').offsetTop - 100,
                    behavior: 'smooth'
                });
            }, 500);
        });
    });
});
</script>

<?php
get_footer();
?> 