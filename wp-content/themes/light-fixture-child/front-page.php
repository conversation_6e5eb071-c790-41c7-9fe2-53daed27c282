<?php
/**
 * The template for displaying the front page.
 *
 * @package Light_Fixture_Child
 */

get_header();
?>

<main id="main" class="site-main">
    <!-- Hero Slider Section -->
    <section class="hero-section">
        <div class="hero-slider">
            <?php
            // 获取轮播图设置
            $slider_settings = get_option('light_fixture_slider_settings');
            $slider_count = isset($slider_settings['slider_count']) ? intval($slider_settings['slider_count']) : 3;
            
            // 如果有轮播图设置，则显示轮播图
            if ($slider_count > 0 && !empty($slider_settings)) {
                // 循环显示每个轮播图
                for ($i = 1; $i <= $slider_count; $i++) {
                    $image = isset($slider_settings['slider_' . $i . '_image']) ? $slider_settings['slider_' . $i . '_image'] : '';
                    $title = isset($slider_settings['slider_' . $i . '_title']) ? $slider_settings['slider_' . $i . '_title'] : '';
                    $subtitle = isset($slider_settings['slider_' . $i . '_subtitle']) ? $slider_settings['slider_' . $i . '_subtitle'] : '';
                    $description = isset($slider_settings['slider_' . $i . '_description']) ? $slider_settings['slider_' . $i . '_description'] : '';
                    $button_text = isset($slider_settings['slider_' . $i . '_button_text']) ? $slider_settings['slider_' . $i . '_button_text'] : '';
                    $button_url = isset($slider_settings['slider_' . $i . '_button_url']) ? $slider_settings['slider_' . $i . '_button_url'] : '';
                    $button2_text = isset($slider_settings['slider_' . $i . '_button2_text']) ? $slider_settings['slider_' . $i . '_button2_text'] : '';
                    $button2_url = isset($slider_settings['slider_' . $i . '_button2_url']) ? $slider_settings['slider_' . $i . '_button2_url'] : '';
                    
                    // 如果没有图片，使用默认图片
                    if (empty($image)) {
                        $image = get_stylesheet_directory_uri() . '/assets/images/hero-bg.jpg';
                    }
                    ?>
                    <div class="hero-slide">
                        <div class="hero-background">
                            <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($title); ?>" class="hero-image" onerror="this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80'; this.onerror=null;">
                        </div>
                        <div class="container">
                            <div class="hero-content">
                                <?php if (!empty($subtitle)) : ?>
                                    <span class="hero-subtitle"><?php echo esc_html($subtitle); ?></span>
                                <?php endif; ?>
                                
                                <?php if (!empty($title)) : ?>
                                    <h1 class="hero-title"><?php echo wp_kses_post($title); ?></h1>
                                <?php endif; ?>
                                
                                <?php if (!empty($description)) : ?>
                                    <p class="hero-description"><?php echo wp_kses_post($description); ?></p>
                                <?php endif; ?>
                                
                                <?php if (!empty($button_text) || !empty($button2_text)) : ?>
                                    <div class="hero-buttons">
                                        <?php if (!empty($button_text)) : ?>
                                            <a href="<?php echo esc_url($button_url); ?>" class="btn btn-primary"><?php echo esc_html($button_text); ?></a>
                                        <?php endif; ?>
                                        
                                        <?php if (!empty($button2_text)) : ?>
                                            <a href="<?php echo esc_url($button2_url); ?>" class="btn btn-secondary"><?php echo esc_html($button2_text); ?></a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <?php
                }
            } else {
                // 如果没有轮播图设置，则显示默认的静态英雄区域
                ?>
                <div class="hero-slide">
                    <div class="hero-background">
                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/hero-bg.jpg" alt="<?php bloginfo('name'); ?>" class="hero-image" onerror="this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80'; this.onerror=null;">
                    </div>
                    <div class="container">
                        <div class="hero-content slide-up">
                            <span class="hero-subtitle">Artisan Lighting Design</span>
                            <h1 class="hero-title">The Art of Light<br>Reimagining Spaces</h1>
                            <p class="hero-description">A perfect fusion of exceptional design and craftsmanship, bringing extraordinary lighting experiences to your space.</p>
                            <div class="hero-buttons">
                                <a href="<?php echo esc_url(home_url('/index.php/productslist/')); ?>" class="btn btn-primary">Explore Collection</a>
                                <a href="<?php echo esc_url(home_url('/index.php/about/')); ?>" class="btn btn-secondary">About Us</a>
                            </div>
                        </div>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </section>

    <?php
    // Support for WordPress editor content
    while (have_posts()) :
        the_post();
        if (get_the_content()) {
            echo '<div class="container">';
            the_content();
            echo '</div>';
        }
    endwhile;
    ?>

</main>

<?php
get_footer();
?> 