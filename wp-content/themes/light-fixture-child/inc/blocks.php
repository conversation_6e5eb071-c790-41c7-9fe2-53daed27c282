<?php
/**
 * 自定义区块注册
 *
 * @package Light_Fixture_Child
 */

/**
 * 注册自定义区块
 */
function light_fixture_register_blocks() {
    // 确保函数存在
    if (!function_exists('register_block_type')) {
        return;
    }

    // 注册轮播图区块
    register_block_type(
        'light-fixture/slider-block',
        array(
            'editor_script' => 'light-fixture-blocks-editor',
            'editor_style'  => 'light-fixture-blocks-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_slider_block',
            'attributes' => array(
                'sliderCount' => array(
                    'type' => 'number',
                    'default' => 3,
                ),
                'slides' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'image' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'subtitle' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'buttonText' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'buttonUrl' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'button2Text' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'button2Url' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                        ),
                    ),
                ),
                'autoplay' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'autoplaySpeed' => array(
                    'type' => 'number',
                    'default' => 5000,
                ),
                'showDots' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showArrows' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'fade' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );
    
    // 注册产品类目导航区块
    register_block_type(
        'light-fixture/product-categories-block',
        array(
            'editor_script' => 'light-fixture-product-categories-editor',
            'editor_style'  => 'light-fixture-product-categories-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_product_categories_block',
            'attributes' => array(
                'title' => array(
                    'type' => 'string',
                    'default' => 'Discover Our Product Collections',
                ),
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Featured Collections',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => 'Each lighting fixture is a work of art, adding unparalleled beauty and warmth to your space.',
                ),
                'columns' => array(
                    'type' => 'number',
                    'default' => 3,
                ),
                'rows' => array(
                    'type' => 'number',
                    'default' => 2,
                ),

                'showViewAllButton' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'viewAllButtonText' => array(
                    'type' => 'string',
                    'default' => 'Browse All Collections',
                ),
                'viewAllButtonLink' => array(
                    'type' => 'string',
                    'default' => '/index.php/productslist/',
                ),
                'categories' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'id' => array(
                                'type' => 'number',
                                'default' => 0,
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'image' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'link' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'buttonText' => array(
                                'type' => 'string',
                                'default' => 'View Collection',
                            ),
                            'order' => array(
                                'type' => 'number',
                                'default' => 0,
                            ),
                        ),
                    ),
                ),
                'useProductCategories' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'selectedCategories' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'number',
                    ),
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );
    
    // 注册关于英雄区域区块
    register_block_type(
        'light-fixture/about-hero-block',
        array(
            'editor_script' => 'light-fixture-about-hero-editor',
            'editor_style'  => 'light-fixture-about-hero-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_about_hero_block',
            'attributes' => array(
                'backgroundImage' => array(
                    'type' => 'string',
                    'default' => 'https://images.unsplash.com/photo-1600508774634-4e11d34730e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80',
                ),
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'About Us',
                ),
                'title' => array(
                    'type' => 'string',
                    'default' => 'Crafted with Ingenuity<br>The Art of Light & Shadow',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => 'We are dedicated to providing high-quality, artistic lighting solutions, reshaping spatial experiences with the art of light and shadow.',
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册关于品牌故事区块
    register_block_type(
        'light-fixture/about-brand-story-block',
        array(
            'editor_script' => 'light-fixture-about-brand-story-editor',
            'editor_style'  => 'light-fixture-about-brand-story-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_about_brand_story_block',
            'attributes' => array(
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Our Story',
                ),
                'title' => array(
                    'type' => 'string',
                    'default' => 'Born from Craftsmanship, Pursuing Excellence',
                ),
                'image' => array(
                    'type' => 'string',
                    'default' => 'https://images.unsplash.com/photo-1544457070-4cd773b4d71e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80',
                ),
                'imageAlt' => array(
                    'type' => 'string',
                    'default' => 'Founder at Work',
                ),
                'paragraphs' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'string',
                    ),
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册关于我们的理念区块
    register_block_type(
        'light-fixture/about-philosophy-block',
        array(
            'editor_script' => 'light-fixture-about-philosophy-editor',
            'editor_style'  => 'light-fixture-about-philosophy-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_about_philosophy_block',
            'attributes' => array(
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Our Philosophy',
                ),
                'title' => array(
                    'type' => 'string',
                    'default' => 'Light is More Than Illumination<br>It\'s the Art of Living',
                ),
                'iconSize' => array(
                    'type' => 'number',
                    'default' => 48,
                ),
                'philosophies' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'icon' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'svgIcon' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                        ),
                    ),
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册关于设计团队区块
    register_block_type(
        'light-fixture/about-team-block',
        array(
            'editor_script' => 'light-fixture-about-team-editor',
            'editor_style'  => 'light-fixture-about-team-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_about_team_block',
            'attributes' => array(
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Our Team',
                ),
                'title' => array(
                    'type' => 'string',
                    'default' => 'A Creative Team Full of Passion',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => 'Our team consists of professional designers and engineers from diverse backgrounds, each member dedicated to transforming creative ideas into outstanding lighting products.',
                ),
                'teamMembers' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'image' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'name' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'alt' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                        ),
                    ),
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册工艺展示卡片区块
    register_block_type(
        'light-fixture/craftsmanship-cards-block',
        array(
            'editor_script' => 'light-fixture-craftsmanship-cards-editor',
            'editor_style'  => 'light-fixture-craftsmanship-cards-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_craftsmanship_cards_block',
            'attributes' => array(
                'title' => array(
                    'type' => 'string',
                    'default' => '原创匠心，精工细作',
                ),
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Our Craftsmanship',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => '每一件灯具，从设计到成品，都经过严格的工艺流程和质量把控，确保卓越品质。',
                ),
                'columns' => array(
                    'type' => 'number',
                    'default' => 5,
                ),
                'rows' => array(
                    'type' => 'number',
                    'default' => 1,
                ),
                'cards' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'number' => array(
                                'type' => 'string',
                                'default' => '01',
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'icon' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                        ),
                    ),
                ),
                'backgroundImage' => array(
                    'type' => 'string',
                    'default' => '',
                ),
                'backgroundImageId' => array(
                    'type' => 'number',
                    'default' => 0,
                ),
                'backgroundOverlay' => array(
                    'type' => 'number',
                    'default' => 0.3,
                ),
                'backgroundPosition' => array(
                    'type' => 'string',
                    'default' => 'center center',
                ),
                'backgroundSize' => array(
                    'type' => 'string',
                    'default' => 'cover',
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册产品列表区块
    register_block_type(
        'light-fixture/products-list-block',
        array(
            'editor_script' => 'light-fixture-products-list-editor',
            'editor_style'  => 'light-fixture-products-list-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_products_list_block',
            'attributes' => array(
                'title' => array(
                    'type' => 'string',
                    'default' => 'Featured Products',
                ),
                'subtitle' => array(
                    'type' => 'string',
                    'default' => 'Our Products',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => 'Carefully selected lighting products that bring unique lighting experiences to your space.',
                ),
                'columns' => array(
                    'type' => 'number',
                    'default' => 5,
                ),
                'rows' => array(
                    'type' => 'number',
                    'default' => 2,
                ),
                'dataSource' => array(
                    'type' => 'string',
                    'default' => 'category', // 'category' or 'custom'
                ),
                'selectedCategory' => array(
                    'type' => 'number',
                    'default' => 0,
                ),
                'customProducts' => array(
                    'type' => 'array',
                    'default' => array(),
                    'items' => array(
                        'type' => 'object',
                        'properties' => array(
                            'name' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'title' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'description' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'image' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'link' => array(
                                'type' => 'string',
                                'default' => '',
                            ),
                            'buttonText' => array(
                                'type' => 'string',
                                'default' => 'View Details',
                            ),
                        ),
                    ),
                ),
                'showViewAllButton' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'viewAllButtonText' => array(
                    'type' => 'string',
                    'default' => 'View More Products',
                ),
                'viewAllButtonLink' => array(
                    'type' => 'string',
                    'default' => '/index.php/productslist/',
                ),
                'showProductTitle' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'showProductDescription' => array(
                    'type' => 'boolean',
                    'default' => true,
                ),
                'displayMode' => array(
                    'type' => 'string',
                    'default' => 'hover', // 'hover' or 'below'
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );

    // 注册图片画廊墙区块
    register_block_type(
        'light-fixture/gallery-wall-block',
        array(
            'editor_script' => 'light-fixture-gallery-wall-editor',
            'editor_style'  => 'light-fixture-gallery-wall-editor',
            'style'         => 'light-fixture-blocks',
            'render_callback' => 'light_fixture_render_gallery_wall_block',
            'attributes' => array(
                'title' => array(
                    'type' => 'string',
                    'default' => 'Factory Display',
                ),
                'description' => array(
                    'type' => 'string',
                    'default' => '',
                ),
                'images' => array(
                    'type' => 'array',
                    'default' => array(),
                ),
                'columns' => array(
                    'type' => 'number',
                    'default' => 3,
                ),
                'imageRatio' => array(
                    'type' => 'string',
                    'default' => 'square',
                ),
                'hoverEffect' => array(
                    'type' => 'string',
                    'default' => 'scale',
                ),
                'spacing' => array(
                    'type' => 'number',
                    'default' => 20,
                ),
                'marginLeft' => array(
                    'type' => 'number',
                    'default' => 40,
                ),
                'marginRight' => array(
                    'type' => 'number',
                    'default' => 40,
                ),
                'frontendMarginLeft' => array(
                    'type' => 'number',
                    'default' => 300,
                ),
                'frontendMarginRight' => array(
                    'type' => 'number',
                    'default' => 300,
                ),
                'showButton' => array(
                    'type' => 'boolean',
                    'default' => false,
                ),
                'buttonText' => array(
                    'type' => 'string',
                    'default' => '查看更多',
                ),
                'buttonUrl' => array(
                    'type' => 'string',
                    'default' => '',
                ),
                'backgroundColor' => array(
                    'type' => 'string',
                    'default' => 'light',
                ),
                'titleSize' => array(
                    'type' => 'number',
                    'default' => 40,
                ),
                'titleSpacing' => array(
                    'type' => 'number',
                    'default' => 30,
                ),
                'className' => array(
                    'type' => 'string',
                ),
            ),
        )
    );
}
add_action('init', 'light_fixture_register_blocks');

/**
 * 渲染轮播图区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_slider_block($attributes) {
    // 提取属性
    $slider_count = isset($attributes['sliderCount']) ? intval($attributes['sliderCount']) : 3;
    $slides = isset($attributes['slides']) ? $attributes['slides'] : array();
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';
    
    // 开始输出缓冲
    ob_start();
    ?>
    <section class="hero-section <?php echo esc_attr($class_name); ?>">
        <div class="hero-slider">
            <?php
            // 如果有轮播图数据，则显示轮播图
            if (!empty($slides)) {
                // 循环显示每个轮播图
                foreach ($slides as $slide) {
                    $image = isset($slide['image']) ? $slide['image'] : '';
                    $title = isset($slide['title']) ? $slide['title'] : '';
                    $subtitle = isset($slide['subtitle']) ? $slide['subtitle'] : '';
                    $description = isset($slide['description']) ? $slide['description'] : '';
                    $button_text = isset($slide['buttonText']) ? $slide['buttonText'] : '';
                    $button_url = isset($slide['buttonUrl']) ? $slide['buttonUrl'] : '';
                    $button2_text = isset($slide['button2Text']) ? $slide['button2Text'] : '';
                    $button2_url = isset($slide['button2Url']) ? $slide['button2Url'] : '';
                    
                    // 如果没有图片，使用默认图片
                    if (empty($image)) {
                        $image = get_stylesheet_directory_uri() . '/assets/images/hero-bg.jpg';
                    }
                    ?>
                    <div class="hero-slide">
                        <div class="hero-background">
                            <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($title); ?>" class="hero-image" onerror="this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80'; this.onerror=null;">
                        </div>
                        <div class="container">
                            <?php
                            // 检查是否有任何内容需要显示
                            $has_subtitle = !empty($subtitle);
                            $has_title = !empty($title);
                            $has_description = !empty($description);
                            $show_button1 = !empty($button_text) && !empty($button_url);
                            $show_button2 = !empty($button2_text) && !empty($button2_url);
                            
                            // 只有当至少有一个内容元素存在时，才显示hero-content容器
                            if ($has_subtitle || $has_title || $has_description || $show_button1 || $show_button2) :
                            ?>
                            <div class="hero-content">
                                <?php if ($has_subtitle) : ?>
                                    <span class="hero-subtitle"><?php echo esc_html($subtitle); ?></span>
                                <?php endif; ?>
                                
                                <?php if ($has_title) : ?>
                                    <h1 class="hero-title"><?php echo wp_kses_post($title); ?></h1>
                                <?php endif; ?>
                                
                                <?php if ($has_description) : ?>
                                    <p class="hero-description"><?php echo wp_kses_post($description); ?></p>
                                <?php endif; ?>
                                
                                <?php if ($show_button1 || $show_button2) : ?>
                                    <div class="hero-buttons">
                                        <?php if ($show_button1) : ?>
                                            <a href="<?php echo esc_url($button_url); ?>" class="btn btn-primary"><?php echo esc_html($button_text); ?></a>
                                        <?php endif; ?>
                                        
                                        <?php if ($show_button2) : ?>
                                            <a href="<?php echo esc_url($button2_url); ?>" class="btn btn-secondary"><?php echo esc_html($button2_text); ?></a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php
                }
            } else {
                // 如果没有轮播图数据，则显示默认的静态英雄区域
                ?>
                <div class="hero-slide">
                    <div class="hero-background">
                        <img src="<?php echo get_stylesheet_directory_uri(); ?>/assets/images/hero-bg.jpg" alt="<?php bloginfo('name'); ?>" class="hero-image" onerror="this.src='https://images.unsplash.com/photo-1586023492125-27b2c045efd7?ixlib=rb-1.2.1&auto=format&fit=crop&w=2000&q=80'; this.onerror=null;">
                    </div>
                    <div class="container">
                        <?php
                        // 默认轮播图内容
                        $default_subtitle = 'Artisan Lighting Design';
                        $default_title = 'The Art of Light<br>Reimagining Spaces';
                        $default_description = 'A perfect fusion of exceptional design and craftsmanship, bringing extraordinary lighting experiences to your space.';
                        $products_url = home_url('/index.php/productslist/');
                        $about_url = home_url('/index.php/about/');
                        
                        // 检查是否有任何内容需要显示
                        $has_default_content = !empty($default_subtitle) || !empty($default_title) || !empty($default_description) || !empty($products_url) || !empty($about_url);
                        
                        if ($has_default_content) :
                        ?>
                        <div class="hero-content slide-up">
                            <?php if (!empty($default_subtitle)) : ?>
                                <span class="hero-subtitle"><?php echo $default_subtitle; ?></span>
                            <?php endif; ?>
                            
                            <?php if (!empty($default_title)) : ?>
                                <h1 class="hero-title"><?php echo $default_title; ?></h1>
                            <?php endif; ?>
                            
                            <?php if (!empty($default_description)) : ?>
                                <p class="hero-description"><?php echo $default_description; ?></p>
                            <?php endif; ?>
                            
                            <?php if (!empty($products_url) || !empty($about_url)) : ?>
                                <div class="hero-buttons">
                                    <?php if (!empty($products_url)) : ?>
                                        <a href="<?php echo esc_url($products_url); ?>" class="btn btn-primary">Explore Collection</a>
                                    <?php endif; ?>
                                    <?php if (!empty($about_url)) : ?>
                                        <a href="<?php echo esc_url($about_url); ?>" class="btn btn-secondary">About Us</a>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php
            }
            ?>
        </div>
    </section>

    <script>
        (function($) {
            $(document).ready(function() {
                var $slider = $('.hero-slider');

                // 如果 Slick 库尚未加载，则异步加载
                if (typeof $.fn.slick === 'undefined') {
                    var slickCSS = document.createElement('link');
                    slickCSS.rel = 'stylesheet';
                    slickCSS.href = '<?php echo get_stylesheet_directory_uri(); ?>/assets/css/vendor/slick.css';
                    document.head.appendChild(slickCSS);

                    var slickThemeCSS = document.createElement('link');
                    slickThemeCSS.rel = 'stylesheet';
                    slickThemeCSS.href = '<?php echo get_stylesheet_directory_uri(); ?>/assets/css/vendor/slick-theme.css';
                    document.head.appendChild(slickThemeCSS);

                    var slickScript = document.createElement('script');
                    slickScript.src = '<?php echo get_stylesheet_directory_uri(); ?>/assets/js/vendor/slick.min.js';
                    slickScript.onload = function() {
                        // Slick 加载完成后，若轮播图尚未初始化，则调用初始化函数
                        if (!$slider.hasClass('slick-initialized') && typeof initHomeSlider === 'function') {
                            initHomeSlider();
                        }
                    };
                    document.body.appendChild(slickScript);
                } else {
                    // Slick 已加载，但如果轮播图未初始化，则初始化
                    if (!$slider.hasClass('slick-initialized') && typeof initHomeSlider === 'function') {
                        initHomeSlider();
                    }
                }
            });
        })(jQuery);
    </script>
    <?php
    
    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染产品类目导航区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_product_categories_block($attributes) {
    // 提取属性
    $title = isset($attributes['title']) ? $attributes['title'] : 'Discover Our Product Collections';
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Featured Collections';
    $description = isset($attributes['description']) ? $attributes['description'] : 'Each lighting fixture is a work of art, adding unparalleled beauty and warmth to your space.';
    $columns = isset($attributes['columns']) ? intval($attributes['columns']) : 3;
    $rows = isset($attributes['rows']) ? intval($attributes['rows']) : 2;
    $show_view_all_button = isset($attributes['showViewAllButton']) ? $attributes['showViewAllButton'] : true;
    $view_all_button_text = isset($attributes['viewAllButtonText']) ? $attributes['viewAllButtonText'] : 'Browse All Collections';
    $view_all_button_link = isset($attributes['viewAllButtonLink']) ? $attributes['viewAllButtonLink'] : '/index.php/productslist/';
    $use_product_categories = isset($attributes['useProductCategories']) ? $attributes['useProductCategories'] : true;
    $selected_categories = isset($attributes['selectedCategories']) ? $attributes['selectedCategories'] : array();
    $custom_categories = isset($attributes['categories']) ? $attributes['categories'] : array();
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';
    
    // 计算每行显示的最大项目数
    $items_per_row = $columns;
    $max_items = $rows * $columns;
    
    // 根据列数设置产品网格的类名
    $grid_class = 'products-grid';
    if ($columns !== 3) {
        $grid_class .= ' products-grid--' . $columns;
    }
    
    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <?php if (!empty($title) || !empty($subtitle) || !empty($description)) : ?>
            <header class="section-header text-center fade-in">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>
                
                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>
                
                <?php if (!empty($description)) : ?>
                    <p class="section-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </header>
            <?php endif; ?>
            
            <div class="<?php echo esc_attr($grid_class); ?>">
                <?php
                // 获取产品分类
                $product_categories = array();
                
                if ($use_product_categories) {
                    // 使用产品分类
                    $args = array(
                        'taxonomy' => 'product_category',
                        'hide_empty' => false,
                    );
                    
                    if (!empty($selected_categories)) {
                        $args['include'] = $selected_categories;
                    }
                    
                    $terms = get_terms($args);
                    
                    if (!is_wp_error($terms) && !empty($terms)) {
                        $product_categories = $terms;
                    }
                } else {
                    // 使用自定义分类
                    if (!empty($custom_categories)) {
                        foreach ($custom_categories as $index => $cat) {
                            $product_categories[] = (object) array(
                                'term_id' => $index,
                                'name' => $cat['title'],
                                'description' => $cat['description'],
                                'custom_image' => $cat['image'],
                                'custom_link' => $cat['link'],
                                'custom_button' => isset($cat['buttonText']) ? $cat['buttonText'] : 'View Collection',
                                'is_custom' => true,
                            );
                        }
                    }
                }
                
                // 如果没有分类，显示测试分类
                if (empty($product_categories)) {
                    echo '<!-- 没有找到产品分类，显示测试分类 -->';
                    
                    // 测试分类数据
                    $test_categories = array(
                        array(
                            'name' => 'Pendant Lights',
                            'description' => 'Elegant pendant light collection suitable for various indoor spaces.',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                        array(
                            'name' => 'Wall Lights',
                            'description' => 'Modern style wall light collection that adds highlights to your walls.',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                        array(
                            'name' => 'Table Lamps',
                            'description' => 'Exquisite table lamp collection that brings warm lighting to your desk.',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                    );
                    
                    // 尝试获取随机产品图片
                    $products = get_posts(array(
                        'post_type' => 'product',
                        'posts_per_page' => 3,
                        'orderby' => 'rand',
                    ));
                    
                    // 如果有产品，使用产品图片替换测试分类图片
                    if (!empty($products)) {
                        foreach ($products as $index => $product) {
                            if (isset($test_categories[$index]) && has_post_thumbnail($product->ID)) {
                                // 获取原始图片URL而不是缩略图
                                $original_image_url = wp_get_attachment_url(get_post_thumbnail_id($product->ID));
                                if ($original_image_url) {
                                    $test_categories[$index]['image'] = $original_image_url;
                                }
                            }
                        }
                    }
                    
                    $delay = 1;
                    $count = 0;
                    foreach ($test_categories as $category) {
                        // 限制显示的数量
                        if ($count >= $max_items) {
                            break;
                        }
                        ?>
                        <div class="product-card slide-up delay-<?php echo esc_attr($delay); ?>">
                            <div class="product-card__image-container">
                                <img src="<?php echo esc_url($category['image']); ?>" alt="<?php echo esc_attr($category['name']); ?>" class="product-card__image">
                                <div class="product-card__overlay">
                                    <a href="#" class="product-card__view-button"><?php echo esc_html__('View Collection', 'light-fixture-child'); ?></a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h3 class="product-card__title"><?php echo esc_html($category['name']); ?></h3>
                                <p class="product-card__description"><?php echo esc_html($category['description']); ?></p>
                            </div>
                        </div>
                        <?php
                        $delay++;
                        if ($delay > 3) {
                            $delay = 1;
                        }
                        $count++;
                    }
                } else {
                    // 显示实际分类
                    $delay = 1;
                    $count = 0;
                    foreach ($product_categories as $category) {
                        // 限制显示的数量
                        if ($count >= $max_items) {
                            break;
                        }
                        
                        $is_custom = isset($category->is_custom) && $category->is_custom;
                        
                        if ($is_custom) {
                            // 自定义分类
                            $category_image = !empty($category->custom_image) ? $category->custom_image : get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                            $category_link = !empty($category->custom_link) ? $category->custom_link : '#';
                            $button_text = !empty($category->custom_button) ? $category->custom_button : 'View Collection';
                        } else {
                            // 产品分类
                            $category_link = get_term_link($category);
                            $category_image = '';
                            
                            // 获取分类图片
                            $term_meta = get_term_meta($category->term_id, 'category_image', true);
                            if (!empty($term_meta)) {
                                // 获取原始图片URL而不是缩略图
                                $category_image = wp_get_attachment_url($term_meta);
                            }
                            
                            // 如果没有分类图片，尝试获取该分类下随机一个产品的特色图片
                            if (empty($category_image)) {
                                $products = get_posts(array(
                                    'post_type' => 'product',
                                    'posts_per_page' => -1,
                                    'orderby' => 'rand',
                                    'tax_query' => array(
                                        array(
                                            'taxonomy' => 'product_category',
                                            'field' => 'term_id',
                                            'terms' => $category->term_id,
                                        ),
                                    ),
                                ));
                                
                                foreach ($products as $product) {
                                    if (has_post_thumbnail($product->ID)) {
                                        // 获取原始图片URL而不是缩略图
                                        $category_image = wp_get_attachment_url(get_post_thumbnail_id($product->ID));
                                        break;
                                    }
                                }
                            }
                            
                            // 如果仍然没有图片，使用默认图片
                            if (empty($category_image)) {
                                $category_image = get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                            }
                            
                            $button_text = __('View Collection', 'light-fixture-child');
                        }
                        ?>
                        <div class="product-card slide-up delay-<?php echo esc_attr($delay); ?>">
                            <div class="product-card__image-container">
                                <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category->name); ?>" class="product-card__image" onerror="this.src='<?php echo esc_url(get_stylesheet_directory_uri()); ?>/assets/images/placeholder.jpg'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url($category_link); ?>" class="product-card__view-button"><?php echo esc_html($button_text); ?></a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h3 class="product-card__title"><?php echo esc_html($category->name); ?></h3>
                                <?php if (!empty($category->description)) : ?>
                                    <p class="product-card__description"><?php echo esc_html($category->description); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php
                        $delay++;
                        if ($delay > 3) {
                            $delay = 1;
                        }
                        $count++;
                    }
                }
                ?>
            </div>
            
            <?php if ($show_view_all_button && !empty($view_all_button_text) && !empty($view_all_button_link)) : ?>
            <div class="text-center mt-5">
                <a href="<?php echo esc_url(home_url($view_all_button_link)); ?>" class="btn btn-secondary"><?php echo esc_html($view_all_button_text); ?></a>
            </div>
            <?php endif; ?>
        </div>
    </section>
    <?php
    
    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染产品列表区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_products_list_block($attributes) {
    // 提取属性
    $title = isset($attributes['title']) ? $attributes['title'] : 'Featured Products';
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Our Products';
    $description = isset($attributes['description']) ? $attributes['description'] : 'Carefully selected lighting products that bring unique lighting experiences to your space.';
    $columns = isset($attributes['columns']) ? intval($attributes['columns']) : 5;
    $rows = isset($attributes['rows']) ? intval($attributes['rows']) : 2;
    $data_source = isset($attributes['dataSource']) ? $attributes['dataSource'] : 'category';
    $selected_category = isset($attributes['selectedCategory']) ? intval($attributes['selectedCategory']) : 0;
    $custom_products = isset($attributes['customProducts']) ? $attributes['customProducts'] : array();
    $show_view_all_button = isset($attributes['showViewAllButton']) ? $attributes['showViewAllButton'] : true;
    $view_all_button_text = isset($attributes['viewAllButtonText']) ? $attributes['viewAllButtonText'] : 'View More Products';
    $view_all_button_link = isset($attributes['viewAllButtonLink']) ? $attributes['viewAllButtonLink'] : '/index.php/productslist/';
    $show_product_title = isset($attributes['showProductTitle']) ? $attributes['showProductTitle'] : true;
    $show_product_description = isset($attributes['showProductDescription']) ? $attributes['showProductDescription'] : true;
    $display_mode = isset($attributes['displayMode']) ? $attributes['displayMode'] : 'hover';
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';
    
    // 计算每行显示的最大项目数
    $items_per_row = $columns;
    $max_items = $rows * $columns;
    
    // 根据列数设置产品网格的类名
    $grid_class = 'products-grid';
    if ($columns !== 5) {
        $grid_class .= ' products-grid--' . $columns;
    }
    
    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section products-list-block <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <?php if (!empty($title) || !empty($subtitle) || !empty($description)) : ?>
            <header class="section-header text-center fade-in">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>
                
                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>
                
                <?php if (!empty($description)) : ?>
                    <p class="section-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </header>
            <?php endif; ?>
            
            <div class="<?php echo esc_attr($grid_class); ?>">
                <?php
                $products = array();
                
                if ($data_source === 'category') {
                    // 从产品分类获取产品
                    $args = array(
                        'post_type' => 'product',
                        'posts_per_page' => $max_items,
                        'orderby' => 'date',
                        'order' => 'DESC',
                    );
                    
                    // 如果选择了特定分类
                    if ($selected_category > 0) {
                        $args['tax_query'] = array(
                            array(
                                'taxonomy' => 'product_category',
                                'field' => 'term_id',
                                'terms' => $selected_category,
                            ),
                        );
                    }
                    
                    $query = new WP_Query($args);
                    $products = $query->posts;
                } else {
                    // 使用自定义产品
                    $products = $custom_products;
                }
                
                // 如果没有产品，显示测试产品
                if (empty($products)) {
                    echo '<!-- 没有找到产品，显示测试产品 -->';
                    
                    // 测试产品数据
                    $test_products = array();
                    for ($i = 1; $i <= $max_items; $i++) {
                        $test_products[] = array(
                            'name' => 'Test Product ' . $i,
                            'title' => 'Modern Minimalist Pendant Light ' . $i,
                            'description' => 'Simple modern style pendant light, suitable for living rooms, dining rooms and other spaces.',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                            'link' => '#',
                            'buttonText' => 'View Details',
                        );
                    }
                    
                    $delay = 1;
                    $count = 0;
                    foreach ($test_products as $product) {
                        // 限制显示的数量
                        if ($count >= $max_items) {
                            break;
                        }
                        ?>
                        <div class="product-item slide-up delay-<?php echo esc_attr($delay); ?> display-mode-<?php echo esc_attr($display_mode); ?>">
                            <div class="product-item__image-container">
                                <img src="<?php echo esc_url($product['image']); ?>" alt="<?php echo esc_attr($product['title']); ?>" class="product-item__image">
                                <div class="product-item__overlay">
                                    <?php if ($display_mode === 'hover' && ($show_product_title || ($show_product_description && !empty($product['description'])))) : ?>
                                    <div class="product-item__hover-content">
                                        <?php if ($show_product_title) : ?>
                                        <h3 class="product-item__title"><?php echo esc_html($product['title']); ?></h3>
                                        <?php endif; ?>
                                        <?php if ($show_product_description && !empty($product['description'])) : ?>
                                        <p class="product-item__description"><?php echo esc_html($product['description']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <?php endif; ?>
                                    <a href="<?php echo esc_url($product['link']); ?>" class="product-item__view-button"><?php echo esc_html($product['buttonText']); ?></a>
                                </div>
                            </div>
                            <?php if ($display_mode === 'below' && ($show_product_title || ($show_product_description && !empty($product['description'])))) : ?>
                            <div class="product-item__content">
                                <?php if ($show_product_title) : ?>
                                <h3 class="product-item__title">
                                    <a href="<?php echo esc_url($product['link']); ?>"><?php echo esc_html($product['title']); ?></a>
                                </h3>
                                <?php endif; ?>
                                <?php if ($show_product_description && !empty($product['description'])) : ?>
                                <p class="product-item__description"><?php echo esc_html($product['description']); ?></p>
                                <?php endif; ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php
                        $delay++;
                        if ($delay > 3) {
                            $delay = 1;
                        }
                        $count++;
                    }
                } else {
                    // 显示实际产品
                    $delay = 1;
                    $count = 0;
                    
                    if ($data_source === 'category') {
                        // 从数据库获取的产品
                        foreach ($products as $product) {
                            // 限制显示的数量
                            if ($count >= $max_items) {
                                break;
                            }
                            
                            $product_id = $product->ID;
                            $product_title = get_the_title($product_id);
                            $product_link = get_permalink($product_id);
                            $product_description = get_the_excerpt($product_id);
                            
                            // 获取产品图片
                            $product_image = get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                            if (has_post_thumbnail($product_id)) {
                                $product_image = wp_get_attachment_url(get_post_thumbnail_id($product_id));
                            }
                            
                            // 获取产品SKU
                            $product_sku = get_post_meta($product_id, '_product_sku', true);
                            $sku_display = !empty($product_sku) ? '<span class="product-item__sku">SKU: ' . esc_html($product_sku) . '</span>' : '';
                            
                            // 获取产品状态
                            $product_status = get_post_meta($product_id, '_product_status', true);
                            $status_display = !empty($product_status) ? '<span class="product-item__status">' . esc_html($product_status) . '</span>' : '';
                            ?>
                            <div class="product-item slide-up delay-<?php echo esc_attr($delay); ?> display-mode-<?php echo esc_attr($display_mode); ?>">
                                <div class="product-item__image-container">
                                    <img src="<?php echo esc_url($product_image); ?>" alt="<?php echo esc_attr($product_title); ?>" class="product-item__image" onerror="this.src='<?php echo esc_url(get_stylesheet_directory_uri()); ?>/assets/images/placeholder.jpg'; this.onerror=null;">
                                    <div class="product-item__overlay">
                                        <?php 
                                        // 检查是否需要显示内容
                                        $show_content = ($show_product_title || ($show_product_description && !empty($product_description)) || !empty($sku_display));
                                        if ($display_mode === 'hover' && $show_content) : 
                                        ?>
                                        <div class="product-item__hover-content">
                                            <?php if ($show_product_title) : ?>
                                            <h3 class="product-item__title"><?php echo esc_html($product_title); ?></h3>
                                            <?php endif; ?>
                                            <?php if ($show_product_description && !empty($product_description)) : ?>
                                                <p class="product-item__description"><?php echo wp_trim_words($product_description, 15); ?></p>
                                            <?php endif; ?>
                                            <?php if (!empty($sku_display)) : ?>
                                                <div class="product-item__meta"><?php echo $sku_display; ?></div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                        <a href="<?php echo esc_url($product_link); ?>" class="product-item__view-button"><?php echo esc_html__('View Details', 'light-fixture-child'); ?></a>
                                    </div>
                                    <?php if (!empty($status_display)) : ?>
                                        <div class="product-item__status-tag"><?php echo $status_display; ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php if ($display_mode === 'below' && $show_content) : ?>
                                <div class="product-item__content">
                                    <?php if ($show_product_title) : ?>
                                    <h3 class="product-item__title">
                                        <a href="<?php echo esc_url($product_link); ?>"><?php echo esc_html($product_title); ?></a>
                                    </h3>
                                    <?php endif; ?>
                                    <?php if ($show_product_description && !empty($product_description)) : ?>
                                        <p class="product-item__description"><?php echo wp_trim_words($product_description, 15); ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($sku_display)) : ?>
                                        <div class="product-item__meta"><?php echo $sku_display; ?></div>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php
                            $delay++;
                            if ($delay > 3) {
                                $delay = 1;
                            }
                            $count++;
                        }
                    } else {
                        // 自定义产品
                        foreach ($products as $product) {
                            // 限制显示的数量
                            if ($count >= $max_items) {
                                break;
                            }
                            
                            $product_title = isset($product['title']) ? $product['title'] : '';
                            $product_name = isset($product['name']) ? $product['name'] : '';
                            $product_description = isset($product['description']) ? $product['description'] : '';
                            $product_image = isset($product['image']) ? $product['image'] : get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                            $product_link = isset($product['link']) ? $product['link'] : '#';
                            $button_text = isset($product['buttonText']) ? $product['buttonText'] : 'View Details';
                            
                            // 如果没有标题，使用名称
                            if (empty($product_title) && !empty($product_name)) {
                                $product_title = $product_name;
                            }
                            ?>
                            <div class="product-item slide-up delay-<?php echo esc_attr($delay); ?> display-mode-<?php echo esc_attr($display_mode); ?>">
                                <div class="product-item__image-container">
                                    <img src="<?php echo esc_url($product_image); ?>" alt="<?php echo esc_attr($product_title); ?>" class="product-item__image" onerror="this.src='<?php echo esc_url(get_stylesheet_directory_uri()); ?>/assets/images/placeholder.jpg'; this.onerror=null;">
                                    <div class="product-item__overlay">
                                        <?php if ($display_mode === 'hover' && ($show_product_title || ($show_product_description && !empty($product_description)))) : ?>
                                        <div class="product-item__hover-content">
                                            <?php if ($show_product_title) : ?>
                                            <h3 class="product-item__title"><?php echo esc_html($product_title); ?></h3>
                                            <?php endif; ?>
                                            <?php if ($show_product_description && !empty($product_description)) : ?>
                                                <p class="product-item__description"><?php echo esc_html($product_description); ?></p>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                        <a href="<?php echo esc_url($product_link); ?>" class="product-item__view-button"><?php echo esc_html($button_text); ?></a>
                                    </div>
                                </div>
                                <?php if ($display_mode === 'below' && ($show_product_title || ($show_product_description && !empty($product_description)))) : ?>
                                <div class="product-item__content">
                                    <?php if ($show_product_title) : ?>
                                    <h3 class="product-item__title">
                                        <a href="<?php echo esc_url($product_link); ?>"><?php echo esc_html($product_title); ?></a>
                                    </h3>
                                    <?php endif; ?>
                                    <?php if ($show_product_description && !empty($product_description)) : ?>
                                        <p class="product-item__description"><?php echo esc_html($product_description); ?></p>
                                    <?php endif; ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            <?php
                            $delay++;
                            if ($delay > 3) {
                                $delay = 1;
                            }
                            $count++;
                        }
                    }
                }
                ?>
            </div>
            
            <?php if ($show_view_all_button && !empty($view_all_button_text) && !empty($view_all_button_link)) : ?>
            <div class="text-center mt-5">
                <a href="<?php echo esc_url(home_url($view_all_button_link)); ?>" class="btn btn-secondary"><?php echo esc_html($view_all_button_text); ?></a>
            </div>
            <?php endif; ?>
        </div>
    </section>
    <?php
    
    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染工艺展示卡片区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_craftsmanship_cards_block($attributes) {
    // 提取属性
    $title = isset($attributes['title']) ? $attributes['title'] : '原创匠心，精工细作';
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Our Craftsmanship';
    $description = isset($attributes['description']) ? $attributes['description'] : '每一件灯具，从设计到成品，都经过严格的工艺流程和质量把控，确保卓越品质。';
    $columns = isset($attributes['columns']) ? intval($attributes['columns']) : 5;
    $rows = isset($attributes['rows']) ? intval($attributes['rows']) : 1;
    $cards = isset($attributes['cards']) ? $attributes['cards'] : array();
    $background_image = isset($attributes['backgroundImage']) ? $attributes['backgroundImage'] : '';
    $background_overlay = isset($attributes['backgroundOverlay']) ? $attributes['backgroundOverlay'] : 0.3;
    $background_position = isset($attributes['backgroundPosition']) ? $attributes['backgroundPosition'] : 'center center';
    $background_size = isset($attributes['backgroundSize']) ? $attributes['backgroundSize'] : 'cover';
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 计算总卡片数
    $total_cards = $columns * $rows;

    // 如果没有卡片数据，使用默认数据
    if (empty($cards)) {
        $cards = array(
            array(
                'number' => '01',
                'title' => '概念构思',
                'description' => '设计师从自然、建筑、艺术中汲取灵感，发展创意想法，绘制概念草图，确定设计方向。',
                'icon' => '',
            ),
            array(
                'number' => '02',
                'title' => '3D建模与原型制作',
                'description' => '运用3D建模软件将设计转化为数字模型，并打印原型进行评估和完善，确保设计的可行性和美观性。',
                'icon' => '',
            ),
            array(
                'number' => '03',
                'title' => '材料选择',
                'description' => '严格筛选优质金属、进口亚克力、水晶玻璃等高品质材料，确保产品质感和耐用性。',
                'icon' => '',
            ),
            array(
                'number' => '04',
                'title' => '精密制造',
                'description' => '经验丰富的工匠使用精密工具进行切割、焊接、抛光等工艺，关注每一个细节。',
                'icon' => '',
            ),
            array(
                'number' => '05',
                'title' => '质量检验',
                'description' => '成品经过严格的光电参数测试和外观质量检查，确保每一件灯具都符合高标准。',
                'icon' => '',
            ),
        );
    }

    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section bg-light <?php echo esc_attr($class_name); ?>" <?php if (!empty($background_image)) : ?>style="background-image: url(<?php echo esc_url($background_image); ?>); background-position: <?php echo esc_attr($background_position); ?>; background-size: <?php echo esc_attr($background_size); ?>; background-repeat: no-repeat; position: relative;"<?php endif; ?>>
        <?php if (!empty($background_image)) : ?>
        <div class="background-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, <?php echo esc_attr($background_overlay); ?>); z-index: 1;"></div>
        <?php endif; ?>
        <div class="container" style="position: relative; z-index: 2;">
            <?php if (!empty($title) || !empty($subtitle) || !empty($description)) : ?>
            <header class="section-header">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>

                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>

                <?php if (!empty($description)) : ?>
                    <p class="section-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </header>
            <?php endif; ?>

            <div class="craftsmanship-process" style="grid-template-columns: repeat(<?php echo esc_attr($columns); ?>, 1fr);">
                <?php
                $delay = 1;
                $count = 0;
                foreach ($cards as $card) {
                    // 限制显示的数量
                    if ($count >= $total_cards) {
                        break;
                    }

                    $card_number = isset($card['number']) ? $card['number'] : '';
                    $card_title = isset($card['title']) ? $card['title'] : '';
                    $card_description = isset($card['description']) ? $card['description'] : '';
                    $card_icon = isset($card['icon']) ? $card['icon'] : '';
                    ?>
                    <div class="process-item fade-in delay-<?php echo esc_attr($delay); ?>">
                        <?php if (!empty($card_icon)) : ?>
                        <div class="process-icon">
                            <img src="<?php echo esc_url($card_icon); ?>" alt="<?php echo esc_attr($card_title); ?>">
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($card_number)) : ?>
                        <span class="process-number"><?php echo esc_html($card_number); ?></span>
                        <?php endif; ?>

                        <?php if (!empty($card_title)) : ?>
                        <h3><?php echo esc_html($card_title); ?></h3>
                        <?php endif; ?>

                        <?php if (!empty($card_description)) : ?>
                        <p><?php echo esc_html($card_description); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php
                    $delay++;
                    if ($delay > 4) {
                        $delay = 1;
                    }
                    $count++;
                }
                ?>
            </div>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染关于英雄区域区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_about_hero_block($attributes) {
    // 提取属性
    $background_image = isset($attributes['backgroundImage']) ? $attributes['backgroundImage'] : 'https://images.unsplash.com/photo-1600508774634-4e11d34730e2?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80';
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'About Us';
    $title = isset($attributes['title']) ? $attributes['title'] : 'Crafted with Ingenuity<br>The Art of Light & Shadow';
    $description = isset($attributes['description']) ? $attributes['description'] : 'We are dedicated to providing high-quality, artistic lighting solutions, reshaping spatial experiences with the art of light and shadow.';
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 开始输出缓冲
    ob_start();
    ?>
    <section class="hero-section about-hero <?php echo esc_attr($class_name); ?>">
        <div class="hero-background">
            <img src="<?php echo esc_url($background_image); ?>" alt="<?php echo esc_attr(strip_tags($title)); ?>">
        </div>
        <div class="container">
            <div class="hero-content">
                <?php if (!empty($subtitle)) : ?>
                    <span class="hero-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>

                <?php if (!empty($title)) : ?>
                    <h1 class="hero-title"><?php echo wp_kses_post($title); ?></h1>
                <?php endif; ?>

                <?php if (!empty($description)) : ?>
                    <p class="hero-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染关于品牌故事区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_about_brand_story_block($attributes) {
    // 提取属性
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Our Story';
    $title = isset($attributes['title']) ? $attributes['title'] : 'Born from Craftsmanship, Pursuing Excellence';
    $image = isset($attributes['image']) ? $attributes['image'] : 'https://images.unsplash.com/photo-1544457070-4cd773b4d71e?ixlib=rb-1.2.1&auto=format&fit=crop&w=1200&q=80';
    $image_alt = isset($attributes['imageAlt']) ? $attributes['imageAlt'] : 'Founder at Work';
    $paragraphs = isset($attributes['paragraphs']) ? $attributes['paragraphs'] : array();
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 如果没有段落数据，使用默认数据
    if (empty($paragraphs)) {
        $paragraphs = array(
            'Our story began in 2008, founded by Li Ming, a designer passionate about the art of lighting. Having worked for years at internationally renowned lighting brands, Li Ming deeply understood the significant impact of light on spatial ambiance.',
            'With a profound understanding of light and shadow artistry and an unyielding pursuit of quality, we have grown from a small design studio into a comprehensive lighting enterprise with our own R&D, production, and sales teams.',
            'For fifteen years, we have consistently adhered to the design philosophy of "people-oriented, light as a medium," perfectly integrating artistic design with practical functionality to provide each client with a unique lighting experience.',
        );
    }

    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <?php if (!empty($subtitle) || !empty($title)) : ?>
            <div class="section-header">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>

                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <div class="about-story">
                <div class="about-story__image">
                    <?php if (!empty($image)) : ?>
                        <img src="<?php echo esc_url($image); ?>" alt="<?php echo esc_attr($image_alt); ?>">
                    <?php endif; ?>
                </div>
                <div class="about-story__content">
                    <?php foreach ($paragraphs as $paragraph) : ?>
                        <p><?php echo esc_html($paragraph); ?></p>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染关于我们的理念区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_about_philosophy_block($attributes) {
    // 提取属性
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Our Philosophy';
    $title = isset($attributes['title']) ? $attributes['title'] : 'Light is More Than Illumination<br>It\'s the Art of Living';
    $philosophies = isset($attributes['philosophies']) ? $attributes['philosophies'] : array();
    $icon_size = isset($attributes['iconSize']) ? intval($attributes['iconSize']) : 48;
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 如果没有理念数据，使用默认数据
    if (empty($philosophies)) {
        $philosophies = array(
            array(
                'icon' => '',
                'title' => 'Artistic Expression',
                'description' => 'We believe that lighting fixtures are not just tools for illumination but also works of art for the space. Each product is meticulously designed, perfectly combining aesthetics and functionality to add an artistic touch to any environment.',
                'svgIcon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="5"></circle><line x1="12" y1="1" x2="12" y2="3"></line><line x1="12" y1="21" x2="12" y2="23"></line><line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line><line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line><line x1="1" y1="12" x2="3" y2="12"></line><line x1="21" y1="12" x2="23" y2="12"></line><line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line><line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line></svg>',
            ),
            array(
                'icon' => '',
                'title' => 'Eco-Friendly & Sustainable',
                'description' => 'We are committed to using environmentally friendly materials and energy-efficient light sources, reducing energy consumption and environmental impact, contributing to sustainable development.',
                'svgIcon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M6 3v7a6 6 0 0 0 6 6 6 6 0 0 0 6-6V3"></path><line x1="4" y1="21" x2="20" y2="21"></line></svg>',
            ),
            array(
                'icon' => '',
                'title' => 'Exquisite Craftsmanship',
                'description' => 'Every product is meticulously crafted by experienced artisans. From material selection to final assembly, every detail is strictly controlled to ensure exceptional quality.',
                'svgIcon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M2 2l7.586 7.586"></path><circle cx="11" cy="11" r="2"></circle><path d="M15.5 15.5L22 22"></path><circle cx="16.5" cy="7.5" r="2.5"></circle><circle cx="7.5" cy="16.5" r="2.5"></circle></svg>',
            ),
            array(
                'icon' => '',
                'title' => 'User Experience',
                'description' => 'We deeply understand customer needs to create lighting solutions that are both beautiful and practical, focusing on light comfort and ease of operation to enhance users\' quality of life.',
                'svgIcon' => '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M23 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>',
            ),
        );
    }

    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section bg-light <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <?php if (!empty($subtitle) || !empty($title)) : ?>
            <div class="section-header">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>

                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo wp_kses_post($title); ?></h2>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <div class="philosophy-grid">
                <?php
                $delay = 0;
                foreach ($philosophies as $philosophy) {
                    $philosophy_icon = isset($philosophy['icon']) ? $philosophy['icon'] : '';
                    $philosophy_title = isset($philosophy['title']) ? $philosophy['title'] : '';
                    $philosophy_description = isset($philosophy['description']) ? $philosophy['description'] : '';
                    $philosophy_svg = isset($philosophy['svgIcon']) ? $philosophy['svgIcon'] : '';
                    ?>
                    <div class="philosophy-item fade-in delay-<?php echo esc_attr($delay); ?>">
                        <div class="philosophy-icon" style="height: <?php echo esc_attr($icon_size + 12); ?>px; width: <?php echo esc_attr($icon_size); ?>px; margin: 0 auto <?php echo esc_attr($icon_size * 0.3); ?>px;">
                            <?php if (!empty($philosophy_icon)) : ?>
                                <img src="<?php echo esc_url($philosophy_icon); ?>" alt="<?php echo esc_attr($philosophy_title); ?>" style="width: 100%; height: 100%; object-fit: contain;">
                            <?php elseif (!empty($philosophy_svg)) : ?>
                                <div style="width: 100%; height: 100%;">
                                <?php
                                // 直接输出SVG，因为这些是我们控制的安全内容
                                echo $philosophy_svg;
                                ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($philosophy_title)) : ?>
                        <h3><?php echo esc_html($philosophy_title); ?></h3>
                        <?php endif; ?>

                        <?php if (!empty($philosophy_description)) : ?>
                        <p><?php echo esc_html($philosophy_description); ?></p>
                        <?php endif; ?>
                    </div>
                    <?php
                    $delay++;
                }
                ?>
            </div>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染关于设计团队区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_about_team_block($attributes) {
    // 提取属性
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : 'Our Team';
    $title = isset($attributes['title']) ? $attributes['title'] : 'A Creative Team Full of Passion';
    $description = isset($attributes['description']) ? $attributes['description'] : 'Our team consists of professional designers and engineers from diverse backgrounds, each member dedicated to transforming creative ideas into outstanding lighting products.';
    $team_members = isset($attributes['teamMembers']) ? $attributes['teamMembers'] : array();
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 如果没有团队成员数据，使用默认数据
    if (empty($team_members)) {
        $team_members = array(
            array(
                'image' => 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'name' => 'Li Ming',
                'title' => 'Founder & Chief Designer',
                'description' => 'With 20 years of experience in lighting design and multiple international design awards, Li focuses on integrating light art with modern technology to create unique lighting experiences.',
                'alt' => 'Li Ming - Founder & Chief Designer',
            ),
            array(
                'image' => 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'name' => 'Zhang Lin',
                'title' => 'Product Design Director',
                'description' => 'With a background in industrial design, Zhang excels at transforming artistic inspiration into viable product designs, focusing on detail and user experience, and is responsible for the design and development of the company\'s core product lines.',
                'alt' => 'Zhang Lin - Product Design Director',
            ),
            array(
                'image' => 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'name' => 'Wang Jian',
                'title' => 'R&D Director',
                'description' => 'With a background in electronic engineering, Wang focuses on LED lighting technology and smart control system R&D, committed to enhancing product performance and user interaction experience.',
                'alt' => 'Wang Jian - R&D Director',
            ),
            array(
                'image' => 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'name' => 'Emma Chen',
                'title' => 'Marketing Director',
                'description' => 'With extensive experience in international marketing, Emma oversees our brand development and global outreach initiatives, ensuring our innovative designs reach audiences worldwide.',
                'alt' => 'Emma Chen - Marketing Director',
            ),
        );
    }

    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <div class="section-header">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>

                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>

                <?php if (!empty($description)) : ?>
                    <p class="section-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </div>

            <div class="team-grid">
                <?php
                $delay = 0;
                foreach ($team_members as $member) {
                    $member_image = isset($member['image']) ? $member['image'] : '';
                    $member_name = isset($member['name']) ? $member['name'] : '';
                    $member_title = isset($member['title']) ? $member['title'] : '';
                    $member_description = isset($member['description']) ? $member['description'] : '';
                    $member_alt = isset($member['alt']) ? $member['alt'] : $member_name;
                    ?>
                    <div class="team-member fade-in delay-<?php echo esc_attr($delay); ?>">
                        <div class="team-member__image">
                            <?php if (!empty($member_image)) : ?>
                                <img src="<?php echo esc_url($member_image); ?>" alt="<?php echo esc_attr($member_alt); ?>">
                            <?php endif; ?>
                        </div>
                        <div class="team-member__info">
                            <?php if (!empty($member_name)) : ?>
                            <h3 class="team-member__name"><?php echo esc_html($member_name); ?></h3>
                            <?php endif; ?>

                            <?php if (!empty($member_title)) : ?>
                            <p class="team-member__title"><?php echo esc_html($member_title); ?></p>
                            <?php endif; ?>

                            <?php if (!empty($member_description)) : ?>
                            <p><?php echo esc_html($member_description); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php
                    $delay++;
                }
                ?>
            </div>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 渲染图片画廊墙区块
 */
function light_fixture_render_gallery_wall_block($attributes) {
    // 获取属性值
    $title = isset($attributes['title']) ? $attributes['title'] : 'Factory Display';
    $description = isset($attributes['description']) ? $attributes['description'] : '';
    $images = isset($attributes['images']) ? $attributes['images'] : array();
    $columns = isset($attributes['columns']) ? $attributes['columns'] : 3;
    $image_ratio = isset($attributes['imageRatio']) ? $attributes['imageRatio'] : 'square';
    $hover_effect = isset($attributes['hoverEffect']) ? $attributes['hoverEffect'] : 'scale';
    $spacing = isset($attributes['spacing']) ? $attributes['spacing'] : 20;
    $margin_left = isset($attributes['marginLeft']) ? $attributes['marginLeft'] : 40;
    $margin_right = isset($attributes['marginRight']) ? $attributes['marginRight'] : 40;
    $frontend_margin_left = isset($attributes['frontendMarginLeft']) ? $attributes['frontendMarginLeft'] : 300;
    $frontend_margin_right = isset($attributes['frontendMarginRight']) ? $attributes['frontendMarginRight'] : 300;
    $show_button = isset($attributes['showButton']) ? $attributes['showButton'] : false;
    $button_text = isset($attributes['buttonText']) ? $attributes['buttonText'] : '查看更多';
    $button_url = isset($attributes['buttonUrl']) ? $attributes['buttonUrl'] : '';
    $background_color = isset($attributes['backgroundColor']) ? $attributes['backgroundColor'] : 'light';
    $title_size = isset($attributes['titleSize']) ? $attributes['titleSize'] : 40;
    $title_spacing = isset($attributes['titleSpacing']) ? $attributes['titleSpacing'] : 30;
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';

    // 如果没有图片，返回空
    if (empty($images)) {
        return '';
    }

    // 开始输出缓冲
    ob_start();
    ?>
    <section id="gallery-wall-<?php echo uniqid(); ?>" class="gallery-wall pb-30 text-center <?php echo esc_attr($background_color); ?> pt-50 <?php echo esc_attr($class_name); ?>" data-group="Gallery">
        <div class="center" style="padding-left: <?php echo esc_attr($margin_left + $frontend_margin_left); ?>px; padding-right: <?php echo esc_attr($margin_right + $frontend_margin_right); ?>px;">
            <?php if (!empty($title)) : ?>
                <h1 class="title aos-init aos-animate" data-aos="fade-down" style="font-size: <?php echo esc_attr($title_size); ?>px;">
                    <?php echo esc_html($title); ?>
                </h1>
            <?php endif; ?>

            <?php if (!empty($description)) : ?>
                <p class="gallery-description" style="margin-top: 1rem; color: #666;">
                    <?php echo esc_html($description); ?>
                </p>
            <?php endif; ?>

            <div class="flexW" style="display: grid; grid-template-columns: repeat(<?php echo esc_attr($columns); ?>, 1fr); gap: <?php echo esc_attr($spacing); ?>px; margin-top: <?php echo esc_attr($title_spacing); ?>px;">
                <?php
                $delay = 0;
                foreach ($images as $image) {
                    $image_id = isset($image['id']) ? $image['id'] : 0;
                    $image_url = isset($image['url']) ? $image['url'] : '';
                    $image_alt = isset($image['alt']) ? $image['alt'] : '';
                    $image_caption = isset($image['caption']) ? $image['caption'] : '';

                    if (empty($image_url)) {
                        continue;
                    }

                    $delay += 0.1;
                    ?>
                    <div class="item content-box aos-init mb-20 aos-animate hover-<?php echo esc_attr($hover_effect); ?>" data-aos="fade-right" data-aos-delay="<?php echo esc_attr($delay * 100); ?>">
                        <div class="imgW ratio-<?php echo esc_attr($image_ratio); ?>" style="<?php
                            if ($image_ratio === 'square') {
                                echo 'position: relative; padding-top: 100%; overflow: hidden; border-radius: 4px;';
                            } elseif ($image_ratio === 'landscape') {
                                echo 'position: relative; padding-top: 75%; overflow: hidden; border-radius: 4px;';
                            } elseif ($image_ratio === 'portrait') {
                                echo 'position: relative; padding-top: 133.33%; overflow: hidden; border-radius: 4px;';
                            } elseif ($image_ratio === 'wide') {
                                echo 'position: relative; padding-top: 56.25%; overflow: hidden; border-radius: 4px;';
                            } else {
                                echo 'border-radius: 4px; overflow: hidden;';
                            }
                        ?>">
                            <img src="<?php echo esc_url($image_url); ?>"
                                 alt="<?php echo esc_attr($image_alt); ?>"
                                 style="<?php
                                    if ($image_ratio === 'original') {
                                        echo 'width: 100%; height: auto; display: block;';
                                    } else {
                                        echo 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover;';
                                    }
                                 ?>">
                            <?php if (!empty($image_caption)) : ?>
                                <div class="image-caption" style="position: absolute; bottom: 0; left: 0; right: 0; background: rgba(0,0,0,0.7); color: white; padding: 8px; font-size: 12px;">
                                    <?php echo esc_html($image_caption); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php
                }
                ?>
            </div>

            <?php if ($show_button && !empty($button_text)) : ?>
                <div class="btnBox" style="z-index: 10; margin-top: 30px;">
                    <a href="<?php echo esc_url($button_url); ?>" class="btn btn-primary" style="display: inline-block; padding: 12px 24px; background-color: #d6ad60; color: #fff; text-decoration: none; border-radius: 4px; transition: background-color 0.3s ease;">
                        <?php echo esc_html($button_text); ?>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </section>
    <?php

    // 返回输出缓冲的内容
    return ob_get_clean();
}