<?php
/**
 * 主题设置与初始化
 *
 * @package Light_Fixture_Child
 */

/**
 * 注册菜单
 */
function light_fixture_child_register_menus() {
    register_nav_menus(
        array(
            'primary' => esc_html__('Primary Menu', 'light-fixture-child'),
            'footer'  => esc_html__('Footer Menu', 'light-fixture-child'),
            'left-panel-menu' => esc_html__('Left Panel Menu', 'light-fixture-child'),
        )
    );
}
add_action('init', 'light_fixture_child_register_menus');

/**
 * 添加自定义小部件区域
 */
function light_fixture_child_widgets_init() {
    register_sidebar(
        array(
            'name'          => esc_html__('Footer 1', 'light-fixture-child'),
            'id'            => 'footer-1',
            'description'   => esc_html__('Add widgets here.', 'light-fixture-child'),
            'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    register_sidebar(
        array(
            'name'          => esc_html__('Footer 2', 'light-fixture-child'),
            'id'            => 'footer-2',
            'description'   => esc_html__('Add widgets here.', 'light-fixture-child'),
            'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    register_sidebar(
        array(
            'name'          => esc_html__('Footer 3', 'light-fixture-child'),
            'id'            => 'footer-3',
            'description'   => esc_html__('Add widgets here.', 'light-fixture-child'),
            'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );

    register_sidebar(
        array(
            'name'          => esc_html__('Footer 4', 'light-fixture-child'),
            'id'            => 'footer-4',
            'description'   => esc_html__('Add widgets here.', 'light-fixture-child'),
            'before_widget' => '<div id="%1$s" class="footer-widget %2$s">',
            'after_widget'  => '</div>',
            'before_title'  => '<h3 class="widget-title">',
            'after_title'   => '</h3>',
        )
    );
}
add_action('widgets_init', 'light_fixture_child_widgets_init');

/**
 * 设置主题功能和注册所有自定义图片尺寸
 *
 * 整合并优化了之前分散的 add_theme_support 和 add_image_size 调用。
 * 遵循 WordPress 主题开发最佳实践。
 */
function light_fixture_theme_setup_and_image_sizes() {
    // 主题支持
    add_theme_support( 'post-thumbnails' ); // 启用特色图片
    add_theme_support( 'custom-logo', array(
        'height'      => 60,
        'width'       => 200,
        'flex-height' => true,
        'flex-width'  => true,
    ) );
    add_theme_support( 'title-tag' ); // 启用标题标签
    add_theme_support( 'html5', array( // 启用HTML5
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ) );
    add_theme_support( 'custom-header', array( // 启用自定义头部
        'default-image'      => '',
        'width'              => 1920,
        'height'             => 400,
        'flex-height'        => true,
        'flex-width'         => true,
        'default-text-color' => 'fff',
        'header-text'        => true,
    ) );

    // Gutenberg编辑器支持
    add_theme_support( 'editor-styles' ); // 启用编辑器样式
    add_theme_support( 'wp-block-styles' ); // 启用默认区块样式
    add_theme_support( 'align-wide' ); // 启用宽对齐和全宽对齐
    add_theme_support( 'responsive-embeds' ); // 启用响应式嵌入内容

    // 启用区块编辑器的颜色调色板
    add_theme_support( 'editor-color-palette', array(
        array(
            'name'  => esc_html__( 'Primary Gold', 'light-fixture-child' ),
            'slug'  => 'primary-gold',
            'color' => '#d6ad60',
        ),
        array(
            'name'  => esc_html__( 'Light Gold', 'light-fixture-child' ),
            'slug'  => 'light-gold',
            'color' => '#e4c078',
        ),
        array(
            'name'  => esc_html__( 'Dark Gold', 'light-fixture-child' ),
            'slug'  => 'dark-gold',
            'color' => '#c19a4a',
        ),
        array(
            'name'  => esc_html__( 'Pure Black', 'light-fixture-child' ),
            'slug'  => 'pure-black',
            'color' => '#000000',
        ),
        array(
            'name'  => esc_html__( 'Pure White', 'light-fixture-child' ),
            'slug'  => 'pure-white',
            'color' => '#ffffff',
        ),
        array(
            'name'  => esc_html__( 'Dark Gray', 'light-fixture-child' ),
            'slug'  => 'dark-gray',
            'color' => '#333333',
        ),
    ) );

    // 启用区块编辑器的字体大小
    add_theme_support( 'editor-font-sizes', array(
        array(
            'name' => esc_html__( 'Small', 'light-fixture-child' ),
            'size' => 14,
            'slug' => 'small'
        ),
        array(
            'name' => esc_html__( 'Normal', 'light-fixture-child' ),
            'size' => 17,
            'slug' => 'normal'
        ),
        array(
            'name' => esc_html__( 'Medium', 'light-fixture-child' ),
            'size' => 20,
            'slug' => 'medium'
        ),
        array(
            'name' => esc_html__( 'Large', 'light-fixture-child' ),
            'size' => 24,
            'slug' => 'large'
        ),
        array(
            'name' => esc_html__( 'Extra Large', 'light-fixture-child' ),
            'size' => 32,
            'slug' => 'extra-large'
        ),
        array(
            'name' => esc_html__( 'Huge', 'light-fixture-child' ),
            'size' => 48,
            'slug' => 'huge'
        ),
    ) );

    // 自定义缩略图尺寸
    // product-thumbnail: 用于产品列表卡片，裁剪为正方形
    add_image_size( 'product-thumbnail', 600, 600, true ); 
    // product-gallery: 用于产品详情页的主图或图集较大图
    add_image_size( 'product-gallery', 1200, 800, false ); 
    // hero-background: 用于页面英雄区背景图
    add_image_size( 'hero-background', 1920, 1080, true ); 
}
add_action( 'after_setup_theme', 'light_fixture_theme_setup_and_image_sizes' ); 