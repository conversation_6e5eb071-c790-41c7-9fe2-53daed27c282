/**
 * WebP Browser Detection JavaScript
 * WebP浏览器检测前端脚本
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

(function() {
    'use strict';
    
    /**
     * WebP检测类
     */
    class WebPDetection {
        constructor() {
            this.webpSupported = null;
            this.cookieName = webpDetection.cookieName || 'webp_support';
            this.cookieExpire = webpDetection.cookieExpire || 30;
            this.ajaxUrl = webpDetection.ajaxUrl;
            this.nonce = webpDetection.nonce;
            
            this.init();
        }
        
        /**
         * 初始化检测
         */
        init() {
            // 检查是否已有Cookie
            const existingCookie = this.getCookie(this.cookieName);
            
            if (existingCookie !== null) {
                // 已有检测结果，直接应用
                this.webpSupported = existingCookie === '1';
                this.applyWebPSupport();
                return;
            }
            
            // 执行WebP检测
            this.detectWebPSupport()
                .then(supported => {
                    this.webpSupported = supported;
                    this.setCookie(this.cookieName, supported ? '1' : '0', this.cookieExpire);
                    this.applyWebPSupport();
                    this.sendDetectionResult(supported);
                })
                .catch(error => {
                    console.warn('WebP检测失败:', error);
                    this.webpSupported = false;
                    this.applyWebPSupport();
                });
        }
        
        /**
         * 检测WebP支持
         * @returns {Promise<boolean>}
         */
        detectWebPSupport() {
            return new Promise((resolve) => {
                // 创建一个1x1像素的WebP图片进行测试
                const webpTestImage = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';
                
                const img = new Image();
                
                img.onload = function() {
                    // 如果图片加载成功且尺寸正确，说明支持WebP
                    resolve(img.width === 1 && img.height === 1);
                };
                
                img.onerror = function() {
                    // 图片加载失败，不支持WebP
                    resolve(false);
                };
                
                // 设置超时
                setTimeout(() => {
                    resolve(false);
                }, 1000);
                
                img.src = webpTestImage;
            });
        }
        
        /**
         * 应用WebP支持状态到页面
         */
        applyWebPSupport() {
            const body = document.body;
            
            if (this.webpSupported) {
                body.classList.add('webp-supported');
                body.classList.remove('webp-not-supported');
            } else {
                body.classList.add('webp-not-supported');
                body.classList.remove('webp-supported');
            }
            
            // 触发自定义事件
            const event = new CustomEvent('webpDetectionComplete', {
                detail: {
                    supported: this.webpSupported,
                    timestamp: Date.now()
                }
            });
            
            document.dispatchEvent(event);

            // 不再进行客户端图片替换，由服务端处理
            // 这避免了双重下载问题

            // 移除自动刷新逻辑，避免无限循环
            // 服务端已经可以正确处理WebP URL，无需刷新页面
        }
        
        /**
         * 更新页面中的WebP图片显示
         */
        updateWebPImages() {
            // 此函数已禁用，避免客户端图片替换导致的双重下载
            // 所有WebP处理现在由服务端完成

            // 仅用于调试：记录WebP支持状态
            if (window.console && window.console.log) {
                console.log('WebP Detection: 服务端处理模式，跳过客户端图片替换');
                console.log('WebP支持状态:', this.webpSupported);
            }

            // 不再进行任何图片URL替换操作
            return;
        }
        
        /**
         * 发送检测结果到服务器
         * @param {boolean} supported 
         */
        sendDetectionResult(supported) {
            if (!this.ajaxUrl || !this.nonce) {
                return;
            }
            
            const formData = new FormData();
            formData.append('action', 'webp_detection');
            formData.append('webp_supported', supported ? 'true' : 'false');
            formData.append('nonce', this.nonce);
            
            // 添加浏览器信息
            formData.append('user_agent', navigator.userAgent);
            formData.append('screen_width', screen.width);
            formData.append('screen_height', screen.height);
            formData.append('pixel_ratio', window.devicePixelRatio || 1);
            
            fetch(this.ajaxUrl, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('WebP检测结果已发送到服务器');
                } else {
                    console.warn('WebP检测结果发送失败:', data.data);
                }
            })
            .catch(error => {
                console.warn('WebP检测结果发送错误:', error);
            });
        }
        
        /**
         * 设置Cookie
         * @param {string} name 
         * @param {string} value 
         * @param {number} days 
         */
        setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;SameSite=Lax`;
        }
        
        /**
         * 获取Cookie
         * @param {string} name 
         * @returns {string|null}
         */
        getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) === ' ') {
                    c = c.substring(1, c.length);
                }
                if (c.indexOf(nameEQ) === 0) {
                    return c.substring(nameEQ.length, c.length);
                }
            }
            
            return null;
        }
        
        /**
         * 获取当前WebP支持状态
         * @returns {boolean|null}
         */
        isWebPSupported() {
            return this.webpSupported;
        }
        
        /**
         * 强制重新检测
         */
        forceRedetection() {
            // 删除现有Cookie
            this.setCookie(this.cookieName, '', -1);
            
            // 重新初始化
            this.webpSupported = null;
            this.init();
        }
    }
    
    /**
     * 工具函数：转换图片URL为WebP版本（仅用于检测，不替换）
     * @param {string} originalUrl
     * @returns {string}
     */
    function convertToWebPUrl(originalUrl) {
        if (!originalUrl) return originalUrl;
        return originalUrl.replace(/\.(jpe?g|png|gif)(\?.*)?$/i, '.webp$2');
    }

    // 注意：已移除enhanceImageWithWebP和enhanceAllImages函数
    // 这些函数会导致双重下载问题，现在完全依赖服务端处理图片URL替换
    
    // DOM加载完成后仅初始化检测器（不处理图片）
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            window.webpDetector = new WebPDetection();
        });
    } else {
        window.webpDetector = new WebPDetection();
    }

    // 注意：已移除MutationObserver图片监听逻辑
    // 图片处理现在完全由服务端负责，避免双重下载
    
    // 暴露全局接口（仅保留检测相关功能）
    window.LightFixtureWebP = {
        detector: null,
        convertToWebPUrl: convertToWebPUrl, // 仅用于URL转换，不进行图片替换

        // 初始化完成后设置detector引用
        init: function() {
            this.detector = window.webpDetector;
        },

        // 获取WebP支持状态
        isWebPSupported: function() {
            return this.detector ? this.detector.isWebPSupported() : false;
        },

        // 强制重新检测
        forceRedetection: function() {
            if (this.detector) {
                this.detector.forceRedetection();
            }
        }
    };

    // 页面加载完成后设置全局引用
    window.addEventListener('load', function() {
        window.LightFixtureWebP.init();
    });
    
})();
