# WebP WP-CLI 命令使用指南

## 概述

WebP WP-CLI命令提供了强大的命令行工具，用于批量处理WordPress媒体库中的图片，将其转换为WebP格式。这些命令特别适用于处理大量历史图片的转换工作。

## 安装要求

- WordPress 5.0+
- WP-CLI 2.0+
- PHP 7.4+
- GD扩展或Imagick扩展（支持WebP）

## 可用命令

### 1. 批量转换命令 (`wp webp convert`)

将历史图片批量转换为WebP格式。

#### 基本用法

```bash
# 转换所有历史图片（默认设置）
wp webp convert

# 指定批次大小和质量
wp webp convert --batch-size=20 --quality=90

# 强制重新转换已存在的WebP文件
wp webp convert --force

# 预览模式 - 查看将要转换的文件但不执行转换
wp webp convert --dry-run
```

#### 高级选项

```bash
# 只转换特定尺寸的图片
wp webp convert --include-sizes=thumbnail,medium,large

# 排除特定尺寸
wp webp convert --exclude-sizes=thumbnail

# 只处理特定MIME类型
wp webp convert --mime-types=image/jpeg,image/png

# 组合使用多个选项
wp webp convert --batch-size=10 --quality=80 --force --include-sizes=medium,large
```

#### 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `--batch-size` | 50 | 每批处理的图片数量 (1-1000) |
| `--quality` | 85 | WebP质量设置 (1-100) |
| `--force` | - | 强制重新转换已存在的WebP文件 |
| `--dry-run` | - | 预览模式，不执行实际转换 |
| `--include-sizes` | - | 包含的图片尺寸，用逗号分隔 |
| `--exclude-sizes` | - | 排除的图片尺寸，用逗号分隔 |
| `--mime-types` | image/jpeg,image/png,image/gif | 处理的MIME类型 |

### 2. 状态查看命令 (`wp webp status`)

查看WebP转换的统计信息。

#### 基本用法

```bash
# 显示转换统计（表格格式）
wp webp status

# JSON格式输出
wp webp status --format=json

# CSV格式输出
wp webp status --format=csv
```

#### 输出示例

```
+----------------+----------+
| 指标           | 数值     |
+----------------+----------+
| 总图片数       | 1,234    |
| 已转换数       | 856      |
| 转换率         | 69.37%   |
| 原始总大小     | 245.6 MB |
| WebP总大小     | 156.8 MB |
| 节省空间       | 88.8 MB  |
+----------------+----------+
```

### 3. 清理命令 (`wp webp cleanup`)

清理孤立的WebP文件（原始文件已删除但WebP文件仍存在）。

#### 基本用法

```bash
# 清理孤立的WebP文件
wp webp cleanup

# 预览将要清理的文件
wp webp cleanup --dry-run
```

### 4. 验证命令 (`wp webp verify`)

验证WebP文件的完整性并修复损坏的文件。

#### 基本用法

```bash
# 验证WebP文件完整性
wp webp verify

# 验证并自动修复损坏的文件
wp webp verify --fix
```

## 实际使用场景

### 场景1：首次部署WebP功能

```bash
# 1. 先预览将要转换的文件
wp webp convert --dry-run

# 2. 查看当前状态
wp webp status

# 3. 开始批量转换（小批次测试）
wp webp convert --batch-size=10

# 4. 检查转换结果
wp webp status

# 5. 继续转换剩余文件
wp webp convert --batch-size=50
```

### 场景2：大型网站批量转换

```bash
# 1. 设置较小的批次大小，避免服务器过载
wp webp convert --batch-size=20 --quality=85

# 2. 监控转换进度
wp webp status

# 3. 如果中断，继续转换（跳过已转换的文件）
wp webp convert --batch-size=20
```

### 场景3：质量优化

```bash
# 1. 重新转换所有文件，使用更高质量
wp webp convert --force --quality=90

# 2. 或者只转换特定尺寸
wp webp convert --force --quality=90 --include-sizes=large,full
```

### 场景4：维护和清理

```bash
# 1. 定期验证文件完整性
wp webp verify

# 2. 清理孤立文件
wp webp cleanup

# 3. 查看最新统计
wp webp status
```

## 性能建议

### 批次大小选择

- **小型网站** (< 1000张图片): `--batch-size=50-100`
- **中型网站** (1000-10000张图片): `--batch-size=20-50`
- **大型网站** (> 10000张图片): `--batch-size=10-20`

### 服务器资源考虑

- **共享主机**: 使用较小的批次大小 (10-20)
- **VPS/独立服务器**: 可以使用较大的批次大小 (50-100)
- **高性能服务器**: 可以使用最大批次大小 (100+)

### 质量设置建议

- **照片类图片**: 质量85-95
- **图标/简单图形**: 质量70-85
- **缩略图**: 质量70-80

## 错误处理

### 常见错误及解决方案

1. **"服务器不支持WebP转换"**
   ```bash
   # 检查服务器WebP支持
   php -m | grep -E "(gd|imagick)"
   ```

2. **"内存不足"**
   ```bash
   # 减小批次大小
   wp webp convert --batch-size=5
   ```

3. **"权限不足"**
   ```bash
   # 检查文件权限
   ls -la wp-content/uploads/
   ```

### 调试模式

```bash
# 启用调试输出
wp webp convert --debug

# 查看详细日志
tail -f wp-content/debug.log
```

## 自动化脚本示例

### 定时转换脚本

```bash
#!/bin/bash
# webp-convert.sh

echo "开始WebP批量转换..."

# 转换新图片
wp webp convert --batch-size=30

# 清理孤立文件
wp webp cleanup

# 验证文件完整性
wp webp verify --fix

# 显示最终统计
wp webp status

echo "WebP转换完成！"
```

### Cron任务设置

```bash
# 每天凌晨2点执行WebP转换
0 2 * * * /path/to/webp-convert.sh >> /var/log/webp-convert.log 2>&1
```

## 监控和报告

### 转换进度监控

```bash
# 实时监控转换状态
watch -n 30 'wp webp status'

# 生成转换报告
wp webp status --format=json > webp-report-$(date +%Y%m%d).json
```

### 日志分析

```bash
# 查看转换日志
grep "WebP" wp-content/debug.log | tail -100

# 统计转换成功率
grep "WebP.*转换成功" wp-content/debug.log | wc -l
```

## 最佳实践

1. **分阶段转换**: 先转换小批次测试，确认无误后再大批量转换
2. **备份重要数据**: 转换前备份媒体库文件
3. **监控服务器资源**: 转换过程中监控CPU和内存使用情况
4. **定期维护**: 定期运行清理和验证命令
5. **质量测试**: 转换后检查图片质量是否满足要求

## 故障排除

如果遇到问题，请按以下步骤排查：

1. 检查WP-CLI版本: `wp --version`
2. 检查WebP支持: `wp webp status`
3. 查看错误日志: `tail -f wp-content/debug.log`
4. 测试小批次转换: `wp webp convert --batch-size=1 --dry-run`
5. 联系技术支持并提供详细的错误信息

---

**注意**: 批量转换可能需要较长时间，建议在服务器负载较低时进行，并确保有足够的磁盘空间存储WebP文件。
