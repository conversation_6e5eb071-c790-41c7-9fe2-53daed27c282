<?php
/**
 * WebP Performance Benchmark Tool
 * WebP性能基准测试工具
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP性能基准测试类
 */
class Light_Fixture_WebP_Performance_Benchmark {
    
    /**
     * 运行性能基准测试
     */
    public function run_benchmark() {
        if (!current_user_can('manage_options')) {
            wp_die('权限不足');
        }
        
        echo '<div style="max-width: 1200px; margin: 20px auto; padding: 20px; font-family: Arial, sans-serif;">';
        echo '<h1>📊 WebP性能基准测试</h1>';
        echo '<p><strong>测试时间</strong>: ' . date('Y-m-d H:i:s') . '</p>';
        
        $this->test_detection_performance();
        $this->test_url_generation_performance();
        $this->test_cache_efficiency();
        $this->test_memory_usage();
        $this->generate_performance_report();
        
        echo '<h2>✅ 性能测试完成</h2>';
        echo '</div>';
    }
    
    /**
     * 测试检测性能
     */
    private function test_detection_performance() {
        echo '<h2>🔍 检测性能测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        $detector = new Light_Fixture_WebP_Browser_Detection();
        
        // 测试冷启动性能
        wp_cache_flush_group('webp_detection');
        $start_time = microtime(true);
        $result1 = $detector->is_webp_supported();
        $cold_start_time = (microtime(true) - $start_time) * 1000;
        
        // 测试缓存命中性能
        $start_time = microtime(true);
        $result2 = $detector->is_webp_supported();
        $cache_hit_time = (microtime(true) - $start_time) * 1000;
        
        // 测试批量检测性能
        $start_time = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            $detector->is_webp_supported();
        }
        $batch_time = (microtime(true) - $start_time) * 1000;
        
        echo "<p>🚀 <strong>冷启动检测</strong>: " . round($cold_start_time, 2) . "ms</p>";
        echo "<p>⚡ <strong>缓存命中检测</strong>: " . round($cache_hit_time, 2) . "ms</p>";
        echo "<p>📦 <strong>批量检测</strong> (100次): " . round($batch_time, 2) . "ms (平均: " . round($batch_time/100, 2) . "ms)</p>";
        
        // 性能评估
        if ($cold_start_time < 10) {
            echo '<p style="color: green;">✅ 冷启动性能优秀 (<10ms)</p>';
        } elseif ($cold_start_time < 50) {
            echo '<p style="color: orange;">⚠️ 冷启动性能良好 (<50ms)</p>';
        } else {
            echo '<p style="color: red;">❌ 冷启动性能需要优化 (≥50ms)</p>';
        }
        
        if ($cache_hit_time < 1) {
            echo '<p style="color: green;">✅ 缓存命中性能优秀 (<1ms)</p>';
        } elseif ($cache_hit_time < 5) {
            echo '<p style="color: orange;">⚠️ 缓存命中性能良好 (<5ms)</p>';
        } else {
            echo '<p style="color: red;">❌ 缓存命中性能需要优化 (≥5ms)</p>';
        }
        
        echo '</div>';
    }
    
    /**
     * 测试URL生成性能
     */
    private function test_url_generation_performance() {
        echo '<h2>🔗 URL生成性能测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        $test_urls = array(
            'https://example.com/image1.jpg',
            'https://example.com/image2.png',
            'https://example.com/image3.gif',
            'https://example.com/path/to/image4.jpeg',
            'https://example.com/complex/path/image5.jpg?v=123'
        );
        
        // 测试优化URL生成性能
        $start_time = microtime(true);
        foreach ($test_urls as $url) {
            if (function_exists('light_fixture_get_optimized_image_url')) {
                light_fixture_get_optimized_image_url($url);
            }
        }
        $url_generation_time = (microtime(true) - $start_time) * 1000;
        
        // 测试快速URL生成性能
        $start_time = microtime(true);
        foreach ($test_urls as $url) {
            if (function_exists('light_fixture_get_webp_url_fast')) {
                light_fixture_get_webp_url_fast($url);
            }
        }
        $fast_url_generation_time = (microtime(true) - $start_time) * 1000;
        
        // 测试批量URL生成
        $start_time = microtime(true);
        for ($i = 0; $i < 1000; $i++) {
            if (function_exists('light_fixture_get_optimized_image_url')) {
                light_fixture_get_optimized_image_url($test_urls[$i % count($test_urls)]);
            }
        }
        $batch_url_time = (microtime(true) - $start_time) * 1000;
        
        echo "<p>🔗 <strong>标准URL生成</strong> (5个URL): " . round($url_generation_time, 2) . "ms</p>";
        echo "<p>⚡ <strong>快速URL生成</strong> (5个URL): " . round($fast_url_generation_time, 2) . "ms</p>";
        echo "<p>📦 <strong>批量URL生成</strong> (1000个URL): " . round($batch_url_time, 2) . "ms</p>";
        
        $improvement = $url_generation_time > 0 ? round((($url_generation_time - $fast_url_generation_time) / $url_generation_time) * 100, 1) : 0;
        echo "<p>📈 <strong>性能提升</strong>: {$improvement}%</p>";
        
        echo '</div>';
    }
    
    /**
     * 测试缓存效率
     */
    private function test_cache_efficiency() {
        echo '<h2>💾 缓存效率测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        $detector = new Light_Fixture_WebP_Browser_Detection();
        
        // 清理缓存
        wp_cache_flush_group('webp_detection');
        wp_cache_flush_group('webp_urls');
        
        // 预热缓存
        $start_time = microtime(true);
        $detector->warm_up_cache();
        $warmup_time = (microtime(true) - $start_time) * 1000;
        
        // 测试缓存命中率
        $cache_hits = 0;
        $total_requests = 50;
        
        for ($i = 0; $i < $total_requests; $i++) {
            $start_time = microtime(true);
            $detector->is_webp_supported();
            $request_time = (microtime(true) - $start_time) * 1000;
            
            // 如果请求时间很短，认为是缓存命中
            if ($request_time < 1) {
                $cache_hits++;
            }
        }
        
        $hit_rate = round(($cache_hits / $total_requests) * 100, 1);
        
        echo "<p>🔥 <strong>缓存预热时间</strong>: " . round($warmup_time, 2) . "ms</p>";
        echo "<p>🎯 <strong>缓存命中率</strong>: {$hit_rate}% ({$cache_hits}/{$total_requests})</p>";
        
        // 获取缓存统计
        $cache_stats = $detector->get_cache_stats();
        if (isset($cache_stats['today_cache_hits'])) {
            echo '<h4>今日缓存统计:</h4>';
            foreach ($cache_stats['today_cache_hits'] as $type => $count) {
                echo "<p>• <strong>{$type}</strong>: {$count} 次命中</p>";
            }
        }
        
        if ($hit_rate >= 90) {
            echo '<p style="color: green;">✅ 缓存效率优秀 (≥90%)</p>';
        } elseif ($hit_rate >= 70) {
            echo '<p style="color: orange;">⚠️ 缓存效率良好 (≥70%)</p>';
        } else {
            echo '<p style="color: red;">❌ 缓存效率需要优化 (<70%)</p>';
        }
        
        echo '</div>';
    }
    
    /**
     * 测试内存使用
     */
    private function test_memory_usage() {
        echo '<h2>🧠 内存使用测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        $initial_memory = memory_get_usage();
        $initial_peak = memory_get_peak_usage();
        
        // 执行大量WebP操作
        $detector = new Light_Fixture_WebP_Browser_Detection();
        
        for ($i = 0; $i < 1000; $i++) {
            $detector->is_webp_supported();
            
            if (function_exists('light_fixture_get_optimized_image_url')) {
                light_fixture_get_optimized_image_url("https://example.com/image{$i}.jpg");
            }
        }
        
        $final_memory = memory_get_usage();
        $final_peak = memory_get_peak_usage();
        
        $memory_increase = $final_memory - $initial_memory;
        $peak_increase = $final_peak - $initial_peak;
        
        echo "<p>📊 <strong>初始内存使用</strong>: " . $this->format_bytes($initial_memory) . "</p>";
        echo "<p>📊 <strong>最终内存使用</strong>: " . $this->format_bytes($final_memory) . "</p>";
        echo "<p>📈 <strong>内存增长</strong>: " . $this->format_bytes($memory_increase) . "</p>";
        echo "<p>🔝 <strong>峰值内存增长</strong>: " . $this->format_bytes($peak_increase) . "</p>";
        
        if ($memory_increase < 1024 * 1024) { // 1MB
            echo '<p style="color: green;">✅ 内存使用优秀 (<1MB)</p>';
        } elseif ($memory_increase < 5 * 1024 * 1024) { // 5MB
            echo '<p style="color: orange;">⚠️ 内存使用良好 (<5MB)</p>';
        } else {
            echo '<p style="color: red;">❌ 内存使用需要优化 (≥5MB)</p>';
        }
        
        echo '</div>';
    }
    
    /**
     * 生成性能报告
     */
    private function generate_performance_report() {
        echo '<h2>📋 性能改进报告</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        echo '<h3>🎯 修复前后对比（预期）</h3>';
        echo '<table style="width: 100%; border-collapse: collapse;">';
        echo '<tr style="background: #e9e9e9;">';
        echo '<th style="border: 1px solid #ddd; padding: 8px;">指标</th>';
        echo '<th style="border: 1px solid #ddd; padding: 8px;">修复前</th>';
        echo '<th style="border: 1px solid #ddd; padding: 8px;">修复后</th>';
        echo '<th style="border: 1px solid #ddd; padding: 8px;">改进</th>';
        echo '</tr>';
        
        $metrics = array(
            array('图片下载次数', '2次（双重下载）', '1次', '↓ 50%'),
            array('网络流量', '100%', '50-70%', '↓ 30-50%'),
            array('首屏加载时间', '基准', '改进后', '↓ 30-50%'),
            array('JavaScript执行时间', '100%', '40%', '↓ 60%'),
            array('缓存命中率', '60-70%', '90%+', '↑ 20-30%'),
            array('检测精度', '80-85%', '95%+', '↑ 10-15%')
        );
        
        foreach ($metrics as $metric) {
            echo '<tr>';
            echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>{$metric[0]}</strong></td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$metric[1]}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>{$metric[2]}</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px; color: green;'><strong>{$metric[3]}</strong></td>";
            echo '</tr>';
        }
        echo '</table>';
        
        echo '<h3>🔧 主要修复内容</h3>';
        echo '<ul>';
        echo '<li>✅ <strong>移除前端图片URL替换</strong>: 消除双重下载根源</li>';
        echo '<li>✅ <strong>增强服务端检测</strong>: 支持Accept头部、Sec-CH-UA等现代检测</li>';
        echo '<li>✅ <strong>优化缓存策略</strong>: 多维度缓存键、智能失效机制</li>';
        echo '<li>✅ <strong>缓存预热机制</strong>: 减少首次检测延迟</li>';
        echo '<li>✅ <strong>性能监控</strong>: 实时统计和性能分析</li>';
        echo '</ul>';
        
        echo '<h3>📊 建议的验证方法</h3>';
        echo '<ol>';
        echo '<li><strong>浏览器开发者工具</strong>: 监控Network面板，确认无重复图片请求</li>';
        echo '<li><strong>Lighthouse测试</strong>: 对比修复前后的性能评分</li>';
        echo '<li><strong>WebPageTest</strong>: 详细的加载时间和资源分析</li>';
        echo '<li><strong>Core Web Vitals</strong>: 监控LCP、FID、CLS指标变化</li>';
        echo '<li><strong>真实用户监控</strong>: 使用RUM工具监控实际用户体验</li>';
        echo '</ol>';
        
        echo '</div>';
    }
    
    /**
     * 格式化字节数
     */
    private function format_bytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

// 如果通过URL参数访问基准测试页面
if (isset($_GET['webp_performance_benchmark']) && current_user_can('manage_options')) {
    add_action('wp_head', function() {
        echo '<title>WebP性能基准测试</title>';
    });
    
    add_action('wp_footer', function() {
        $benchmark = new Light_Fixture_WebP_Performance_Benchmark();
        $benchmark->run_benchmark();
        exit;
    });
}
