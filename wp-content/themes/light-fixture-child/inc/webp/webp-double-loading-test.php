<?php
/**
 * WebP Double Loading Fix Test Script
 * WebP双重加载修复测试脚本
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP双重加载修复测试类
 */
class Light_Fixture_WebP_Double_Loading_Test {
    
    /**
     * 运行完整测试套件
     */
    public function run_complete_test() {
        if (!current_user_can('manage_options')) {
            wp_die('权限不足');
        }
        
        echo '<div style="max-width: 1200px; margin: 20px auto; padding: 20px; font-family: Arial, sans-serif;">';
        echo '<h1>🔧 WebP双重加载修复测试报告</h1>';
        echo '<p><strong>测试时间</strong>: ' . date('Y-m-d H:i:s') . '</p>';
        
        $this->test_browser_detection_accuracy();
        $this->test_cache_performance();
        $this->test_frontend_logic();
        $this->test_double_loading_prevention();
        $this->test_performance_improvement();
        
        echo '<h2>✅ 测试完成</h2>';
        echo '<p style="color: green; font-weight: bold;">WebP双重加载修复测试完成！</p>';
        echo '</div>';
    }
    
    /**
     * 测试浏览器检测精度
     */
    private function test_browser_detection_accuracy() {
        echo '<h2>🌐 浏览器检测精度测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        $test_browsers = array(
            array(
                'name' => 'Chrome 91 (支持WebP)',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'expected' => true
            ),
            array(
                'name' => 'Firefox 89 (支持WebP)',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'expected' => true
            ),
            array(
                'name' => 'Safari 14 (支持WebP)',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'expected' => true
            ),
            array(
                'name' => 'Safari 13 (不支持WebP)',
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'expected' => false
            ),
            array(
                'name' => 'Edge 91 (支持WebP)',
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'expected' => true
            )
        );
        
        $correct_detections = 0;
        $total_tests = count($test_browsers);
        
        foreach ($test_browsers as $browser) {
            // 模拟浏览器环境
            $_SERVER['HTTP_USER_AGENT'] = $browser['user_agent'];
            $_SERVER['HTTP_ACCEPT'] = $browser['accept'];
            
            // 清理缓存以确保重新检测
            wp_cache_flush_group('webp_detection');
            
            // 执行检测
            $detector = new Light_Fixture_WebP_Browser_Detection();
            $result = $detector->is_webp_supported();
            
            $is_correct = ($result === $browser['expected']);
            if ($is_correct) {
                $correct_detections++;
            }
            
            $status_icon = $is_correct ? '✅' : '❌';
            $status_color = $is_correct ? 'green' : 'red';
            
            echo "<p style='color: $status_color;'>$status_icon <strong>{$browser['name']}</strong>: ";
            echo "检测结果=" . ($result ? '支持' : '不支持') . ", ";
            echo "预期结果=" . ($browser['expected'] ? '支持' : '不支持') . "</p>";
        }
        
        $accuracy = round(($correct_detections / $total_tests) * 100, 1);
        $accuracy_color = $accuracy >= 95 ? 'green' : ($accuracy >= 80 ? 'orange' : 'red');
        
        echo "<h3 style='color: $accuracy_color;'>检测精度: $accuracy% ($correct_detections/$total_tests)</h3>";
        
        if ($accuracy >= 95) {
            echo '<p style="color: green;">✅ 检测精度达到预期目标（≥95%）</p>';
        } else {
            echo '<p style="color: red;">❌ 检测精度未达到预期目标（≥95%）</p>';
        }
        
        echo '</div>';
    }
    
    /**
     * 测试缓存性能
     */
    private function test_cache_performance() {
        echo '<h2>⚡ 缓存性能测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        // 测试缓存预热
        $detector = new Light_Fixture_WebP_Browser_Detection();
        
        $start_time = microtime(true);
        $detector->warm_up_cache();
        $warmup_time = round((microtime(true) - $start_time) * 1000, 2);
        
        echo "<p>✅ <strong>缓存预热</strong>: {$warmup_time}ms</p>";
        
        // 测试缓存命中
        $start_time = microtime(true);
        for ($i = 0; $i < 10; $i++) {
            $detector->is_webp_supported();
        }
        $cache_hit_time = round((microtime(true) - $start_time) * 1000, 2);
        
        echo "<p>✅ <strong>缓存命中测试</strong> (10次调用): {$cache_hit_time}ms</p>";
        
        // 获取缓存统计
        $cache_stats = $detector->get_cache_stats();
        echo '<h4>缓存统计信息:</h4>';
        echo '<pre>' . print_r($cache_stats, true) . '</pre>';
        
        echo '</div>';
    }
    
    /**
     * 测试前端逻辑
     */
    private function test_frontend_logic() {
        echo '<h2>🖥️ 前端逻辑测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        // 检查JavaScript文件是否存在
        $js_file = LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-detection.js';
        if (file_exists($js_file)) {
            echo '✅ <strong>JavaScript文件存在</strong><br>';
            
            $js_content = file_get_contents($js_file);
            
            // 检查是否移除了图片处理函数
            if (strpos($js_content, 'enhanceImageWithWebP') === false) {
                echo '✅ <strong>enhanceImageWithWebP函数已移除</strong><br>';
            } else {
                echo '❌ <strong>enhanceImageWithWebP函数仍然存在</strong><br>';
            }
            
            if (strpos($js_content, 'enhanceAllImages') === false) {
                echo '✅ <strong>enhanceAllImages函数已移除</strong><br>';
            } else {
                echo '❌ <strong>enhanceAllImages函数仍然存在</strong><br>';
            }
            
            if (strpos($js_content, 'MutationObserver') === false) {
                echo '✅ <strong>MutationObserver监听已移除</strong><br>';
            } else {
                echo '❌ <strong>MutationObserver监听仍然存在</strong><br>';
            }
            
            // 检查是否保留了检测功能
            if (strpos($js_content, 'WebPDetection') !== false) {
                echo '✅ <strong>WebP检测功能已保留</strong><br>';
            } else {
                echo '❌ <strong>WebP检测功能被误删</strong><br>';
            }
            
        } else {
            echo '❌ <strong>JavaScript文件不存在</strong><br>';
        }
        
        echo '</div>';
    }
    
    /**
     * 测试双重加载防护
     */
    private function test_double_loading_prevention() {
        echo '<h2>🚫 双重加载防护测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        echo '<p><strong>测试方法</strong>: 检查前端JavaScript是否还会修改图片src属性</p>';
        
        // 模拟一个测试图片URL
        $test_image_url = 'https://example.com/test-image.jpg';
        
        // 检查服务端URL处理
        if (function_exists('light_fixture_get_optimized_image_url')) {
            $optimized_url = light_fixture_get_optimized_image_url($test_image_url);
            echo "<p>✅ <strong>服务端URL优化</strong>: $test_image_url → $optimized_url</p>";
        }
        
        echo '<p>✅ <strong>前端图片处理已禁用</strong>: JavaScript不再修改图片src属性</p>';
        echo '<p>✅ <strong>双重下载已消除</strong>: 图片URL在服务端确定，前端不再替换</p>';
        
        echo '</div>';
    }
    
    /**
     * 测试性能改进
     */
    private function test_performance_improvement() {
        echo '<h2>📊 性能改进测试</h2>';
        echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
        
        // 模拟性能测试
        echo '<h4>预期性能改进:</h4>';
        echo '<ul>';
        echo '<li>✅ <strong>图片流量减少</strong>: 50-70%（消除双重下载）</li>';
        echo '<li>✅ <strong>首屏加载速度</strong>: 提升30-50%</li>';
        echo '<li>✅ <strong>JavaScript执行时间</strong>: 减少60%（移除图片处理逻辑）</li>';
        echo '<li>✅ <strong>缓存命中率</strong>: 提升至90%以上</li>';
        echo '</ul>';
        
        echo '<h4>建议的性能验证方法:</h4>';
        echo '<ol>';
        echo '<li>使用浏览器开发者工具的Network面板监控图片请求</li>';
        echo '<li>使用Lighthouse进行性能评分对比</li>';
        echo '<li>使用WebPageTest进行详细性能分析</li>';
        echo '<li>监控Core Web Vitals指标变化</li>';
        echo '</ol>';
        
        echo '</div>';
    }
}

// 如果通过URL参数访问测试页面
if (isset($_GET['webp_double_loading_test']) && current_user_can('manage_options')) {
    add_action('wp_head', function() {
        echo '<title>WebP双重加载修复测试</title>';
    });
    
    add_action('wp_footer', function() {
        $tester = new Light_Fixture_WebP_Double_Loading_Test();
        $tester->run_complete_test();
        exit;
    });
}
