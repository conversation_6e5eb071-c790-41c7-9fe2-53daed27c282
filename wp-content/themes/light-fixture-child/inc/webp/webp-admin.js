/**
 * WebP Admin JavaScript
 * WebP管理界面JavaScript支持
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * WebP管理功能类
     */
    class WebPAdmin {
        constructor() {
            this.init();
        }
        
        /**
         * 初始化
         */
        init() {
            this.bindEvents();
            this.initMediaLibraryEnhancements();
            this.initAttachmentDetailsEnhancements();
        }
        
        /**
         * 绑定事件
         */
        bindEvents() {
            // 媒体库WebP转换按钮
            $(document).on('click', '.convert-to-webp', this.convertSingleImage.bind(this));
            $(document).on('click', '.regenerate-webp', this.regenerateSingleImage.bind(this));
            
            // 批量操作
            $(document).on('click', '.bulk-convert-webp', this.bulkConvertImages.bind(this));
            
            // 媒体库列表增强
            this.enhanceMediaLibraryList();
        }
        
        /**
         * 转换单个图片
         */
        convertSingleImage(event) {
            event.preventDefault();
            
            const button = $(event.target);
            const attachmentId = button.data('attachment-id');
            
            if (!attachmentId) {
                this.showNotice('错误：无法获取附件ID', 'error');
                return;
            }
            
            button.prop('disabled', true).text('转换中...');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'regenerate_webp',
                    attachment_id: attachmentId,
                    nonce: webpAdmin.regenerateNonce
                },
                success: (response) => {
                    if (response.success) {
                        this.showNotice('WebP转换成功', 'success');
                        this.updateWebPStatus(attachmentId, true);
                        button.removeClass('convert-to-webp').addClass('regenerate-webp')
                              .text('重新生成').prop('disabled', false);
                    } else {
                        this.showNotice('转换失败: ' + response.data, 'error');
                        button.prop('disabled', false).text('转换为WebP');
                    }
                },
                error: () => {
                    this.showNotice('请求失败，请重试', 'error');
                    button.prop('disabled', false).text('转换为WebP');
                }
            });
        }
        
        /**
         * 重新生成单个图片WebP
         */
        regenerateSingleImage(event) {
            event.preventDefault();
            
            const button = $(event.target);
            const attachmentId = button.data('attachment-id');
            
            if (!confirm('确定要重新生成WebP文件吗？这将覆盖现有的WebP文件。')) {
                return;
            }
            
            this.convertSingleImage(event);
        }
        
        /**
         * 批量转换图片
         */
        bulkConvertImages(event) {
            event.preventDefault();
            
            const button = $(event.target);
            const batchSize = parseInt(button.data('batch-size')) || 10;
            
            if (!confirm(`确定要批量转换图片为WebP格式吗？\n每批处理 ${batchSize} 张图片。`)) {
                return;
            }
            
            button.prop('disabled', true);
            this.startBulkConversion(0, batchSize, button);
        }
        
        /**
         * 开始批量转换
         */
        startBulkConversion(offset, batchSize, button) {
            const progressBar = this.createProgressBar();
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'bulk_convert_webp',
                    batch_size: batchSize,
                    offset: offset,
                    nonce: webpAdmin.bulkConvertNonce
                },
                success: (response) => {
                    if (response.success) {
                        const data = response.data;
                        const newOffset = offset + batchSize;
                        
                        this.updateProgressBar(progressBar, newOffset, data.converted);
                        
                        if (data.errors.length > 0) {
                            console.warn('转换错误:', data.errors);
                        }
                        
                        if (data.has_more) {
                            // 继续下一批
                            setTimeout(() => {
                                this.startBulkConversion(newOffset, batchSize, button);
                            }, 1000);
                        } else {
                            // 转换完成
                            this.completeBulkConversion(button, progressBar);
                        }
                    } else {
                        this.showNotice('批量转换失败: ' + response.data, 'error');
                        button.prop('disabled', false);
                        progressBar.remove();
                    }
                },
                error: () => {
                    this.showNotice('批量转换请求失败', 'error');
                    button.prop('disabled', false);
                    progressBar.remove();
                }
            });
        }
        
        /**
         * 创建进度条
         */
        createProgressBar() {
            const progressHtml = `
                <div id="webp-bulk-progress" style="margin: 20px 0; padding: 15px; background: #f9f9f9; border-radius: 5px;">
                    <h4>批量转换进度</h4>
                    <div style="background: #ddd; height: 20px; border-radius: 10px; overflow: hidden;">
                        <div id="webp-progress-bar" style="background: #0073aa; height: 100%; width: 0%; transition: width 0.3s;"></div>
                    </div>
                    <p id="webp-progress-text">准备开始...</p>
                </div>
            `;
            
            const progressBar = $(progressHtml);
            $('.wrap').prepend(progressBar);
            
            return progressBar;
        }
        
        /**
         * 更新进度条
         */
        updateProgressBar(progressBar, processed, converted) {
            const progressPercent = Math.min(100, (processed / 100) * 100); // 假设总数为100，实际应该动态计算
            
            progressBar.find('#webp-progress-bar').css('width', progressPercent + '%');
            progressBar.find('#webp-progress-text').text(`已处理: ${processed} 张，已转换: ${converted} 张`);
        }
        
        /**
         * 完成批量转换
         */
        completeBulkConversion(button, progressBar) {
            progressBar.find('#webp-progress-text').text('批量转换完成！');
            
            setTimeout(() => {
                progressBar.fadeOut(() => {
                    progressBar.remove();
                });
            }, 3000);
            
            button.prop('disabled', false);
            this.showNotice('批量WebP转换完成', 'success');
            
            // 刷新页面以显示最新状态
            setTimeout(() => {
                location.reload();
            }, 2000);
        }
        
        /**
         * 显示通知
         */
        showNotice(message, type = 'info') {
            const noticeClass = type === 'error' ? 'notice-error' : 
                               type === 'success' ? 'notice-success' : 'notice-info';
            
            const notice = $(`
                <div class="notice ${noticeClass} is-dismissible">
                    <p>${message}</p>
                    <button type="button" class="notice-dismiss">
                        <span class="screen-reader-text">忽略此通知。</span>
                    </button>
                </div>
            `);
            
            $('.wrap h1').after(notice);
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    notice.fadeOut();
                }, 5000);
            }
            
            // 绑定关闭按钮
            notice.find('.notice-dismiss').on('click', function() {
                notice.fadeOut();
            });
        }
        
        /**
         * 更新WebP状态显示
         */
        updateWebPStatus(attachmentId, hasWebP) {
            // 更新媒体库列表中的状态
            const statusCell = $(`.wp-list-table tr[id="post-${attachmentId}"] .column-webp_status`);
            if (statusCell.length) {
                statusCell.html(hasWebP ? '<span style="color: green;">✅</span>' : '<span style="color: red;">❌</span>');
            }
            
            // 更新附件详情中的状态
            const webpFields = $('.webp-status-field');
            if (webpFields.length) {
                // 这里可以更新附件详情页面的WebP状态显示
            }
        }
        
        /**
         * 增强媒体库列表
         */
        enhanceMediaLibraryList() {
            // 为媒体库列表添加快速操作按钮
            $('.wp-list-table tbody tr').each(function() {
                const row = $(this);
                const attachmentId = row.attr('id')?.replace('post-', '');
                
                if (!attachmentId) return;
                
                const webpStatus = row.find('.column-webp_status span');
                const actionsCell = row.find('.row-actions');
                
                if (webpStatus.length && actionsCell.length) {
                    const hasWebP = webpStatus.text().includes('✅');
                    const actionText = hasWebP ? '重新生成WebP' : '转换为WebP';
                    const actionClass = hasWebP ? 'regenerate-webp' : 'convert-to-webp';
                    
                    const webpAction = `<span class="webp-action"> | <a href="#" class="${actionClass}" data-attachment-id="${attachmentId}">${actionText}</a></span>`;
                    actionsCell.append(webpAction);
                }
            });
        }
        
        /**
         * 初始化媒体库增强功能
         */
        initMediaLibraryEnhancements() {
            // 添加批量操作选项
            if ($('.bulkactions select').length) {
                $('.bulkactions select').append('<option value="convert_to_webp">转换为WebP</option>');
            }
            
            // 处理批量操作
            $(document).on('click', '#doaction, #doaction2', (event) => {
                const action = $(event.target).siblings('select').val();
                
                if (action === 'convert_to_webp') {
                    event.preventDefault();
                    
                    const checkedItems = $('input[name="media[]"]:checked');
                    if (checkedItems.length === 0) {
                        alert('请选择要转换的图片');
                        return;
                    }
                    
                    if (confirm(`确定要将选中的 ${checkedItems.length} 张图片转换为WebP格式吗？`)) {
                        this.bulkConvertSelectedImages(checkedItems);
                    }
                }
            });
        }
        
        /**
         * 批量转换选中的图片
         */
        bulkConvertSelectedImages(checkedItems) {
            const attachmentIds = [];
            checkedItems.each(function() {
                attachmentIds.push($(this).val());
            });
            
            let completed = 0;
            const total = attachmentIds.length;
            const progressBar = this.createProgressBar();
            
            const convertNext = (index) => {
                if (index >= total) {
                    this.completeBulkConversion($('.bulk-convert-webp'), progressBar);
                    return;
                }
                
                const attachmentId = attachmentIds[index];
                
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'regenerate_webp',
                        attachment_id: attachmentId,
                        nonce: webpAdmin.regenerateNonce
                    },
                    success: (response) => {
                        completed++;
                        this.updateProgressBar(progressBar, completed, completed);
                        
                        if (response.success) {
                            this.updateWebPStatus(attachmentId, true);
                        }
                        
                        // 继续下一个
                        setTimeout(() => convertNext(index + 1), 500);
                    },
                    error: () => {
                        completed++;
                        setTimeout(() => convertNext(index + 1), 500);
                    }
                });
            };
            
            convertNext(0);
        }
        
        /**
         * 初始化附件详情增强功能
         */
        initAttachmentDetailsEnhancements() {
            // 为附件编辑页面添加WebP信息显示
            if ($('.attachment-details').length || $('.edit-attachment-frame').length) {
                this.enhanceAttachmentDetails();
            }
        }
        
        /**
         * 增强附件详情显示
         */
        enhanceAttachmentDetails() {
            // 这里可以添加更多的附件详情页面增强功能
            // 比如显示WebP文件大小对比、转换历史等
        }
    }
    
    // 全局函数，供HTML中的按钮调用
    window.convertToWebP = function(attachmentId) {
        const webpAdmin = new WebPAdmin();
        webpAdmin.convertSingleImage({
            preventDefault: () => {},
            target: $('<button>').data('attachment-id', attachmentId)[0]
        });
    };
    
    window.regenerateWebP = function(attachmentId) {
        const webpAdmin = new WebPAdmin();
        webpAdmin.regenerateSingleImage({
            preventDefault: () => {},
            target: $('<button>').data('attachment-id', attachmentId)[0]
        });
    };
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        // 只在管理后台的媒体相关页面初始化
        if ($('body').hasClass('wp-admin') && 
            ($('body').hasClass('upload-php') || $('body').hasClass('post-php') || $('body').hasClass('media-php'))) {
            new WebPAdmin();
        }
    });
    
})(jQuery);
