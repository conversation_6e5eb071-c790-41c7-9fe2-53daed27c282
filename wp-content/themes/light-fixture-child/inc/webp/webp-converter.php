<?php
/**
 * WebP Converter Core Engine
 * WebP转换核心引擎
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP转换器核心类
 */
class Light_Fixture_WebP_Converter {

    /**
     * 默认配置
     */
    private $config = array(
        'quality' => 85,                    // WebP质量 (1-100)
        'memory_limit' => '256M',           // 内存限制
        'timeout' => 300,                   // 超时时间(秒)
        'enable_logging' => true,           // 启用日志
        'supported_formats' => array('jpeg', 'jpg', 'png', 'gif'),
        'max_file_size' => 10485760,        // 最大文件大小 10MB
    );

    /**
     * 支持的图片编辑器
     */
    private $preferred_editor = null;

    /**
     * 自定义图片尺寸配置
     */
    private $custom_sizes = array(
        'product-thumbnail' => array(
            'width' => 600,
            'height' => 600,
            'crop' => true,
            'description' => '产品缩略图'
        ),
        'product-gallery' => array(
            'width' => 1200,
            'height' => 800,
            'crop' => false,
            'description' => '产品画廊图'
        ),
        'hero-background' => array(
            'width' => 1920,
            'height' => 1080,
            'crop' => true,
            'description' => '首页背景图'
        )
    );

    /**
     * 对象缓存，用于存储WebP文件存在性检查结果
     */
    private $webp_cache = array();
    
    /**
     * 构造函数
     */
    public function __construct($config = array()) {
        $this->config = array_merge($this->config, $config);
        $this->detect_image_editor();
        $this->init_hooks();
    }
    
    /**
     * 检测可用的图片编辑器
     */
    private function detect_image_editor() {
        // 检查Imagick支持
        if (extension_loaded('imagick') && class_exists('Imagick')) {
            $imagick = new Imagick();
            $formats = $imagick->queryFormats('WEBP');
            if (!empty($formats)) {
                $this->preferred_editor = 'imagick';
                return;
            }
        }
        
        // 检查GD支持
        if (extension_loaded('gd') && function_exists('imagewebp')) {
            $gd_info = gd_info();
            if (isset($gd_info['WebP Support']) && $gd_info['WebP Support']) {
                $this->preferred_editor = 'gd';
                return;
            }
        }
        
        $this->preferred_editor = null;
        $this->log_message('警告: 服务器不支持WebP转换', 'warning');
    }
    
    /**
     * 初始化WordPress钩子
     */
    private function init_hooks() {
        // 新图片上传时自动转换
        add_filter('wp_generate_attachment_metadata', array($this, 'auto_convert_on_upload'), 10, 2);

        // 删除附件时清理WebP文件
        add_action('delete_attachment', array($this, 'cleanup_webp_on_delete'));

        // 自定义尺寸相关钩子
        add_filter('wp_generate_attachment_metadata', array($this, 'convert_custom_sizes_to_webp'), 25, 2);
        add_filter('wp_get_attachment_image_src', array($this, 'filter_custom_size_src'), 15, 4);
        add_filter('wp_get_attachment_image_srcset', array($this, 'enhance_custom_srcset'), 15, 4);
        add_filter('wp_get_attachment_image', array($this, 'enhance_custom_image_html'), 15, 5);
        add_filter('image_size_names_choose', array($this, 'add_custom_sizes_to_media_library'));

        // 主题特定的图片输出钩子
        add_filter('light_fixture_get_product_image', array($this, 'get_optimized_product_image'), 10, 3);
        add_filter('light_fixture_get_hero_image', array($this, 'get_optimized_hero_image'), 10, 3);
    }
    
    /**
     * 检查是否支持WebP转换
     */
    public function is_webp_supported() {
        return !is_null($this->preferred_editor);
    }
    
    /**
     * 获取推荐的图片编辑器
     */
    public function get_preferred_editor() {
        return $this->preferred_editor;
    }
    
    /**
     * 转换单个图片文件为WebP
     * 
     * @param string $source_path 源文件路径
     * @param string $target_path 目标WebP文件路径
     * @param int $quality 质量设置 (1-100)
     * @return bool|WP_Error 成功返回true，失败返回WP_Error
     */
    public function convert_to_webp($source_path, $target_path = null, $quality = null) {
        // 参数验证
        if (!file_exists($source_path)) {
            return new WP_Error('file_not_found', '源文件不存在: ' . $source_path);
        }
        
        if (!$this->is_webp_supported()) {
            return new WP_Error('webp_not_supported', '服务器不支持WebP转换');
        }
        
        // 检查文件大小
        $file_size = filesize($source_path);
        if ($file_size > $this->config['max_file_size']) {
            return new WP_Error('file_too_large', '文件过大: ' . size_format($file_size));
        }
        
        // 检查文件格式
        $image_info = wp_getimagesize($source_path);
        if (!$image_info) {
            return new WP_Error('invalid_image', '无效的图片文件: ' . $source_path);
        }
        
        $mime_type = $image_info['mime'];
        $extension = $this->get_extension_from_mime($mime_type);
        
        if (!in_array($extension, $this->config['supported_formats'])) {
            return new WP_Error('unsupported_format', '不支持的图片格式: ' . $mime_type);
        }
        
        // 生成目标路径
        if (!$target_path) {
            $target_path = $this->generate_webp_path($source_path);
        }
        
        // 确保目标目录存在
        $target_dir = dirname($target_path);
        if (!wp_mkdir_p($target_dir)) {
            return new WP_Error('mkdir_failed', '无法创建目录: ' . $target_dir);
        }
        
        // 设置质量
        $quality = $quality ?: $this->config['quality'];
        
        // 执行转换
        $result = $this->perform_conversion($source_path, $target_path, $quality, $mime_type);
        
        if (is_wp_error($result)) {
            $this->log_message('转换失败: ' . $result->get_error_message(), 'error');
            return $result;
        }
        
        // 验证转换结果
        if (!file_exists($target_path) || filesize($target_path) === 0) {
            return new WP_Error('conversion_failed', 'WebP文件生成失败');
        }
        
        $this->log_message('转换成功: ' . basename($source_path) . ' -> ' . basename($target_path), 'info');
        return true;
    }
    
    /**
     * 执行实际的图片转换
     */
    private function perform_conversion($source_path, $target_path, $quality, $mime_type) {
        // 设置内存和时间限制
        $this->set_resource_limits();
        
        try {
            if ($this->preferred_editor === 'imagick') {
                return $this->convert_with_imagick($source_path, $target_path, $quality);
            } elseif ($this->preferred_editor === 'gd') {
                return $this->convert_with_gd($source_path, $target_path, $quality, $mime_type);
            }
        } catch (Exception $e) {
            return new WP_Error('conversion_exception', $e->getMessage());
        }
        
        return new WP_Error('no_editor', '没有可用的图片编辑器');
    }
    
    /**
     * 使用Imagick进行转换
     */
    private function convert_with_imagick($source_path, $target_path, $quality) {
        $imagick = new Imagick($source_path);
        
        // 设置WebP格式和质量
        $imagick->setImageFormat('webp');
        $imagick->setImageCompressionQuality($quality);
        
        // 移除EXIF数据以减小文件大小
        $imagick->stripImage();
        
        // 保存文件
        $result = $imagick->writeImage($target_path);
        $imagick->clear();
        $imagick->destroy();
        
        return $result ? true : new WP_Error('imagick_failed', 'Imagick转换失败');
    }
    
    /**
     * 使用GD进行转换
     */
    private function convert_with_gd($source_path, $target_path, $quality, $mime_type) {
        // 根据MIME类型创建图片资源
        switch ($mime_type) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($source_path);
                break;
            case 'image/png':
                $image = imagecreatefrompng($source_path);
                // 处理PNG透明度
                imagealphablending($image, false);
                imagesavealpha($image, true);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($source_path);
                break;
            default:
                return new WP_Error('unsupported_mime', '不支持的MIME类型: ' . $mime_type);
        }
        
        if (!$image) {
            return new WP_Error('gd_create_failed', 'GD创建图片资源失败');
        }
        
        // 转换为WebP
        $result = imagewebp($image, $target_path, $quality);
        imagedestroy($image);
        
        return $result ? true : new WP_Error('gd_webp_failed', 'GD WebP转换失败');
    }
    
    /**
     * 生成WebP文件路径
     */
    private function generate_webp_path($source_path) {
        $path_info = pathinfo($source_path);
        return $path_info['dirname'] . '/' . $path_info['filename'] . '.webp';
    }
    
    /**
     * 从MIME类型获取文件扩展名
     */
    private function get_extension_from_mime($mime_type) {
        $mime_to_ext = array(
            'image/jpeg' => 'jpeg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
        );
        
        return isset($mime_to_ext[$mime_type]) ? $mime_to_ext[$mime_type] : '';
    }
    
    /**
     * 设置资源限制
     */
    private function set_resource_limits() {
        // 设置内存限制
        if (function_exists('ini_set')) {
            ini_set('memory_limit', $this->config['memory_limit']);
        }
        
        // 设置执行时间限制
        if (function_exists('set_time_limit')) {
            set_time_limit($this->config['timeout']);
        }
    }
    
    /**
     * 记录日志消息
     */
    private function log_message($message, $level = 'info') {
        if (!$this->config['enable_logging']) {
            return;
        }
        
        $log_message = sprintf('[WebP] %s - %s - %s', 
            current_time('Y-m-d H:i:s'), 
            strtoupper($level), 
            $message
        );
        
        error_log($log_message);
    }
    
    /**
     * 新图片上传时自动转换
     */
    public function auto_convert_on_upload($metadata, $attachment_id) {
        if (!$this->is_webp_supported()) {
            return $metadata;
        }
        
        $file_path = get_attached_file($attachment_id);
        if (!$file_path || !file_exists($file_path)) {
            return $metadata;
        }
        
        // 转换原始图片
        $this->convert_to_webp($file_path);
        
        // 转换所有尺寸的图片
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $upload_dir = wp_upload_dir();
            $base_dir = dirname($file_path);
            
            foreach ($metadata['sizes'] as $size_name => $size_data) {
                $size_file_path = $base_dir . '/' . $size_data['file'];
                if (file_exists($size_file_path)) {
                    $this->convert_to_webp($size_file_path);
                }
            }
        }
        
        return $metadata;
    }
    
    /**
     * 删除附件时清理WebP文件
     */
    public function cleanup_webp_on_delete($attachment_id) {
        $file_path = get_attached_file($attachment_id);
        if (!$file_path) {
            return;
        }
        
        // 删除原始图片的WebP版本
        $webp_path = $this->generate_webp_path($file_path);
        if (file_exists($webp_path)) {
            wp_delete_file($webp_path);
        }
        
        // 删除所有尺寸的WebP版本
        $metadata = wp_get_attachment_metadata($attachment_id);
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $base_dir = dirname($file_path);
            
            foreach ($metadata['sizes'] as $size_name => $size_data) {
                $size_file_path = $base_dir . '/' . $size_data['file'];
                $size_webp_path = $this->generate_webp_path($size_file_path);
                if (file_exists($size_webp_path)) {
                    wp_delete_file($size_webp_path);
                }
            }
        }
    }

    /**
     * 转换自定义尺寸为WebP
     */
    public function convert_custom_sizes_to_webp($metadata, $attachment_id) {
        if (!is_array($metadata) || !isset($metadata['sizes'])) {
            return $metadata;
        }

        if (!$this->is_webp_supported()) {
            return $metadata;
        }

        $file_path = get_attached_file($attachment_id);
        if (!$file_path) {
            return $metadata;
        }

        $base_dir = dirname($file_path);

        // 转换所有自定义尺寸
        foreach ($this->custom_sizes as $size_name => $size_config) {
            if (isset($metadata['sizes'][$size_name])) {
                $size_data = $metadata['sizes'][$size_name];
                $size_file_path = $base_dir . '/' . $size_data['file'];

                if (file_exists($size_file_path)) {
                    $webp_result = $this->convert_to_webp($size_file_path);

                    if (!is_wp_error($webp_result)) {
                        // 记录转换成功
                        $this->log_message("自定义尺寸 {$size_name} WebP转换成功", 'info');
                    }
                }
            }
        }

        return $metadata;
    }

    /**
     * 过滤自定义尺寸图片源
     */
    public function filter_custom_size_src($image, $attachment_id, $size, $icon) {
        if (!$image || $icon || !is_string($size)) {
            return $image;
        }

        // 只处理自定义尺寸
        if (!array_key_exists($size, $this->custom_sizes)) {
            return $image;
        }

        // 检查浏览器WebP支持
        if (!light_fixture_is_webp_browser_supported()) {
            return $image;
        }

        // 获取WebP版本URL
        $webp_url = $this->get_custom_size_webp_url($attachment_id, $size);
        if ($webp_url) {
            $image[0] = $webp_url;
        }

        return $image;
    }

    /**
     * 获取自定义尺寸的WebP URL
     */
    private function get_custom_size_webp_url($attachment_id, $size) {
        $cache_key = "webp_custom_url_{$attachment_id}_{$size}";

        // 检查对象缓存
        if (isset($this->webp_cache[$cache_key])) {
            return $this->webp_cache[$cache_key];
        }

        $image_src = wp_get_attachment_image_src($attachment_id, $size);
        if (!$image_src) {
            return false;
        }

        $original_url = $image_src[0];
        $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $original_url);

        // 检查WebP文件是否存在
        $upload_dir = wp_upload_dir();
        $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

        $result_url = file_exists($webp_path) ? $webp_url : false;

        // 缓存结果
        $this->webp_cache[$cache_key] = $result_url;

        return $result_url;
    }

    /**
     * 增强自定义图片HTML输出
     */
    public function enhance_custom_image_html($html, $attachment_id, $size, $icon, $attr) {
        if (!$html || $icon || !is_string($size)) {
            return $html;
        }

        // 只处理自定义尺寸
        if (!array_key_exists($size, $this->custom_sizes)) {
            return $html;
        }

        // 添加自定义尺寸相关的CSS类
        $size_class = 'webp-custom-size-' . $size;

        if (strpos($html, 'class=') !== false) {
            $html = str_replace('class="', 'class="' . $size_class . ' ', $html);
        } else {
            $html = str_replace('<img ', '<img class="' . $size_class . '" ', $html);
        }

        // 添加自定义尺寸数据属性
        $size_config = $this->custom_sizes[$size];
        $html = str_replace('<img ',
            '<img data-webp-size="' . $size . '" data-webp-dimensions="' . $size_config['width'] . 'x' . $size_config['height'] . '" ',
            $html
        );

        return $html;
    }

    /**
     * 增强自定义srcset
     */
    public function enhance_custom_srcset($sources, $size_array, $image_src, $image_meta, $attachment_id = 0) {
        if (!light_fixture_is_webp_browser_supported()) {
            return $sources;
        }

        foreach ($sources as $width => $source) {
            $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $source['url']);

            // 检查WebP文件是否存在
            $upload_dir = wp_upload_dir();
            $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);

            if (file_exists($webp_path)) {
                $sources[$width]['url'] = $webp_url;
            }
        }

        return $sources;
    }

    /**
     * 添加自定义尺寸到媒体库选择器
     */
    public function add_custom_sizes_to_media_library($sizes) {
        foreach ($this->custom_sizes as $size_name => $size_config) {
            $sizes[$size_name] = $size_config['description'];
        }
        return $sizes;
    }

    /**
     * 获取优化的产品图片
     */
    public function get_optimized_product_image($attachment_id, $size = 'product-thumbnail', $attr = array()) {
        return wp_get_attachment_image($attachment_id, $size, false, $attr);
    }

    /**
     * 获取优化的首页背景图片
     */
    public function get_optimized_hero_image($attachment_id, $size = 'hero-background', $attr = array()) {
        return wp_get_attachment_image($attachment_id, $size, false, $attr);
    }
}

// 初始化WebP转换器
function light_fixture_init_webp_converter() {
    global $light_fixture_webp_converter;
    $light_fixture_webp_converter = new Light_Fixture_WebP_Converter();
}

// 获取WebP转换器实例
function light_fixture_get_webp_converter() {
    global $light_fixture_webp_converter;
    if (!isset($light_fixture_webp_converter)) {
        light_fixture_init_webp_converter();
    }
    return $light_fixture_webp_converter;
}

// 在WordPress初始化时启动转换器
add_action('init', 'light_fixture_init_webp_converter');
