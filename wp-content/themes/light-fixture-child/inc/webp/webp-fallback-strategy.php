<?php
/**
 * WebP回退策略实现
 * 实现"后端直接转换URL + 前端15秒后回退"的方案
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP回退策略类
 */
class Light_Fixture_WebP_Fallback_Strategy {
    
    /**
     * 回退超时时间（毫秒）
     */
    const FALLBACK_TIMEOUT = 15000; // 15秒
    
    /**
     * 构造函数
     */
    public function __construct() {
        // 注册前端脚本
        add_action('wp_enqueue_scripts', array($this, 'enqueue_fallback_script'));
        
        // 修改图片输出，直接使用WebP URL
        add_filter('wp_get_attachment_image_src', array($this, 'force_webp_urls'), 10, 4);
        add_filter('the_content', array($this, 'replace_content_images_with_fallback'), 20);
    }
    
    /**
     * 加载回退脚本
     */
    public function enqueue_fallback_script() {
        wp_enqueue_script(
            'webp-fallback-strategy',
            get_stylesheet_directory_uri() . '/inc/webp/webp-fallback.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_localize_script('webp-fallback-strategy', 'webpFallback', array(
            'timeout' => self::FALLBACK_TIMEOUT,
            'debug' => defined('WP_DEBUG') && WP_DEBUG
        ));
    }
    
    /**
     * 强制返回WebP URL（如果WebP文件存在）
     */
    public function force_webp_urls($image, $attachment_id, $size, $icon) {
        if (!$image || !is_array($image)) {
            return $image;
        }
        
        $original_url = $image[0];
        
        // 检查是否为图片URL
        if (!preg_match('/\.(jpe?g|png|gif)(\?.*)?$/i', $original_url)) {
            return $image;
        }
        
        // 生成WebP URL
        $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $original_url);
        
        // 检查WebP文件是否存在
        $upload_dir = wp_upload_dir();
        $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);
        
        if (file_exists($webp_path)) {
            // 返回WebP URL，添加回退数据属性
            $image[0] = $webp_url;
        }
        
        return $image;
    }
    
    /**
     * 替换内容中的图片，添加回退机制
     */
    public function replace_content_images_with_fallback($content) {
        return preg_replace_callback(
            '/<img([^>]+)src=["\']([^"\']+)["\']([^>]*)>/i',
            array($this, 'add_fallback_to_image'),
            $content
        );
    }
    
    /**
     * 为图片添加回退机制
     */
    private function add_fallback_to_image($matches) {
        $img_attrs_before = $matches[1];
        $src_url = $matches[2];
        $img_attrs_after = $matches[3];
        
        // 检查是否为图片URL
        if (!preg_match('/\.(jpe?g|png|gif)(\?.*)?$/i', $src_url)) {
            return $matches[0];
        }
        
        // 如果已经是WebP，生成原始URL作为回退
        if (preg_match('/\.webp(\?.*)?$/i', $src_url)) {
            $fallback_url = preg_replace('/\.webp(\?.*)?$/i', '.jpg$1', $src_url);
            
            // 尝试其他格式
            $upload_dir = wp_upload_dir();
            $fallback_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $fallback_url);
            
            if (!file_exists($fallback_path)) {
                $fallback_url = preg_replace('/\.webp(\?.*)?$/i', '.png$1', $src_url);
                $fallback_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $fallback_url);
                
                if (!file_exists($fallback_path)) {
                    $fallback_url = preg_replace('/\.webp(\?.*)?$/i', '.gif$1', $src_url);
                }
            }
            
            return '<img' . $img_attrs_before . 'src="' . $src_url . '" data-fallback="' . $fallback_url . '" class="webp-with-fallback"' . $img_attrs_after . '>';
        }
        
        // 如果是原始格式，检查是否有WebP版本
        $webp_url = preg_replace('/\.(jpe?g|png|gif)(\?.*)?$/i', '.webp$2', $src_url);
        $upload_dir = wp_upload_dir();
        $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);
        
        if (file_exists($webp_path)) {
            return '<img' . $img_attrs_before . 'src="' . $webp_url . '" data-fallback="' . $src_url . '" class="webp-with-fallback"' . $img_attrs_after . '>';
        }
        
        return $matches[0];
    }
}

// 初始化回退策略（仅在需要时）
function light_fixture_init_webp_fallback_strategy() {
    // 可以通过选项控制是否启用此策略
    if (get_option('light_fixture_webp_fallback_enabled', false)) {
        new Light_Fixture_WebP_Fallback_Strategy();
    }
}

// 在WordPress初始化时启动
add_action('init', 'light_fixture_init_webp_fallback_strategy');

/**
 * 添加管理选项
 */
function light_fixture_add_webp_fallback_option() {
    add_settings_field(
        'light_fixture_webp_fallback_enabled',
        'WebP回退策略',
        'light_fixture_webp_fallback_option_callback',
        'media',
        'default'
    );
    
    register_setting('media', 'light_fixture_webp_fallback_enabled');
}
add_action('admin_init', 'light_fixture_add_webp_fallback_option');

/**
 * 回退选项回调
 */
function light_fixture_webp_fallback_option_callback() {
    $enabled = get_option('light_fixture_webp_fallback_enabled', false);
    echo '<label>';
    echo '<input type="checkbox" name="light_fixture_webp_fallback_enabled" value="1" ' . checked(1, $enabled, false) . '>';
    echo ' 启用WebP回退策略（后端强制WebP + 前端15秒回退）';
    echo '</label>';
    echo '<p class="description">启用后，将直接输出WebP URL，如果加载失败则在15秒后回退到原始格式。</p>';
}
