<?php
/**
 * WebP Method Check Script
 * WebP方法检查脚本
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 检查WebP钩子集成类的所有方法
 */
function light_fixture_check_webp_methods() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    echo '<div style="max-width: 1200px; margin: 20px auto; padding: 20px; font-family: Arial, sans-serif;">';
    echo '<h1>🔍 WebP方法检查报告</h1>';
    
    // 检查类是否存在
    if (!class_exists('Light_Fixture_WebP_Hooks_Integration')) {
        echo '<p style="color: red;">❌ Light_Fixture_WebP_Hooks_Integration 类不存在</p>';
        echo '</div>';
        return;
    }
    
    echo '<p style="color: green;">✅ Light_Fixture_WebP_Hooks_Integration 类存在</p>';
    
    // 获取类的所有方法
    $reflection = new ReflectionClass('Light_Fixture_WebP_Hooks_Integration');
    $methods = $reflection->getMethods();
    
    echo '<h2>📋 类方法列表</h2>';
    echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    
    $required_methods = array(
        'add_performance_hints' => '添加性能提示',
        'add_webp_preload_hints' => '添加WebP预加载提示',
        'enhance_image_metadata_for_seo' => '增强图片元数据用于SEO',
        'optimize_social_media_images' => '优化社交媒体图片',
        'optimize_sitemap_images' => '优化sitemap图片',
        'add_webp_images_to_sitemap' => '添加WebP图片到sitemap',
        'add_web_vitals_optimization' => '添加Core Web Vitals优化',
        'add_search_engine_hints' => '添加搜索引擎提示',
        'optimize_amp_images' => '优化AMP图片',
        'add_cache_optimization_headers' => '添加缓存优化头部'
    );
    
    $existing_methods = array();
    foreach ($methods as $method) {
        $existing_methods[] = $method->getName();
    }
    
    echo '<h3>必需的SEO方法检查：</h3>';
    foreach ($required_methods as $method_name => $description) {
        if (in_array($method_name, $existing_methods)) {
            echo "✅ <strong>$method_name</strong> - $description<br>";
        } else {
            echo "❌ <strong>$method_name</strong> - $description (缺失)<br>";
        }
    }
    
    echo '<h3>所有方法列表：</h3>';
    foreach ($existing_methods as $method_name) {
        $method = $reflection->getMethod($method_name);
        $visibility = '';
        if ($method->isPublic()) $visibility = 'public';
        elseif ($method->isProtected()) $visibility = 'protected';
        elseif ($method->isPrivate()) $visibility = 'private';
        
        echo "<code>$visibility $method_name()</code><br>";
    }
    
    echo '</div>';
    
    // 检查钩子注册
    echo '<h2>🔗 钩子注册检查</h2>';
    echo '<div style="background: #f9f9f9; padding: 15px; border-radius: 5px; margin: 10px 0;">';
    
    $seo_hooks = array(
        'wp_head' => array('add_performance_hints', 'add_webp_preload_hints', 'optimize_social_media_images', 'add_search_engine_hints', 'add_cache_optimization_headers'),
        'wp_get_attachment_metadata' => array('enhance_image_metadata_for_seo'),
        'wp_sitemaps_posts_entry' => array('optimize_sitemap_images'),
        'wp_sitemaps_posts_url_list' => array('add_webp_images_to_sitemap'),
        'wp_footer' => array('add_web_vitals_optimization'),
        'amp_post_template_data' => array('optimize_amp_images')
    );
    
    foreach ($seo_hooks as $hook_name => $hook_methods) {
        if (has_action($hook_name) || has_filter($hook_name)) {
            echo "✅ <strong>$hook_name</strong> 钩子已注册<br>";
            
            // 检查具体的回调方法
            foreach ($hook_methods as $method) {
                if (method_exists('Light_Fixture_WebP_Hooks_Integration', $method)) {
                    echo "&nbsp;&nbsp;&nbsp;&nbsp;✅ $method 方法存在<br>";
                } else {
                    echo "&nbsp;&nbsp;&nbsp;&nbsp;❌ $method 方法不存在<br>";
                }
            }
        } else {
            echo "❌ <strong>$hook_name</strong> 钩子未注册<br>";
        }
    }
    
    echo '</div>';
    echo '</div>';
}

// 如果通过URL参数访问检查页面
if (isset($_GET['webp_method_check']) && current_user_can('manage_options')) {
    add_action('wp_head', function() {
        echo '<title>WebP方法检查</title>';
    });
    
    add_action('wp_footer', function() {
        light_fixture_check_webp_methods();
        exit;
    });
}
