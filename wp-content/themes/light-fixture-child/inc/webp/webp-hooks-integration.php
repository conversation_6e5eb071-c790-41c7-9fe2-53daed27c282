<?php
/**
 * WebP Hooks Integration
 * WebP钩子集成增强模块
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP钩子集成类
 */
class Light_Fixture_WebP_Hooks_Integration {

    /**
     * 支持的缓存插件
     */
    private $supported_cache_plugins = array(
        'wp-rocket' => 'WP Rocket',
        'w3-total-cache' => 'W3 Total Cache',
        'wp-super-cache' => 'WP Super Cache',
        'litespeed-cache' => 'LiteSpeed Cache',
        'wp-fastest-cache' => 'WP Fastest Cache',
        'autoptimize' => 'Autoptimize',
        'wp-optimize' => 'WP-Optimize'
    );

    /**
     * 构造函数
     */
    public function __construct() {
        $this->init_upload_hooks();
        $this->init_image_hooks();
        $this->init_admin_hooks();
        $this->init_frontend_hooks();
        $this->init_cache_hooks();
        $this->init_seo_hooks();
        $this->detect_and_integrate_cache_plugins();
    }
    
    /**
     * 初始化上传相关钩子
     */
    private function init_upload_hooks() {
        // 上传前验证
        add_filter('wp_handle_upload_prefilter', array($this, 'pre_upload_validation'));
        
        // 上传完成后处理
        add_filter('wp_handle_upload', array($this, 'post_upload_processing'), 10, 2);
        
        // 附件元数据生成后处理 - 转换所有尺寸
        add_filter('wp_generate_attachment_metadata', array($this, 'convert_all_sizes_to_webp'), 25, 2);

        // 图片编辑后重新生成WebP
        add_filter('wp_save_image_editor_file', array($this, 'regenerate_webp_on_edit'), 10, 5);

        // 单个尺寸生成后立即转换
        add_filter('image_make_intermediate_size', array($this, 'convert_intermediate_size'), 10, 1);
    }
    
    /**
     * 初始化图片相关钩子
     */
    private function init_image_hooks() {
        // 图片URL过滤 - 智能返回WebP版本
        add_filter('wp_get_attachment_image_src', array($this, 'filter_attachment_image_src'), 10, 4);
        
        // 图片HTML过滤 - 添加WebP支持
        add_filter('wp_get_attachment_image', array($this, 'filter_attachment_image_html'), 10, 5);
        
        // 响应式图片srcset过滤
        add_filter('wp_get_attachment_image_srcset', array($this, 'filter_attachment_srcset'), 10, 4);
        
        // 图片sizes属性过滤
        add_filter('wp_get_attachment_image_sizes', array($this, 'filter_attachment_sizes'), 10, 5);
        
        // 媒体库显示增强
        add_filter('attachment_fields_to_edit', array($this, 'add_webp_fields_to_media'), 10, 2);
    }
    
    /**
     * 初始化管理后台钩子
     */
    private function init_admin_hooks() {
        // 媒体库列表增强
        add_filter('manage_media_columns', array($this, 'add_webp_column'));
        add_action('manage_media_custom_column', array($this, 'display_webp_column'), 10, 2);

        // 附件详情页面增强
        add_action('attachment_submitbox_misc_actions', array($this, 'add_webp_info_to_attachment'));

        // 加载管理界面脚本
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // AJAX处理
        add_action('wp_ajax_regenerate_webp', array($this, 'ajax_regenerate_webp'));
        add_action('wp_ajax_bulk_convert_webp', array($this, 'ajax_bulk_convert_webp'));
    }
    
    /**
     * 初始化前端钩子
     */
    private function init_frontend_hooks() {
        // 内容过滤 - 自动替换图片为WebP版本
        add_filter('the_content', array($this, 'filter_content_images'), 20);
        add_filter('post_thumbnail_html', array($this, 'filter_thumbnail_html'), 10, 5);
        
        // 小工具内容过滤
        add_filter('widget_text', array($this, 'filter_widget_images'), 20);
        
        // 自定义字段图片过滤
        add_filter('acf/format_value/type=image', array($this, 'filter_acf_image'), 10, 3);
    }
    
    /**
     * 上传前验证
     */
    public function pre_upload_validation($file) {
        // 检查文件类型是否支持WebP转换
        if (isset($file['type']) && strpos($file['type'], 'image/') === 0) {
            $supported_types = array('image/jpeg', 'image/png', 'image/gif');
            
            if (in_array($file['type'], $supported_types)) {
                // 添加标记，表示这个文件支持WebP转换
                $file['webp_convertible'] = true;
            }
        }
        
        return $file;
    }
    
    /**
     * 上传完成后处理
     */
    public function post_upload_processing($upload, $context) {
        // 记录上传信息
        if (isset($upload['file']) && isset($upload['webp_convertible'])) {
            light_fixture_log_webp_message(
                '新图片上传: ' . basename($upload['file']),
                'info',
                array('context' => $context, 'file_size' => filesize($upload['file']))
            );
        }
        
        return $upload;
    }
    
    /**
     * 转换所有尺寸为WebP
     */
    public function convert_all_sizes_to_webp($metadata, $attachment_id) {
        if (!is_array($metadata)) {
            return $metadata;
        }

        $file_path = get_attached_file($attachment_id);
        if (!$file_path || !light_fixture_is_supported_image($file_path)) {
            return $metadata;
        }

        $converter = light_fixture_get_webp_converter();
        if (!$converter->is_webp_supported()) {
            return $metadata;
        }

        $conversion_results = array();
        $total_savings = 0;

        // 转换原始文件
        $start_time = microtime(true);
        $result = $converter->convert_to_webp($file_path);
        $conversion_time = microtime(true) - $start_time;

        $webp_info = array(
            'webp_converted' => !is_wp_error($result),
            'webp_file_exists' => false,
            'webp_conversion_time' => $conversion_time,
            'webp_file_size' => 0,
            'webp_savings' => 0
        );

        if (!is_wp_error($result)) {
            $webp_path = light_fixture_get_webp_path($file_path);
            if (file_exists($webp_path)) {
                $webp_info['webp_file_exists'] = true;
                $webp_info['webp_file_size'] = filesize($webp_path);
                $original_size = filesize($file_path);
                $webp_info['webp_savings'] = $original_size - $webp_info['webp_file_size'];
                $total_savings += $webp_info['webp_savings'];

                light_fixture_log_webp_message(
                    "原始文件WebP转换成功: " . basename($file_path) .
                    " (节省: " . size_format($webp_info['webp_savings']) . ")",
                    'info',
                    array('attachment_id' => $attachment_id)
                );
            }
        } else {
            light_fixture_log_webp_message(
                "原始文件WebP转换失败: " . $result->get_error_message(),
                'error',
                array('attachment_id' => $attachment_id, 'file_path' => $file_path)
            );
        }

        // 转换所有中间尺寸
        if (isset($metadata['sizes']) && is_array($metadata['sizes'])) {
            $base_dir = dirname($file_path);

            foreach ($metadata['sizes'] as $size_name => &$size_data) {
                $size_file_path = $base_dir . '/' . $size_data['file'];

                if (!file_exists($size_file_path)) {
                    continue;
                }

                $start_time = microtime(true);
                $size_result = $converter->convert_to_webp($size_file_path);
                $size_conversion_time = microtime(true) - $start_time;

                $size_data['webp_converted'] = !is_wp_error($size_result);
                $size_data['webp_conversion_time'] = $size_conversion_time;
                $size_data['webp_exists'] = false;
                $size_data['webp_file_size'] = 0;
                $size_data['webp_savings'] = 0;

                if (!is_wp_error($size_result)) {
                    $size_webp_path = light_fixture_get_webp_path($size_file_path);
                    if (file_exists($size_webp_path)) {
                        $size_data['webp_exists'] = true;
                        $size_data['webp_file_size'] = filesize($size_webp_path);
                        $size_original_size = filesize($size_file_path);
                        $size_data['webp_savings'] = $size_original_size - $size_data['webp_file_size'];
                        $total_savings += $size_data['webp_savings'];

                        light_fixture_log_webp_message(
                            "尺寸WebP转换成功: {$size_name} - " . basename($size_file_path) .
                            " (节省: " . size_format($size_data['webp_savings']) . ")",
                            'info',
                            array('attachment_id' => $attachment_id, 'size' => $size_name)
                        );
                    }
                } else {
                    light_fixture_log_webp_message(
                        "尺寸WebP转换失败: {$size_name} - " . $size_result->get_error_message(),
                        'error',
                        array('attachment_id' => $attachment_id, 'size' => $size_name)
                    );
                }
            }
        }

        $webp_info['total_savings'] = $total_savings;
        $metadata['webp_info'] = $webp_info;

        // 触发转换完成事件
        do_action('light_fixture_webp_converted', $attachment_id, $file_path, $webp_info);

        return $metadata;
    }
    
    /**
     * 图片编辑后重新生成WebP
     */
    public function regenerate_webp_on_edit($override, $filename, $image, $mime_type, $post_id) {
        if (!$override && $post_id && light_fixture_is_supported_image($filename)) {
            // 延迟执行WebP转换，避免影响编辑器保存
            wp_schedule_single_event(time() + 5, 'light_fixture_delayed_webp_conversion', array($filename, $post_id));
        }
        
        return $override;
    }
    
    /**
     * 转换中间尺寸图片
     */
    public function convert_intermediate_size($resized_file) {
        if ($resized_file && isset($resized_file['file'])) {
            $converter = light_fixture_get_webp_converter();
            $file_path = dirname($resized_file['path']) . '/' . $resized_file['file'];
            
            if (file_exists($file_path)) {
                $converter->convert_to_webp($file_path);
            }
        }
        
        return $resized_file;
    }
    
    /**
     * 过滤附件图片源
     */
    public function filter_attachment_image_src($image, $attachment_id, $size, $icon) {
        if (!$image || $icon || !light_fixture_is_webp_browser_supported()) {
            return $image;
        }
        
        $webp_url = light_fixture_get_optimized_image_url($image[0]);
        if ($webp_url !== $image[0]) {
            $image[0] = $webp_url;
        }
        
        return $image;
    }
    
    /**
     * 过滤附件图片HTML
     */
    public function filter_attachment_image_html($html, $attachment_id, $size, $icon, $attr) {
        if (!$html || $icon || !light_fixture_is_webp_browser_supported()) {
            return $html;
        }
        
        // 为图片添加WebP数据属性
        $html = preg_replace_callback(
            '/(<img[^>]+src=["\'])([^"\']+)(["\'][^>]*>)/i',
            function($matches) {
                $img_tag = $matches[1];
                $src_url = $matches[2];
                $img_end = $matches[3];
                
                $webp_url = light_fixture_get_optimized_image_url($src_url);
                if ($webp_url !== $src_url) {
                    // 添加data-webp属性
                    $img_end = str_replace('>', ' data-webp="' . esc_attr($webp_url) . '">', $img_end);
                }
                
                return $img_tag . $src_url . $img_end;
            },
            $html
        );
        
        return $html;
    }
    
    /**
     * 过滤响应式图片srcset
     */
    public function filter_attachment_srcset($sources, $size_array, $image_src, $image_meta) {
        if (!light_fixture_is_webp_browser_supported() || empty($sources)) {
            return $sources;
        }
        
        foreach ($sources as $width => &$source) {
            $webp_url = light_fixture_get_optimized_image_url($source['url']);
            if ($webp_url !== $source['url']) {
                $source['url'] = $webp_url;
            }
        }
        
        return $sources;
    }
    
    /**
     * 过滤图片sizes属性
     */
    public function filter_attachment_sizes($sizes, $size, $image_src, $image_meta, $attachment_id) {
        // 这里可以根据WebP支持情况调整sizes属性
        // 目前保持原样
        return $sizes;
    }
    
    /**
     * 添加WebP字段到媒体编辑页面
     */
    public function add_webp_fields_to_media($fields, $post) {
        if (!wp_attachment_is_image($post->ID)) {
            return $fields;
        }
        
        $webp_status = light_fixture_check_attachment_webp_status($post->ID);
        $has_webp = in_array(true, $webp_status, true);
        
        $status_html = '<div style="margin-top: 10px;">';
        $status_html .= '<strong>WebP状态:</strong><br>';
        
        if ($has_webp) {
            $status_html .= '<span style="color: green;">✅ 已转换</span><br>';
            
            foreach ($webp_status as $size => $exists) {
                $icon = $exists ? '✅' : '❌';
                $status_html .= "<small>$icon $size</small><br>";
            }
            
            $status_html .= '<button type="button" onclick="regenerateWebP(' . $post->ID . ')" class="button">重新生成WebP</button>';
        } else {
            $status_html .= '<span style="color: red;">❌ 未转换</span><br>';
            $status_html .= '<button type="button" onclick="convertToWebP(' . $post->ID . ')" class="button button-primary">转换为WebP</button>';
        }
        
        $status_html .= '</div>';
        
        $fields['webp_status'] = array(
            'label' => 'WebP转换',
            'input' => 'html',
            'html' => $status_html
        );
        
        return $fields;
    }
    
    /**
     * 添加WebP列到媒体库
     */
    public function add_webp_column($columns) {
        $columns['webp_status'] = 'WebP';
        return $columns;
    }
    
    /**
     * 显示WebP列内容
     */
    public function display_webp_column($column_name, $attachment_id) {
        if ($column_name === 'webp_status' && wp_attachment_is_image($attachment_id)) {
            $webp_status = light_fixture_check_attachment_webp_status($attachment_id);
            $has_webp = in_array(true, $webp_status, true);
            
            if ($has_webp) {
                echo '<span style="color: green;">✅</span>';
            } else {
                echo '<span style="color: red;">❌</span>';
            }
        }
    }
    
    /**
     * 加载管理界面脚本
     */
    public function enqueue_admin_scripts($hook) {
        // 只在媒体相关页面加载
        if (!in_array($hook, array('upload.php', 'post.php', 'media.php', 'attachment.php'))) {
            return;
        }

        wp_enqueue_script(
            'light-fixture-webp-admin',
            get_stylesheet_directory_uri() . '/inc/webp/webp-admin.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // 传递数据到JavaScript
        wp_localize_script('light-fixture-webp-admin', 'webpAdmin', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'regenerateNonce' => wp_create_nonce('regenerate_webp_nonce'),
            'bulkConvertNonce' => wp_create_nonce('bulk_convert_webp_nonce')
        ));
    }

    /**
     * 添加WebP信息到附件详情
     */
    public function add_webp_info_to_attachment() {
        global $post;
        
        if (!wp_attachment_is_image($post->ID)) {
            return;
        }
        
        $comparison = light_fixture_get_size_comparison(get_attached_file($post->ID));
        
        if ($comparison) {
            echo '<div class="misc-pub-section misc-pub-webp">';
            echo '<strong>WebP信息:</strong><br>';
            echo '原始大小: ' . $comparison['original_size_formatted'] . '<br>';
            echo 'WebP大小: ' . $comparison['webp_size_formatted'] . '<br>';
            echo '节省空间: ' . light_fixture_format_savings_percent($comparison['savings_percent']);
            echo '</div>';
        }
    }
    
    /**
     * AJAX重新生成WebP
     */
    public function ajax_regenerate_webp() {
        check_ajax_referer('regenerate_webp_nonce', 'nonce');
        
        if (!current_user_can('upload_files')) {
            wp_die('权限不足');
        }
        
        $attachment_id = intval($_POST['attachment_id']);
        $file_path = get_attached_file($attachment_id);
        
        if (!$file_path) {
            wp_send_json_error('文件不存在');
        }
        
        $converter = light_fixture_get_webp_converter();
        $result = $converter->convert_to_webp($file_path);
        
        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        } else {
            wp_send_json_success('WebP重新生成成功');
        }
    }
    
    /**
     * AJAX批量转换WebP
     */
    public function ajax_bulk_convert_webp() {
        check_ajax_referer('bulk_convert_webp_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('权限不足');
        }
        
        $batch_size = intval($_POST['batch_size']) ?: 10;
        $offset = intval($_POST['offset']) ?: 0;
        
        // 获取待转换的图片
        $attachments = get_posts(array(
            'post_type' => 'attachment',
            'post_mime_type' => array('image/jpeg', 'image/png', 'image/gif'),
            'posts_per_page' => $batch_size,
            'offset' => $offset,
            'post_status' => 'inherit'
        ));
        
        $converted = 0;
        $errors = array();
        $converter = light_fixture_get_webp_converter();
        
        foreach ($attachments as $attachment) {
            $file_path = get_attached_file($attachment->ID);
            
            if ($file_path && !light_fixture_webp_exists($file_path)) {
                $result = $converter->convert_to_webp($file_path);
                
                if (is_wp_error($result)) {
                    $errors[] = $attachment->ID . ': ' . $result->get_error_message();
                } else {
                    $converted++;
                }
            }
        }
        
        wp_send_json_success(array(
            'converted' => $converted,
            'errors' => $errors,
            'has_more' => count($attachments) === $batch_size
        ));
    }
    
    /**
     * 过滤内容中的图片
     */
    public function filter_content_images($content) {
        if (!light_fixture_is_webp_browser_supported()) {
            return $content;
        }
        
        return preg_replace_callback(
            '/<img([^>]+)src=["\']([^"\']+)["\']([^>]*)>/i',
            array($this, 'replace_content_image'),
            $content
        );
    }
    
    /**
     * 替换内容中的图片
     */
    private function replace_content_image($matches) {
        $img_attrs_before = $matches[1];
        $src_url = $matches[2];
        $img_attrs_after = $matches[3];
        
        $webp_url = light_fixture_get_optimized_image_url($src_url);
        
        if ($webp_url !== $src_url) {
            return '<img' . $img_attrs_before . 'src="' . $src_url . '" data-webp="' . $webp_url . '"' . $img_attrs_after . '>';
        }
        
        return $matches[0];
    }
    
    /**
     * 过滤缩略图HTML
     */
    public function filter_thumbnail_html($html, $post_id, $post_thumbnail_id, $size, $attr) {
        return $this->filter_attachment_image_html($html, $post_thumbnail_id, $size, false, $attr);
    }
    
    /**
     * 过滤小工具图片
     */
    public function filter_widget_images($text) {
        return $this->filter_content_images($text);
    }
    
    /**
     * 过滤ACF图片字段
     */
    public function filter_acf_image($value, $post_id, $field) {
        if (is_array($value) && isset($value['url'])) {
            $value['webp_url'] = light_fixture_get_optimized_image_url($value['url']);
        }
        
        return $value;
    }

    /**
     * 初始化缓存相关钩子
     */
    private function init_cache_hooks() {
        // 缓存清理钩子
        add_action('light_fixture_webp_converted', array($this, 'clear_image_cache'), 10, 2);
        add_action('delete_attachment', array($this, 'clear_attachment_cache'));

        // 缓存预热
        add_action('wp_ajax_webp_cache_preload', array($this, 'handle_cache_preload'));
        add_action('wp_ajax_nopriv_webp_cache_preload', array($this, 'handle_cache_preload'));

        // 浏览器缓存优化
        add_action('wp_head', array($this, 'add_browser_cache_headers'), 1);

        // CDN集成
        add_filter('wp_get_attachment_url', array($this, 'optimize_cdn_urls'), 20, 2);
    }

    /**
     * 初始化SEO相关钩子
     */
    private function init_seo_hooks() {
        // 页面速度优化
        add_action('wp_head', array($this, 'add_performance_hints'), 1);
        add_action('wp_head', array($this, 'add_webp_preload_hints'), 2);

        // 结构化数据增强
        add_filter('wp_get_attachment_metadata', array($this, 'enhance_image_metadata_for_seo'), 20, 2);

        // Open Graph和Twitter Cards优化
        add_action('wp_head', array($this, 'optimize_social_media_images'), 5);

        // XML Sitemap图片优化
        add_filter('wp_sitemaps_posts_entry', array($this, 'optimize_sitemap_images'), 10, 3);
        add_filter('wp_sitemaps_posts_url_list', array($this, 'add_webp_images_to_sitemap'), 10, 2);

        // Core Web Vitals优化
        add_action('wp_footer', array($this, 'add_web_vitals_optimization'), 1);

        // 搜索引擎爬虫优化
        add_action('wp_head', array($this, 'add_search_engine_hints'), 3);

        // AMP支持
        add_filter('amp_post_template_data', array($this, 'optimize_amp_images'));

        // 页面缓存集成
        add_action('wp_head', array($this, 'add_cache_optimization_headers'), 1);
    }

    /**
     * 检测并集成缓存插件
     */
    private function detect_and_integrate_cache_plugins() {
        // WP Rocket集成
        if ($this->is_plugin_active('wp-rocket/wp-rocket.php')) {
            $this->integrate_wp_rocket();
        }

        // W3 Total Cache集成
        if ($this->is_plugin_active('w3-total-cache/w3-total-cache.php')) {
            $this->integrate_w3_total_cache();
        }

        // WP Super Cache集成
        if ($this->is_plugin_active('wp-super-cache/wp-cache.php')) {
            $this->integrate_wp_super_cache();
        }

        // LiteSpeed Cache集成
        if ($this->is_plugin_active('litespeed-cache/litespeed-cache.php')) {
            $this->integrate_litespeed_cache();
        }

        // WP Fastest Cache集成
        if ($this->is_plugin_active('wp-fastest-cache/wpFastestCache.php')) {
            $this->integrate_wp_fastest_cache();
        }

        // Autoptimize集成
        if ($this->is_plugin_active('autoptimize/autoptimize.php')) {
            $this->integrate_autoptimize();
        }
    }

    /**
     * 检查插件是否激活
     */
    private function is_plugin_active($plugin_path) {
        return is_plugin_active($plugin_path);
    }

    /**
     * WP Rocket集成
     */
    private function integrate_wp_rocket() {
        // 添加WebP到WP Rocket的图片优化
        add_filter('rocket_buffer', array($this, 'wp_rocket_webp_replacement'));

        // 清理WP Rocket缓存
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (function_exists('rocket_clean_post')) {
                rocket_clean_post($attachment_id);
            }
            if (function_exists('rocket_clean_domain')) {
                rocket_clean_domain();
            }
        }, 10, 2);

        // 添加WebP到预加载
        add_filter('rocket_preload_urls', array($this, 'add_webp_urls_to_rocket_preload'));
    }

    /**
     * W3 Total Cache集成
     */
    private function integrate_w3_total_cache() {
        // 清理W3TC缓存
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (function_exists('w3tc_flush_all')) {
                w3tc_flush_all();
            }
        }, 10, 2);

        // 添加WebP MIME类型
        add_filter('w3tc_can_cache_request', array($this, 'w3tc_allow_webp_caching'));
    }

    /**
     * WP Super Cache集成
     */
    private function integrate_wp_super_cache() {
        // 清理WP Super Cache
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (function_exists('wp_cache_clear_cache')) {
                wp_cache_clear_cache();
            }
        }, 10, 2);
    }

    /**
     * LiteSpeed Cache集成
     */
    private function integrate_litespeed_cache() {
        // 清理LiteSpeed缓存
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (class_exists('LiteSpeed\Purge')) {
                LiteSpeed\Purge::purge_all();
            }
        }, 10, 2);

        // 集成LiteSpeed的WebP功能
        add_filter('litespeed_media_webp_check', '__return_true');
    }

    /**
     * WP Fastest Cache集成
     */
    private function integrate_wp_fastest_cache() {
        // 清理WP Fastest Cache
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (class_exists('WpFastestCache')) {
                $wpfc = new WpFastestCache();
                $wpfc->deleteCache();
            }
        }, 10, 2);
    }

    /**
     * Autoptimize集成
     */
    private function integrate_autoptimize() {
        // 清理Autoptimize缓存
        add_action('light_fixture_webp_converted', function($attachment_id, $file_path) {
            if (class_exists('autoptimizeCache')) {
                autoptimizeCache::clearall();
            }
        }, 10, 2);
    }

    /**
     * 清理图片缓存
     */
    public function clear_image_cache($attachment_id, $file_path) {
        // 清理WordPress对象缓存
        wp_cache_delete($attachment_id, 'posts');
        wp_cache_delete($attachment_id, 'post_meta');

        // 清理自定义缓存
        wp_cache_flush_group('webp_detection');
        wp_cache_flush_group('webp_urls');
    }

    /**
     * 清理附件缓存
     */
    public function clear_attachment_cache($attachment_id) {
        $this->clear_image_cache($attachment_id, '');
    }

    /**
     * 处理缓存预热请求
     */
    public function handle_cache_preload() {
        if (!current_user_can('manage_options')) {
            wp_die('权限不足');
        }

        // 预热关键图片的WebP版本
        $this->preload_critical_webp_images();

        wp_send_json_success('缓存预热完成');
    }

    /**
     * 预热关键WebP图片
     */
    private function preload_critical_webp_images() {
        global $wpdb;

        // 获取最近的图片附件
        $attachments = $wpdb->get_results(
            "SELECT ID FROM {$wpdb->posts}
             WHERE post_type = 'attachment'
             AND post_mime_type LIKE 'image/%'
             ORDER BY post_date DESC
             LIMIT 20"
        );

        foreach ($attachments as $attachment) {
            $sizes = array('thumbnail', 'medium', 'large');

            foreach ($sizes as $size) {
                $image_src = wp_get_attachment_image_src($attachment->ID, $size);
                if ($image_src) {
                    // 预热缓存
                    light_fixture_get_optimized_image_url($image_src[0]);
                }
            }
        }
    }

    /**
     * 添加浏览器缓存头部
     */
    public function add_browser_cache_headers() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        // 添加Vary头部用于缓存
        if (!headers_sent()) {
            header('Vary: Accept');
        }

        echo '<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width">' . "\n";
    }

    /**
     * 优化CDN URLs
     */
    public function optimize_cdn_urls($url, $attachment_id) {
        if (!light_fixture_is_webp_browser_supported()) {
            return $url;
        }

        // 检查是否使用CDN
        $cdn_url = $this->get_cdn_base_url();
        if (!$cdn_url) {
            return $url;
        }

        // 检查WebP版本是否存在
        $webp_url = light_fixture_get_optimized_image_url($url);
        if ($webp_url !== $url) {
            // 替换为CDN URL
            $upload_dir = wp_upload_dir();
            $relative_path = str_replace($upload_dir['baseurl'], '', $webp_url);
            return $cdn_url . $relative_path;
        }

        return $url;
    }

    /**
     * 获取CDN基础URL
     */
    private function get_cdn_base_url() {
        // 检查常见的CDN插件
        if (defined('CLOUDFLARE_PLUGIN_DIR')) {
            return get_option('cloudflare_cdn_url', '');
        }

        if (class_exists('MaxCDN')) {
            return get_option('maxcdn_url', '');
        }

        // 检查自定义CDN设置
        return get_option('light_fixture_cdn_url', '');
    }

    /**
     * WP Rocket WebP替换
     */
    public function wp_rocket_webp_replacement($buffer) {
        if (!light_fixture_is_webp_browser_supported()) {
            return $buffer;
        }

        // 替换图片URL为WebP版本
        $buffer = preg_replace_callback(
            '/(<img[^>]+src=["\'])([^"\']+\.(jpe?g|png|gif))(["\'][^>]*>)/i',
            function($matches) {
                $img_start = $matches[1];
                $img_url = $matches[2];
                $img_end = $matches[4];

                $webp_url = light_fixture_get_optimized_image_url($img_url);
                if ($webp_url !== $img_url) {
                    return $img_start . $webp_url . $img_end;
                }

                return $matches[0];
            },
            $buffer
        );

        return $buffer;
    }

    /**
     * 添加WebP URLs到WP Rocket预加载
     */
    public function add_webp_urls_to_rocket_preload($urls) {
        foreach ($urls as $url) {
            // 检查URL中的图片并添加WebP版本
            if (preg_match('/\.(jpe?g|png|gif)$/i', $url)) {
                $webp_url = light_fixture_get_optimized_image_url($url);
                if ($webp_url !== $url) {
                    $urls[] = $webp_url;
                }
            }
        }

        return $urls;
    }

    /**
     * W3TC允许WebP缓存
     */
    public function w3tc_allow_webp_caching($can_cache) {
        if (strpos($_SERVER['REQUEST_URI'] ?? '', '.webp') !== false) {
            return true;
        }

        return $can_cache;
    }

    /**
     * 添加性能提示
     */
    public function add_performance_hints() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        echo "<!-- WebP Performance Hints -->\n";
        echo '<link rel="dns-prefetch" href="//fonts.googleapis.com">' . "\n";
        echo '<link rel="preconnect" href="//fonts.gstatic.com" crossorigin>' . "\n";
    }

    /**
     * 添加WebP预加载提示
     */
    public function add_webp_preload_hints() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        // 调用工具函数进行预加载
        light_fixture_preload_critical_webp_images();
    }

    /**
     * 增强图片元数据用于SEO
     */
    public function enhance_image_metadata_for_seo($metadata, $attachment_id) {
        if (!is_array($metadata)) {
            return $metadata;
        }

        // 添加WebP SEO信息
        if (light_fixture_webp_exists(get_attached_file($attachment_id))) {
            $metadata['seo_optimized'] = true;
            $metadata['webp_enabled'] = true;

            // 计算性能提升
            $size_comparison = light_fixture_get_size_comparison(get_attached_file($attachment_id));
            if ($size_comparison) {
                $metadata['performance_improvement'] = array(
                    'size_reduction' => $size_comparison['savings_percent'],
                    'load_time_improvement' => $this->estimate_load_time_improvement($size_comparison['savings_percent'])
                );
            }
        }

        return $metadata;
    }

    /**
     * 优化社交媒体图片
     */
    public function optimize_social_media_images() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        // 获取当前页面的特色图片
        $image_url = '';
        if (is_singular()) {
            $thumbnail_id = get_post_thumbnail_id();
            if ($thumbnail_id) {
                $image_src = wp_get_attachment_image_src($thumbnail_id, 'large');
                if ($image_src) {
                    $image_url = light_fixture_get_optimized_image_url($image_src[0]);
                }
            }
        }

        if ($image_url) {
            echo '<meta property="og:image" content="' . esc_url($image_url) . '">' . "\n";
            echo '<meta name="twitter:image" content="' . esc_url($image_url) . '">' . "\n";
        }
    }

    /**
     * 优化sitemap图片
     */
    public function optimize_sitemap_images($entry, $post, $post_type) {
        // 为sitemap条目添加WebP图片信息
        if (has_post_thumbnail($post->ID)) {
            $thumbnail_id = get_post_thumbnail_id($post->ID);
            $image_url = wp_get_attachment_image_src($thumbnail_id, 'large');

            if ($image_url) {
                $webp_url = light_fixture_get_optimized_image_url($image_url[0]);
                if ($webp_url !== $image_url[0]) {
                    $entry['webp_image'] = $webp_url;
                }
            }
        }

        return $entry;
    }

    /**
     * 添加WebP图片到sitemap
     */
    public function add_webp_images_to_sitemap($url_list, $post_type) {
        if ($post_type !== 'attachment') {
            return $url_list;
        }

        foreach ($url_list as &$url_item) {
            $attachment_id = url_to_postid($url_item['loc']);
            if (!$attachment_id) {
                continue;
            }

            $file_path = get_attached_file($attachment_id);
            if (light_fixture_webp_exists($file_path)) {
                $webp_url = light_fixture_get_optimized_image_url(wp_get_attachment_url($attachment_id));

                // 添加WebP版本信息
                $url_item['webp_available'] = true;
                $url_item['webp_url'] = $webp_url;
            }
        }

        return $url_list;
    }

    /**
     * 添加Core Web Vitals优化
     */
    public function add_web_vitals_optimization() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        ?>
        <script>
        // WebP Core Web Vitals优化
        (function() {
            'use strict';

            // 图片懒加载优化
            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            if (img.dataset.webp && window.webpDetector && window.webpDetector.isWebPSupported()) {
                                img.src = img.dataset.webp;
                            }
                            observer.unobserve(img);
                        }
                    });
                });

                document.querySelectorAll('img[data-webp]').forEach(img => {
                    imageObserver.observe(img);
                });
            }
        })();
        </script>
        <?php
    }

    /**
     * 添加搜索引擎提示
     */
    public function add_search_engine_hints() {
        echo "<!-- Search Engine Optimization -->\n";
        echo '<meta name="robots" content="index, follow, max-image-preview:large">' . "\n";

        if (light_fixture_is_webp_browser_supported()) {
            echo '<meta name="image-format" content="webp">' . "\n";
            echo '<meta name="performance-optimized" content="true">' . "\n";
        }
    }

    /**
     * 优化AMP图片
     */
    public function optimize_amp_images($data) {
        if (!isset($data['post_amp_content'])) {
            return $data;
        }

        // 在AMP内容中替换图片为WebP版本
        $content = $data['post_amp_content'];

        $content = preg_replace_callback(
            '/<amp-img([^>]+)src=["\']([^"\']+)["\']([^>]*)>/i',
            function($matches) {
                $before_src = $matches[1];
                $src_url = $matches[2];
                $after_src = $matches[3];

                // 检查是否有WebP版本
                $webp_url = light_fixture_get_optimized_image_url($src_url);
                if ($webp_url !== $src_url) {
                    return '<amp-img' . $before_src . 'src="' . $webp_url . '"' . $after_src . '>';
                }

                return $matches[0];
            },
            $content
        );

        $data['post_amp_content'] = $content;
        return $data;
    }

    /**
     * 添加缓存优化头部
     */
    public function add_cache_optimization_headers() {
        if (!light_fixture_is_webp_browser_supported()) {
            return;
        }

        // 添加Vary头部用于缓存
        if (!headers_sent()) {
            header('Vary: Accept');
        }

        echo '<meta http-equiv="Accept-CH" content="DPR, Viewport-Width, Width">' . "\n";
    }

    /**
     * 估算加载时间改进
     */
    private function estimate_load_time_improvement($size_reduction_percent) {
        // 简单估算：文件大小减少的百分比 * 0.7 = 加载时间改进百分比
        return round($size_reduction_percent * 0.7, 1);
    }
}

// 延迟WebP转换的定时任务处理
add_action('light_fixture_delayed_webp_conversion', function($filename, $post_id) {
    if (file_exists($filename)) {
        $converter = light_fixture_get_webp_converter();
        $converter->convert_to_webp($filename);
        
        // 更新附件元数据
        $metadata = wp_get_attachment_metadata($post_id);
        if ($metadata) {
            wp_update_attachment_metadata($post_id, $metadata);
        }
    }
}, 10, 2);

// 初始化钩子集成
function light_fixture_init_webp_hooks_integration() {
    global $light_fixture_webp_hooks_integration;
    $light_fixture_webp_hooks_integration = new Light_Fixture_WebP_Hooks_Integration();
}

// 在WordPress初始化时启动钩子集成
add_action('init', 'light_fixture_init_webp_hooks_integration');
