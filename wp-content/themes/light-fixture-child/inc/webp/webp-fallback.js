/**
 * WebP回退策略JavaScript
 * 实现15秒超时回退机制
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * WebP回退管理器
     */
    class WebPFallbackManager {
        constructor() {
            this.timeout = webpFallback.timeout || 15000; // 15秒
            this.debug = webpFallback.debug || false;
            this.processedImages = new Set();
            this.failedImages = new Set();
            
            this.init();
        }
        
        /**
         * 初始化
         */
        init() {
            // 等待DOM加载完成
            $(document).ready(() => {
                this.processImages();
                this.observeNewImages();
            });
        }
        
        /**
         * 处理页面中的所有WebP图片
         */
        processImages() {
            const images = document.querySelectorAll('img.webp-with-fallback[data-fallback]');
            
            if (this.debug) {
                console.log(`WebP Fallback: 找到 ${images.length} 个需要处理的图片`);
            }
            
            images.forEach(img => this.setupImageFallback(img));
        }
        
        /**
         * 为单个图片设置回退机制
         */
        setupImageFallback(img) {
            if (this.processedImages.has(img)) {
                return;
            }
            
            this.processedImages.add(img);
            
            const originalSrc = img.src;
            const fallbackSrc = img.dataset.fallback;
            
            if (!fallbackSrc) {
                return;
            }
            
            // 设置加载状态
            img.classList.add('webp-loading');
            
            // 创建超时定时器
            const timeoutId = setTimeout(() => {
                this.fallbackToOriginal(img, originalSrc, fallbackSrc, 'timeout');
            }, this.timeout);
            
            // 监听加载成功
            const onLoad = () => {
                clearTimeout(timeoutId);
                img.classList.remove('webp-loading');
                img.classList.add('webp-loaded');
                
                if (this.debug) {
                    console.log(`WebP Fallback: WebP图片加载成功 - ${originalSrc}`);
                }
                
                // 清理事件监听器
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            // 监听加载失败
            const onError = () => {
                clearTimeout(timeoutId);
                this.fallbackToOriginal(img, originalSrc, fallbackSrc, 'error');
                
                // 清理事件监听器
                img.removeEventListener('load', onLoad);
                img.removeEventListener('error', onError);
            };
            
            // 添加事件监听器
            img.addEventListener('load', onLoad);
            img.addEventListener('error', onError);
            
            // 如果图片已经加载完成（缓存情况）
            if (img.complete) {
                if (img.naturalWidth > 0) {
                    onLoad();
                } else {
                    onError();
                }
            }
        }
        
        /**
         * 回退到原始图片
         */
        fallbackToOriginal(img, originalSrc, fallbackSrc, reason) {
            this.failedImages.add(originalSrc);
            
            img.classList.remove('webp-loading');
            img.classList.add('webp-fallback');
            
            // 切换到回退图片
            img.src = fallbackSrc;
            
            if (this.debug) {
                console.log(`WebP Fallback: 回退到原始图片 (${reason}) - ${originalSrc} -> ${fallbackSrc}`);
            }
            
            // 触发自定义事件
            const event = new CustomEvent('webpFallback', {
                detail: {
                    originalSrc: originalSrc,
                    fallbackSrc: fallbackSrc,
                    reason: reason,
                    element: img
                }
            });
            
            img.dispatchEvent(event);
        }
        
        /**
         * 观察新添加的图片（用于动态内容）
         */
        observeNewImages() {
            if (!window.MutationObserver) {
                return;
            }
            
            const observer = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查新添加的图片
                            if (node.tagName === 'IMG' && 
                                node.classList.contains('webp-with-fallback') && 
                                node.dataset.fallback) {
                                this.setupImageFallback(node);
                            }
                            
                            // 检查新添加元素内的图片
                            const images = node.querySelectorAll('img.webp-with-fallback[data-fallback]');
                            images.forEach(img => this.setupImageFallback(img));
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        /**
         * 获取统计信息
         */
        getStats() {
            return {
                processed: this.processedImages.size,
                failed: this.failedImages.size,
                successRate: this.processedImages.size > 0 ? 
                    ((this.processedImages.size - this.failedImages.size) / this.processedImages.size * 100).toFixed(2) + '%' : 
                    '0%'
            };
        }
    }
    
    // 初始化回退管理器
    const fallbackManager = new WebPFallbackManager();
    
    // 暴露到全局作用域（用于调试）
    window.webpFallbackManager = fallbackManager;
    
    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .webp-loading {
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }
        
        .webp-loaded {
            opacity: 1;
        }
        
        .webp-fallback {
            opacity: 1;
        }
        
        /* 可选：为回退图片添加视觉指示 */
        .webp-fallback::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 0;
            height: 0;
            border-left: 10px solid transparent;
            border-top: 10px solid #ff9800;
            pointer-events: none;
            opacity: 0.7;
        }
    `;
    document.head.appendChild(style);
    
})(jQuery);
