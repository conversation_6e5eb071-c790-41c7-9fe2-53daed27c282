/**
 * WebP Admin Page Styles
 * WebP管理页面样式
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

/* 管理页面布局 */
.webp-admin-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
}

.webp-admin-section h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

/* 状态卡片网格 */
.webp-status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.webp-status-card {
    background: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    text-align: center;
}

.webp-status-card h3 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.webp-status-value {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
}

.webp-status-value.success {
    color: #46b450;
}

.webp-status-value.error {
    color: #dc3232;
}

.webp-status-detail {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* 批量转换区域 */
.webp-bulk-convert {
    max-width: 800px;
}

.webp-convert-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
    padding: 15px;
    background: #f9f9f9;
    border-radius: 4px;
}

.webp-convert-options label {
    display: flex;
    flex-direction: column;
    font-weight: 500;
}

.webp-convert-options select {
    margin-top: 5px;
    padding: 5px;
}

.webp-convert-options input[type="checkbox"] {
    margin-right: 8px;
    margin-top: 0;
}

.webp-convert-actions {
    margin: 20px 0;
}

.webp-convert-actions .button {
    margin-right: 10px;
}

/* 进度条 */
#webp-bulk-progress {
    margin: 20px 0;
    padding: 20px;
    background: #f0f0f1;
    border-radius: 4px;
    border-left: 4px solid #0073aa;
}

.webp-progress-bar {
    width: 100%;
    height: 20px;
    background: #ddd;
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.webp-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #0073aa, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.webp-progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 10px 0;
    font-weight: 500;
}

.webp-progress-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin-top: 15px;
    font-size: 14px;
}

.webp-progress-details > div {
    padding: 8px;
    background: #fff;
    border-radius: 3px;
    text-align: center;
}

/* 日志区域 */
#webp-bulk-log {
    margin: 20px 0;
}

.webp-log-content {
    max-height: 300px;
    overflow-y: auto;
    background: #1e1e1e;
    color: #fff;
    padding: 15px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
}

.webp-log-entry {
    margin: 2px 0;
    padding: 2px 0;
}

.webp-log-success {
    color: #46b450;
}

.webp-log-error {
    color: #dc3232;
}

.webp-log-warning {
    color: #ffb900;
}

/* 维护工具 */
.webp-maintenance-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.webp-tool {
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fafafa;
}

.webp-tool h3 {
    margin-top: 0;
    color: #0073aa;
}

.webp-tool p {
    color: #666;
    font-size: 14px;
}

/* 使用指南 */
.webp-usage-guide {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.webp-guide-item {
    padding: 15px;
    border-left: 4px solid #0073aa;
    background: #f9f9f9;
}

.webp-guide-item h3 {
    margin-top: 0;
    color: #0073aa;
}

.webp-guide-item p {
    margin-bottom: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.webp-guide-item code {
    background: #e1e1e1;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

/* 系统信息表格 */
.webp-system-info .widefat {
    margin-top: 0;
}

.webp-system-info .widefat td {
    padding: 10px;
    vertical-align: top;
}

.webp-system-info .widefat td:first-child {
    width: 200px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .webp-status-grid {
        grid-template-columns: 1fr;
    }
    
    .webp-convert-options {
        grid-template-columns: 1fr;
    }
    
    .webp-progress-details {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .webp-maintenance-tools {
        grid-template-columns: 1fr;
    }
    
    .webp-usage-guide {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .webp-progress-details {
        grid-template-columns: 1fr;
    }
    
    .webp-progress-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}

/* 动画效果 */
.webp-status-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.webp-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.webp-tool {
    transition: border-color 0.2s ease;
}

.webp-tool:hover {
    border-color: #0073aa;
}

/* 加载状态 */
.webp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.webp-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #0073aa;
    border-top-color: transparent;
    border-radius: 50%;
    animation: webp-spin 1s linear infinite;
}

@keyframes webp-spin {
    to {
        transform: rotate(360deg);
    }
}

/* 通知样式增强 */
.webp-admin-section .notice {
    margin: 15px 0;
}

.webp-admin-section .notice p {
    margin: 0.5em 0;
}

/* 按钮状态 */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 工具提示 */
.webp-tooltip {
    position: relative;
    cursor: help;
}

.webp-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.webp-tooltip:hover::after {
    opacity: 1;
}
