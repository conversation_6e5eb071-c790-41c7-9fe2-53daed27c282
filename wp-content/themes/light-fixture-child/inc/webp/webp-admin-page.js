/**
 * WebP Admin Page JavaScript
 * WebP管理页面JavaScript
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

(function($) {
    'use strict';
    
    /**
     * WebP管理页面类
     */
    class WebPAdminPage {
        constructor() {
            this.isConverting = false;
            this.convertedTotal = 0;
            this.processedTotal = 0;
            this.skippedTotal = 0;
            this.errorTotal = 0;
            this.totalImages = 0;
            
            this.init();
        }
        
        /**
         * 初始化
         */
        init() {
            this.bindEvents();
            this.loadInitialStats();
        }
        
        /**
         * 绑定事件
         */
        bindEvents() {
            // 批量转换按钮
            $('#webp-start-bulk-convert').on('click', this.startBulkConvert.bind(this));
            $('#webp-stop-bulk-convert').on('click', this.stopBulkConvert.bind(this));
            
            // 维护工具
            $('#webp-cleanup-orphaned').on('click', this.cleanupOrphaned.bind(this));
            $('#webp-refresh-stats').on('click', this.refreshStats.bind(this));
        }
        
        /**
         * 加载初始统计信息
         */
        loadInitialStats() {
            $.ajax({
                url: webpAdminPage.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'webp_get_stats',
                    nonce: webpAdminPage.nonce
                },
                success: (response) => {
                    if (response.success) {
                        this.totalImages = response.data.total_images;
                    }
                }
            });
        }
        
        /**
         * 开始批量转换
         */
        startBulkConvert() {
            if (this.isConverting) {
                return;
            }
            
            const batchSize = parseInt($('#webp-batch-size').val());
            const quality = parseInt($('#webp-quality').val());
            const force = $('#webp-force-convert').is(':checked');
            
            if (!confirm(webpAdminPage.strings.confirm_bulk)) {
                return;
            }
            
            this.isConverting = true;
            this.convertedTotal = 0;
            this.processedTotal = 0;
            this.skippedTotal = 0;
            this.errorTotal = 0;
            
            // 更新UI
            $('#webp-start-bulk-convert').hide();
            $('#webp-stop-bulk-convert').show();
            $('#webp-bulk-progress').show();
            $('#webp-bulk-log').show();
            
            this.updateProgress(0, '开始批量转换...');
            this.clearLog();
            
            // 开始转换
            this.processBatch(0, batchSize, quality, force);
        }
        
        /**
         * 停止批量转换
         */
        stopBulkConvert() {
            this.isConverting = false;
            
            $('#webp-start-bulk-convert').show();
            $('#webp-stop-bulk-convert').hide();
            
            this.updateProgress(this.processedTotal, '转换已停止');
            this.addLog('转换已手动停止', 'warning');
        }
        
        /**
         * 处理批次
         */
        processBatch(offset, batchSize, quality, force) {
            if (!this.isConverting) {
                return;
            }
            
            $.ajax({
                url: webpAdminPage.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'webp_bulk_convert',
                    nonce: webpAdminPage.nonce,
                    batch_size: batchSize,
                    quality: quality,
                    offset: offset,
                    force: force
                },
                success: (response) => {
                    if (response.success) {
                        const data = response.data;
                        
                        this.processedTotal += data.processed;
                        this.convertedTotal += data.converted;
                        this.skippedTotal += data.skipped;
                        this.errorTotal += data.errors;
                        
                        // 更新进度
                        const progress = this.totalImages > 0 ? 
                            Math.round((this.processedTotal / this.totalImages) * 100) : 0;
                        
                        this.updateProgress(progress, 
                            `已处理 ${this.processedTotal} 张图片，转换 ${this.convertedTotal} 张`
                        );
                        
                        this.updateProgressDetails();
                        
                        // 记录日志
                        if (data.converted > 0) {
                            this.addLog(`批次完成：转换 ${data.converted} 张图片`, 'success');
                        }
                        
                        if (data.error_messages.length > 0) {
                            data.error_messages.forEach(msg => {
                                this.addLog(`错误：${msg}`, 'error');
                            });
                        }
                        
                        // 继续下一批或完成
                        if (data.has_more && this.isConverting) {
                            setTimeout(() => {
                                this.processBatch(offset + batchSize, batchSize, quality, force);
                            }, 1000);
                        } else {
                            this.completeBulkConvert();
                        }
                    } else {
                        this.addLog(`批次处理失败：${response.data}`, 'error');
                        this.completeBulkConvert();
                    }
                },
                error: () => {
                    this.addLog('网络请求失败，请检查网络连接', 'error');
                    this.completeBulkConvert();
                }
            });
        }
        
        /**
         * 完成批量转换
         */
        completeBulkConvert() {
            this.isConverting = false;
            
            $('#webp-start-bulk-convert').show();
            $('#webp-stop-bulk-convert').hide();
            
            const finalProgress = this.totalImages > 0 ? 
                Math.round((this.processedTotal / this.totalImages) * 100) : 100;
            
            this.updateProgress(finalProgress, '批量转换完成');
            
            this.addLog(
                `转换完成！总计处理 ${this.processedTotal} 张图片，` +
                `成功转换 ${this.convertedTotal} 张，` +
                `跳过 ${this.skippedTotal} 张，` +
                `错误 ${this.errorTotal} 张`,
                'success'
            );
            
            // 刷新统计信息
            setTimeout(() => {
                this.refreshStats();
            }, 2000);
        }
        
        /**
         * 更新进度条
         */
        updateProgress(percent, text) {
            $('.webp-progress-fill').css('width', percent + '%');
            $('#webp-progress-text').text(text);
            $('#webp-progress-percent').text(percent + '%');
        }
        
        /**
         * 更新进度详情
         */
        updateProgressDetails() {
            $('#webp-processed-count').text(this.processedTotal);
            $('#webp-converted-count').text(this.convertedTotal);
            $('#webp-skipped-count').text(this.skippedTotal);
            $('#webp-error-count').text(this.errorTotal);
        }
        
        /**
         * 添加日志
         */
        addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logClass = `webp-log-${type}`;
            const logEntry = `<div class="webp-log-entry ${logClass}">[${timestamp}] ${message}</div>`;
            
            $('.webp-log-content').append(logEntry);
            
            // 自动滚动到底部
            const logContent = $('.webp-log-content')[0];
            logContent.scrollTop = logContent.scrollHeight;
        }
        
        /**
         * 清空日志
         */
        clearLog() {
            $('.webp-log-content').empty();
        }
        
        /**
         * 清理孤立文件
         */
        cleanupOrphaned() {
            if (!confirm(webpAdminPage.strings.confirm_cleanup)) {
                return;
            }
            
            const button = $('#webp-cleanup-orphaned');
            const result = $('#webp-cleanup-result');
            
            button.prop('disabled', true).text('清理中...');
            result.hide();
            
            $.ajax({
                url: webpAdminPage.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'webp_cleanup_orphaned',
                    nonce: webpAdminPage.nonce
                },
                success: (response) => {
                    if (response.success) {
                        result.html(`<div class="notice notice-success"><p>${response.data.message}</p></div>`).show();
                    } else {
                        result.html(`<div class="notice notice-error"><p>清理失败：${response.data}</p></div>`).show();
                    }
                },
                error: () => {
                    result.html('<div class="notice notice-error"><p>清理请求失败</p></div>').show();
                },
                complete: () => {
                    button.prop('disabled', false).text('清理孤立文件');
                }
            });
        }
        
        /**
         * 刷新统计信息
         */
        refreshStats() {
            const button = $('#webp-refresh-stats');
            button.prop('disabled', true).text('刷新中...');
            
            $.ajax({
                url: webpAdminPage.ajaxUrl,
                type: 'POST',
                data: {
                    action: 'webp_get_stats',
                    nonce: webpAdminPage.nonce
                },
                success: (response) => {
                    if (response.success) {
                        // 更新页面统计信息
                        location.reload();
                    }
                },
                error: () => {
                    alert('刷新统计信息失败');
                },
                complete: () => {
                    button.prop('disabled', false).text('刷新统计');
                }
            });
        }
    }
    
    // 页面加载完成后初始化
    $(document).ready(function() {
        if ($('.wrap').find('h1').text().includes('WebP转换管理')) {
            new WebPAdminPage();
        }
    });
    
})(jQuery);
