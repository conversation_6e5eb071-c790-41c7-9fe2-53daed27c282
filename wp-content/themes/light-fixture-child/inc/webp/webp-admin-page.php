<?php
/**
 * WebP Admin Management Page
 * WebP管理界面页面
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP管理页面类
 */
class Light_Fixture_WebP_Admin_Page {
    
    /**
     * 构造函数
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_assets'));
        add_action('wp_ajax_webp_bulk_convert', array($this, 'handle_bulk_convert'));
        add_action('wp_ajax_webp_get_stats', array($this, 'handle_get_stats'));
        add_action('wp_ajax_webp_cleanup_orphaned', array($this, 'handle_cleanup_orphaned'));
    }
    
    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_management_page(
            'WebP转换管理',
            'WebP转换',
            'manage_options',
            'webp-converter',
            array($this, 'render_admin_page')
        );
    }
    
    /**
     * 加载管理页面资源
     */
    public function enqueue_admin_assets($hook) {
        if ($hook !== 'tools_page_webp-converter') {
            return;
        }
        
        wp_enqueue_script(
            'webp-admin-page',
            get_stylesheet_directory_uri() . '/inc/webp/webp-admin-page.js',
            array('jquery'),
            '1.0.0',
            true
        );
        
        wp_enqueue_style(
            'webp-admin-page',
            get_stylesheet_directory_uri() . '/inc/webp/webp-admin-page.css',
            array(),
            '1.0.0'
        );
        
        wp_localize_script('webp-admin-page', 'webpAdminPage', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('webp_admin_nonce'),
            'strings' => array(
                'converting' => '转换中...',
                'completed' => '转换完成',
                'error' => '转换出错',
                'confirm_bulk' => '确定要批量转换所有历史图片吗？这可能需要较长时间。',
                'confirm_cleanup' => '确定要清理孤立的WebP文件吗？此操作不可撤销。'
            )
        ));
    }
    
    /**
     * 渲染管理页面
     */
    public function render_admin_page() {
        $stats = light_fixture_get_webp_statistics();
        $converter = light_fixture_get_webp_converter();
        $webp_supported = $converter->is_webp_supported();
        $preferred_editor = $converter->get_preferred_editor();
        
        ?>
        <div class="wrap">
            <h1>WebP转换管理</h1>
            
            <?php if (!$webp_supported): ?>
            <div class="notice notice-error">
                <p><strong>警告：</strong>服务器不支持WebP转换。请联系主机商启用GD WebP支持或安装Imagick扩展。</p>
            </div>
            <?php endif; ?>
            
            <!-- 系统状态 -->
            <div class="webp-admin-section">
                <h2>系统状态</h2>
                <div class="webp-status-grid">
                    <div class="webp-status-card">
                        <h3>WebP支持</h3>
                        <div class="webp-status-value <?php echo $webp_supported ? 'success' : 'error'; ?>">
                            <?php echo $webp_supported ? '✅ 支持' : '❌ 不支持'; ?>
                        </div>
                        <div class="webp-status-detail">
                            推荐引擎: <?php echo $preferred_editor ?: '无'; ?>
                        </div>
                    </div>
                    
                    <div class="webp-status-card">
                        <h3>总图片数</h3>
                        <div class="webp-status-value"><?php echo number_format($stats['total_images']); ?></div>
                        <div class="webp-status-detail">媒体库中的图片文件</div>
                    </div>
                    
                    <div class="webp-status-card">
                        <h3>已转换</h3>
                        <div class="webp-status-value success"><?php echo number_format($stats['webp_converted']); ?></div>
                        <div class="webp-status-detail">转换率: <?php echo $stats['conversion_rate']; ?>%</div>
                    </div>
                    
                    <div class="webp-status-card">
                        <h3>空间节省</h3>
                        <div class="webp-status-value success"><?php echo $stats['total_savings_formatted']; ?></div>
                        <div class="webp-status-detail">
                            原始: <?php echo $stats['total_original_size_formatted']; ?><br>
                            WebP: <?php echo $stats['total_webp_size_formatted']; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 批量转换 -->
            <div class="webp-admin-section">
                <h2>批量转换</h2>
                <div class="webp-bulk-convert">
                    <p>将所有历史图片转换为WebP格式。此操作会跳过已转换的图片。</p>
                    
                    <div class="webp-convert-options">
                        <label>
                            批次大小:
                            <select id="webp-batch-size">
                                <option value="10">10 (推荐用于共享主机)</option>
                                <option value="20" selected>20 (推荐用于VPS)</option>
                                <option value="50">50 (推荐用于独立服务器)</option>
                                <option value="100">100 (高性能服务器)</option>
                            </select>
                        </label>
                        
                        <label>
                            WebP质量:
                            <select id="webp-quality">
                                <option value="70">70 (较小文件)</option>
                                <option value="80">80 (平衡)</option>
                                <option value="85" selected>85 (推荐)</option>
                                <option value="90">90 (高质量)</option>
                                <option value="95">95 (最高质量)</option>
                            </select>
                        </label>
                        
                        <label>
                            <input type="checkbox" id="webp-force-convert">
                            强制重新转换已存在的WebP文件
                        </label>
                    </div>
                    
                    <div class="webp-convert-actions">
                        <button type="button" id="webp-start-bulk-convert" class="button button-primary" <?php echo !$webp_supported ? 'disabled' : ''; ?>>
                            开始批量转换
                        </button>
                        <button type="button" id="webp-stop-bulk-convert" class="button" style="display: none;">
                            停止转换
                        </button>
                    </div>
                    
                    <div id="webp-bulk-progress" style="display: none;">
                        <h3>转换进度</h3>
                        <div class="webp-progress-bar">
                            <div class="webp-progress-fill"></div>
                        </div>
                        <div class="webp-progress-info">
                            <span id="webp-progress-text">准备开始...</span>
                            <span id="webp-progress-percent">0%</span>
                        </div>
                        <div class="webp-progress-details">
                            <div>已处理: <span id="webp-processed-count">0</span></div>
                            <div>成功转换: <span id="webp-converted-count">0</span></div>
                            <div>跳过: <span id="webp-skipped-count">0</span></div>
                            <div>错误: <span id="webp-error-count">0</span></div>
                        </div>
                    </div>
                    
                    <div id="webp-bulk-log" style="display: none;">
                        <h3>转换日志</h3>
                        <div class="webp-log-content"></div>
                    </div>
                </div>
            </div>
            
            <!-- 维护工具 -->
            <div class="webp-admin-section">
                <h2>维护工具</h2>
                <div class="webp-maintenance-tools">
                    <div class="webp-tool">
                        <h3>清理孤立文件</h3>
                        <p>删除没有对应原始文件的WebP文件。</p>
                        <button type="button" id="webp-cleanup-orphaned" class="button">清理孤立文件</button>
                        <div id="webp-cleanup-result" style="display: none;"></div>
                    </div>
                    
                    <div class="webp-tool">
                        <h3>刷新统计</h3>
                        <p>重新计算WebP转换统计信息。</p>
                        <button type="button" id="webp-refresh-stats" class="button">刷新统计</button>
                    </div>
                    
                    <div class="webp-tool">
                        <h3>测试功能</h3>
                        <p>运行WebP功能测试套件。</p>
                        <a href="<?php echo admin_url('?run_webp_tests=1'); ?>" class="button" target="_blank">运行测试</a>
                    </div>
                </div>
            </div>
            
            <!-- 使用指南 -->
            <div class="webp-admin-section">
                <h2>使用指南</h2>
                <div class="webp-usage-guide">
                    <div class="webp-guide-item">
                        <h3>🔄 自动转换</h3>
                        <p>新上传的图片会自动生成WebP版本，无需手动操作。</p>
                    </div>
                    
                    <div class="webp-guide-item">
                        <h3>🌐 浏览器兼容</h3>
                        <p>系统会自动检测浏览器支持，不支持WebP的浏览器会显示原始图片。</p>
                    </div>
                    
                    <div class="webp-guide-item">
                        <h3>📊 性能提升</h3>
                        <p>WebP格式通常比JPEG小30-50%，比PNG小80%以上，显著提升页面加载速度。</p>
                    </div>
                    
                    <div class="webp-guide-item">
                        <h3>💻 命令行工具</h3>
                        <p>对于大量图片，推荐使用WP-CLI命令: <code>wp webp convert</code></p>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="webp-admin-section">
                <h2>系统信息</h2>
                <div class="webp-system-info">
                    <table class="widefat">
                        <tbody>
                            <tr>
                                <td><strong>PHP版本</strong></td>
                                <td><?php echo PHP_VERSION; ?></td>
                            </tr>
                            <tr>
                                <td><strong>WordPress版本</strong></td>
                                <td><?php echo get_bloginfo('version'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>GD扩展</strong></td>
                                <td><?php echo extension_loaded('gd') ? '✅ 已安装' : '❌ 未安装'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>Imagick扩展</strong></td>
                                <td><?php echo extension_loaded('imagick') ? '✅ 已安装' : '❌ 未安装'; ?></td>
                            </tr>
                            <tr>
                                <td><strong>内存限制</strong></td>
                                <td><?php echo ini_get('memory_limit'); ?></td>
                            </tr>
                            <tr>
                                <td><strong>最大执行时间</strong></td>
                                <td><?php echo ini_get('max_execution_time'); ?>秒</td>
                            </tr>
                            <tr>
                                <td><strong>上传目录</strong></td>
                                <td><?php 
                                    $upload_dir = wp_upload_dir();
                                    echo $upload_dir['basedir'];
                                    echo is_writable($upload_dir['basedir']) ? ' (可写)' : ' (不可写)';
                                ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * 处理批量转换AJAX请求
     */
    public function handle_bulk_convert() {
        check_ajax_referer('webp_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('权限不足');
        }
        
        $batch_size = intval($_POST['batch_size']) ?: 20;
        $quality = intval($_POST['quality']) ?: 85;
        $offset = intval($_POST['offset']) ?: 0;
        $force = isset($_POST['force']) && $_POST['force'] === 'true';
        
        global $wpdb;
        
        // 获取图片附件
        $attachments = $wpdb->get_results($wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_type = 'attachment' 
             AND post_mime_type IN ('image/jpeg', 'image/png', 'image/gif')
             ORDER BY ID ASC
             LIMIT %d OFFSET %d",
            $batch_size,
            $offset
        ));
        
        $results = array(
            'processed' => 0,
            'converted' => 0,
            'skipped' => 0,
            'errors' => 0,
            'error_messages' => array(),
            'has_more' => count($attachments) === $batch_size
        );
        
        $converter = light_fixture_get_webp_converter();
        
        foreach ($attachments as $attachment) {
            $file_path = get_attached_file($attachment->ID);
            
            if (!$file_path || !file_exists($file_path)) {
                continue;
            }
            
            $results['processed']++;
            
            // 检查是否需要转换
            if (!$force && light_fixture_webp_exists($file_path)) {
                $results['skipped']++;
                continue;
            }
            
            // 执行转换
            $result = $converter->convert_to_webp($file_path, null, $quality);
            
            if (is_wp_error($result)) {
                $results['errors']++;
                $results['error_messages'][] = basename($file_path) . ': ' . $result->get_error_message();
            } else {
                $results['converted']++;
            }
        }
        
        wp_send_json_success($results);
    }
    
    /**
     * 处理获取统计信息AJAX请求
     */
    public function handle_get_stats() {
        check_ajax_referer('webp_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('权限不足');
        }
        
        $stats = light_fixture_get_webp_statistics();
        wp_send_json_success($stats);
    }
    
    /**
     * 处理清理孤立文件AJAX请求
     */
    public function handle_cleanup_orphaned() {
        check_ajax_referer('webp_admin_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_send_json_error('权限不足');
        }
        
        $cleaned_count = light_fixture_cleanup_orphaned_webp();
        
        wp_send_json_success(array(
            'cleaned_count' => $cleaned_count,
            'message' => "成功清理了 $cleaned_count 个孤立的WebP文件"
        ));
    }
}

// 初始化管理页面
new Light_Fixture_WebP_Admin_Page();
