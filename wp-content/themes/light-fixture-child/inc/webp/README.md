# WebP 自动转换功能模块

## 模块概述

本模块为 Light Fixture 灯饰展示网站提供图片自动转换WebP功能，旨在提升页面加载速度和SEO表现。

## 技术架构

### 主题环境
- **子主题**: light-fixture-child
- **父主题**: twentytwentyfive (Twenty Twenty-Five v1.2)
- **WordPress版本**: 6.x+
- **PHP版本**: 8.x+

### 模块文件结构
```
wp-content/themes/light-fixture-child/inc/webp/
├── README.md                   # 本文档
├── webp-converter.php          # 核心转换引擎
├── webp-browser-detection.php  # 浏览器兼容性检测
├── webp-admin.php             # WordPress管理界面
├── webp-cli.php               # WP-CLI命令行工具
└── webp-utils.php             # 工具函数库
```

## 功能特性

### 核心功能
1. **自动转换**: 新图片上传时自动生成WebP副本
2. **批量处理**: 支持历史图片批量转换
3. **智能选择**: 根据浏览器支持自动返回最优格式
4. **存储优化**: 自动清理和空间管理

### 支持的图片格式
- **输入格式**: JPEG, PNG, GIF
- **输出格式**: WebP (保留原始格式)
- **质量控制**: 可配置压缩质量 (默认85%)

### 自定义图片尺寸支持
- `product-thumbnail`: 600x600 (裁剪)
- `product-gallery`: 1200x800 (缩放)
- `hero-background`: 1920x1080 (裁剪)
- 所有WordPress默认尺寸

## 技术实现

### WordPress钩子集成
- `wp_generate_attachment_metadata`: 新图片自动转换
- `wp_get_attachment_image_src`: 智能URL重写
- `wp_handle_upload`: 上传处理集成

### 浏览器兼容性检测
- **前端检测**: JavaScript + Cookie缓存
- **服务端判断**: User-Agent + 功能检测
- **降级机制**: 自动返回原始格式

### 性能优化
- **批量处理**: 避免服务器过载
- **内存控制**: 大图片分块处理
- **缓存机制**: 转换结果缓存
- **错误恢复**: 断点续传支持

## 配置选项

### 默认配置
```php
$webp_config = array(
    'quality' => 85,                    // WebP质量 (1-100)
    'batch_size' => 50,                 // 批量处理大小
    'memory_limit' => '256M',           // 内存限制
    'timeout' => 300,                   // 超时时间(秒)
    'enable_logging' => true,           // 启用日志
    'auto_cleanup' => true,             // 自动清理
    'browser_cache_days' => 30          // 浏览器检测缓存天数
);
```

### 可配置项目
- WebP压缩质量
- 批量处理大小
- 内存和超时限制
- 日志记录级别
- 自动清理策略

## 兼容性

### 主题兼容性
- ✅ Twenty Twenty-Five 父主题
- ✅ light-fixture-child 子主题
- ✅ 自定义图片尺寸
- ✅ 响应式图片 (srcset)

### 插件兼容性
- ✅ Rank Math SEO
- ✅ Yoast SEO
- ✅ WordPress媒体库
- ✅ 图片懒加载插件

### 浏览器支持
- ✅ Chrome 23+
- ✅ Firefox 65+
- ✅ Safari 14+
- ✅ Edge 18+
- ✅ 自动降级到原始格式

## 安装和使用

### 自动安装
模块将自动集成到子主题的 `functions.php` 中，无需手动配置。

### 手动激活
```php
// 在 functions.php 中添加
require_once LIGHT_FIXTURE_CHILD_THEME_DIR . '/inc/webp/webp-converter.php';
```

### WP-CLI 使用
```bash
# 批量转换历史图片
wp webp convert --batch-size=50 --quality=85

# 检查转换状态
wp webp status

# 清理WebP文件
wp webp cleanup
```

### 管理界面
访问 WordPress 后台 → 工具 → WebP转换 进行可视化管理。

## 监控和维护

### 日志文件
- 位置: `wp-content/debug.log`
- 格式: `[WebP] 时间戳 - 操作 - 状态 - 详情`

### 性能监控
- 转换成功率统计
- 文件大小节省统计
- 处理时间监控
- 错误率跟踪

### 故障排除
1. 检查服务器WebP支持
2. 验证文件权限
3. 查看错误日志
4. 测试浏览器兼容性

## 开发信息

### 版本历史
- v1.0.0: 初始版本，基础WebP转换功能

### 开发规范
- 遵循WordPress编码标准
- PSR-12 PHP编码规范
- 完整的错误处理
- 详细的代码注释

### 测试要求
- 单元测试覆盖
- 集成测试验证
- 性能测试评估
- 兼容性测试确认

---

**开发状态**: 开发中
**最后更新**: 2025-07-27
**维护者**: Light Fixture Development Team
