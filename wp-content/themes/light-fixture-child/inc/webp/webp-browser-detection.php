<?php
/**
 * WebP Browser Detection System
 * WebP浏览器兼容性检测系统
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WebP浏览器检测类
 */
class Light_Fixture_WebP_Browser_Detection {
    
    /**
     * Cookie名称
     */
    const WEBP_SUPPORT_COOKIE = 'webp_support';
    
    /**
     * Cookie过期时间（天）
     */
    const COOKIE_EXPIRE_DAYS = 30;
    
    /**
     * 支持WebP的浏览器User-Agent模式
     */
    private $webp_user_agents = array(
        'Chrome' => '/Chrome\/([0-9]+)/',           // Chrome 23+
        'Firefox' => '/Firefox\/([0-9]+)/',         // Firefox 65+
        'Safari' => '/Version\/([0-9]+).*Safari/',  // Safari 14+
        'Edge' => '/Edg\/([0-9]+)/',               // Edge 18+
        'Opera' => '/OPR\/([0-9]+)/',              // Opera 19+
        'Android' => '/Android.*Chrome\/([0-9]+)/', // Android Chrome
    );
    
    /**
     * 最低支持版本
     */
    private $min_versions = array(
        'Chrome' => 23,
        'Firefox' => 65,
        'Safari' => 14,
        'Edge' => 18,
        'Opera' => 19,
        'Android' => 50,
    );
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->init_hooks();
        $this->init_client_hints();
    }

    /**
     * 初始化Client Hints支持
     */
    private function init_client_hints() {
        // 添加Client Hints头部请求
        add_action('wp_head', array($this, 'add_client_hints_headers'), 1);
        add_action('send_headers', array($this, 'send_client_hints_headers'));
    }

    /**
     * 添加Client Hints头部到HTML
     */
    public function add_client_hints_headers() {
        // 请求浏览器发送更多的客户端信息
        echo '<meta http-equiv="Accept-CH" content="Sec-CH-UA, Sec-CH-UA-Mobile, Sec-CH-UA-Platform, Sec-CH-UA-Platform-Version">' . "\n";
        echo '<meta http-equiv="Accept-CH-Lifetime" content="86400">' . "\n"; // 24小时
    }

    /**
     * 发送Client Hints HTTP头部
     */
    public function send_client_hints_headers() {
        if (!headers_sent()) {
            header('Accept-CH: Sec-CH-UA, Sec-CH-UA-Mobile, Sec-CH-UA-Platform, Sec-CH-UA-Platform-Version');
            header('Accept-CH-Lifetime: 86400');
        }
    }
    
    /**
     * 初始化WordPress钩子
     */
    private function init_hooks() {
        // 在前端加载JavaScript检测脚本
        add_action('wp_enqueue_scripts', array($this, 'enqueue_detection_script'));

        // 处理AJAX检测请求
        add_action('wp_ajax_webp_detection', array($this, 'handle_webp_detection'));
        add_action('wp_ajax_nopriv_webp_detection', array($this, 'handle_webp_detection'));

        // 在页面头部添加内联样式
        add_action('wp_head', array($this, 'add_webp_detection_styles'), 1);

        // 缓存管理
        add_action('init', array($this, 'maybe_warm_up_cache'));
        add_action('wp_loaded', array($this, 'cleanup_old_stats'));

        // 定时任务
        add_action('webp_cache_cleanup', array($this, 'cleanup_old_stats'));
        if (!wp_next_scheduled('webp_cache_cleanup')) {
            wp_schedule_event(time(), 'daily', 'webp_cache_cleanup');
        }
    }

    /**
     * 可能执行缓存预热
     */
    public function maybe_warm_up_cache() {
        // 只在管理员访问时或特定条件下预热缓存
        if (is_admin() || (rand(1, 100) <= 5)) { // 5%的概率预热
            $this->warm_up_cache();
        }
    }
    
    /**
     * 加载WebP检测JavaScript脚本
     */
    public function enqueue_detection_script() {
        // 只在前端加载
        if (is_admin()) {
            return;
        }
        
        wp_enqueue_script(
            'light-fixture-webp-detection',
            get_stylesheet_directory_uri() . '/inc/webp/webp-detection.js',
            array(),
            '1.0.0',
            true
        );
        
        // 传递AJAX URL到JavaScript
        wp_localize_script('light-fixture-webp-detection', 'webpDetection', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('webp_detection_nonce'),
            'cookieName' => self::WEBP_SUPPORT_COOKIE,
            'cookieExpire' => self::COOKIE_EXPIRE_DAYS
        ));
    }
    
    /**
     * 添加WebP检测相关的CSS样式
     */
    public function add_webp_detection_styles() {
        echo "<style id='webp-detection-styles'>\n";
        echo "/* WebP检测初始状态 - 隐藏WebP图片 */\n";
        echo ".webp-image { display: none; }\n";
        echo ".fallback-image { display: block; }\n";
        echo "/* WebP支持时显示WebP图片 */\n";
        echo ".webp-supported .webp-image { display: block; }\n";
        echo ".webp-supported .fallback-image { display: none; }\n";
        echo "</style>\n";
    }
    
    /**
     * 检查当前浏览器是否支持WebP
     *
     * @return bool
     */
    public function is_webp_supported() {
        // 生成多维度缓存键
        $cache_key = $this->generate_enhanced_cache_key();

        // 首先检查对象缓存
        $cached_result = wp_cache_get($cache_key, 'webp_detection');
        if ($cached_result !== false) {
            // 缓存命中，记录统计信息
            $this->record_cache_hit('object_cache');
            return $cached_result === '1';
        }

        // 检查Cookie（次优选择）
        if (isset($_COOKIE[self::WEBP_SUPPORT_COOKIE])) {
            $cookie_result = $_COOKIE[self::WEBP_SUPPORT_COOKIE] === '1';

            // 将Cookie结果缓存到对象缓存中，缓存时间基于置信度
            $cache_duration = $this->calculate_cache_duration(0.8); // Cookie置信度80%
            wp_cache_set($cache_key, $cookie_result ? '1' : '0', 'webp_detection', $cache_duration);

            $this->record_cache_hit('cookie');
            return $cookie_result;
        }

        // 如果没有Cookie，使用增强的服务端检测
        $server_detection = $this->detect_webp_by_user_agent();

        // 根据检测置信度设置缓存时间
        $detection_info = $this->get_last_detection_info();
        $cache_duration = $this->calculate_cache_duration($detection_info['confidence'] ?? 0.7);

        // 缓存检测结果到对象缓存
        wp_cache_set($cache_key, $server_detection ? '1' : '0', 'webp_detection', $cache_duration);

        // 静默设置Cookie，避免刷新页面
        if (!headers_sent()) {
            $cookie_value = $server_detection ? '1' : '0';
            $cookie_expire = time() + (30 * 24 * 60 * 60);
            setcookie(self::WEBP_SUPPORT_COOKIE, $cookie_value, $cookie_expire, '/');
        }

        // 记录检测统计信息
        $this->record_detection_stats($detection_info);

        return $server_detection;
    }

    /**
     * 生成增强的缓存键
     *
     * @return string
     */
    private function generate_enhanced_cache_key() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $accept_header = $_SERVER['HTTP_ACCEPT'] ?? '';
        $sec_ch_ua = $_SERVER['HTTP_SEC_CH_UA'] ?? '';

        // 获取客户端IP的哈希（用于区分不同网络环境）
        $ip_hash = substr(md5($_SERVER['REMOTE_ADDR'] ?? ''), 0, 8);

        // 生成浏览器指纹
        $browser_fingerprint = $this->generate_browser_fingerprint($user_agent, $accept_header, $sec_ch_ua);

        return 'webp_support_v2_' . md5($browser_fingerprint . $ip_hash);
    }

    /**
     * 生成浏览器指纹
     *
     * @param string $user_agent
     * @param string $accept_header
     * @param string $sec_ch_ua
     * @return string
     */
    private function generate_browser_fingerprint($user_agent, $accept_header, $sec_ch_ua) {
        // 提取关键信息，忽略版本号的小版本
        $ua_normalized = preg_replace('/(\d+\.\d+)\.\d+/', '$1', $user_agent);

        // 标准化Accept头部
        $accept_normalized = strpos($accept_header, 'image/webp') !== false ? 'webp_supported' : 'webp_not_in_accept';

        // 提取Sec-CH-UA的主要浏览器信息
        $sec_ch_normalized = '';
        if (!empty($sec_ch_ua)) {
            if (preg_match('/"([^"]+)";v="(\d+)"/', $sec_ch_ua, $matches)) {
                $sec_ch_normalized = strtolower($matches[1]) . '_v' . $matches[2];
            }
        }

        return $ua_normalized . '|' . $accept_normalized . '|' . $sec_ch_normalized;
    }

    /**
     * 根据置信度和其他因素计算缓存时间
     *
     * @param float $confidence
     * @param array $context 额外的上下文信息
     * @return int
     */
    private function calculate_cache_duration($confidence, $context = array()) {
        $base_duration = $this->get_base_cache_duration($confidence);

        // 应用时间衰减因子
        $time_factor = $this->calculate_time_decay_factor();

        // 应用负载因子
        $load_factor = $this->calculate_load_factor();

        // 应用浏览器稳定性因子
        $stability_factor = $this->calculate_browser_stability_factor($context);

        // 计算最终缓存时间
        $final_duration = $base_duration * $time_factor * $load_factor * $stability_factor;

        // 确保在合理范围内
        return max(300, min($final_duration, 7 * 24 * 3600)); // 5分钟到7天
    }

    /**
     * 获取基础缓存时间
     */
    private function get_base_cache_duration($confidence) {
        if ($confidence >= 0.95) {
            return 48 * 3600; // 48小时
        } elseif ($confidence >= 0.90) {
            return 24 * 3600; // 24小时
        } elseif ($confidence >= 0.80) {
            return 12 * 3600; // 12小时
        } elseif ($confidence >= 0.70) {
            return 6 * 3600;  // 6小时
        } elseif ($confidence >= 0.60) {
            return 3 * 3600;  // 3小时
        } else {
            return 1 * 3600;  // 1小时
        }
    }

    /**
     * 计算时间衰减因子
     */
    private function calculate_time_decay_factor() {
        $hour = intval(date('H'));

        // 在高峰时间（9-18点）缓存时间稍短，以便更快响应变化
        if ($hour >= 9 && $hour <= 18) {
            return 0.8;
        }

        // 在低峰时间缓存时间更长
        return 1.2;
    }

    /**
     * 计算负载因子
     */
    private function calculate_load_factor() {
        // 简单的负载检测：基于当前缓存命中率
        $today_stats_key = 'webp_cache_stats_' . date('Y-m-d');
        $today_stats = wp_cache_get($today_stats_key, 'webp_stats') ?: array();

        $total_hits = array_sum($today_stats);

        if ($total_hits > 1000) {
            // 高负载时延长缓存时间
            return 1.5;
        } elseif ($total_hits > 100) {
            // 中等负载
            return 1.0;
        } else {
            // 低负载时缩短缓存时间，更快学习
            return 0.7;
        }
    }

    /**
     * 计算浏览器稳定性因子
     */
    private function calculate_browser_stability_factor($context) {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        // 检测是否为稳定的主流浏览器
        $stable_browsers = array('Chrome', 'Firefox', 'Safari', 'Edge');

        foreach ($stable_browsers as $browser) {
            if (strpos($user_agent, $browser) !== false) {
                // 主流浏览器，缓存时间更长
                return 1.3;
            }
        }

        // 检测是否为移动浏览器
        if (strpos($user_agent, 'Mobile') !== false || strpos($user_agent, 'Android') !== false) {
            // 移动浏览器变化较快，缓存时间稍短
            return 0.9;
        }

        // 未知或不常见浏览器，缓存时间较短
        return 0.6;
    }

    /**
     * 智能缓存清理
     */
    public function smart_cache_cleanup() {
        $cleanup_stats = array(
            'total_checked' => 0,
            'expired_removed' => 0,
            'low_confidence_removed' => 0,
            'start_time' => time()
        );

        // 获取所有WebP检测缓存键（这里简化处理）
        // 在实际实现中，可能需要维护一个缓存键列表

        // 清理低置信度的旧缓存
        $this->cleanup_low_confidence_cache();

        // 清理过期统计数据
        $this->cleanup_old_stats();

        $cleanup_stats['end_time'] = time();
        $cleanup_stats['duration'] = $cleanup_stats['end_time'] - $cleanup_stats['start_time'];

        // 记录清理统计
        wp_cache_set('webp_cleanup_stats_' . date('Y-m-d-H'), $cleanup_stats, 'webp_stats', 3600);

        return $cleanup_stats;
    }

    /**
     * 清理低置信度缓存
     */
    private function cleanup_low_confidence_cache() {
        // 这里可以实现更复杂的清理逻辑
        // 例如：清理置信度低于0.5且超过1小时的缓存

        // 由于WordPress对象缓存的限制，这里主要清理统计数据
        $cutoff_time = time() - 3600; // 1小时前

        for ($i = 1; $i <= 24; $i++) {
            $hour_key = 'webp_detection_stats_' . date('Y-m-d-H', $cutoff_time - ($i * 3600));
            wp_cache_delete($hour_key, 'webp_stats');
        }
    }

    /**
     * 记录缓存命中统计
     *
     * @param string $cache_type
     */
    private function record_cache_hit($cache_type) {
        // 简单的统计记录，可以用于性能分析
        $stats_key = 'webp_cache_stats_' . date('Y-m-d');
        $stats = wp_cache_get($stats_key, 'webp_stats') ?: array();
        $stats[$cache_type] = ($stats[$cache_type] ?? 0) + 1;
        wp_cache_set($stats_key, $stats, 'webp_stats', 24 * 3600);
    }

    /**
     * 获取最后一次检测信息
     *
     * @return array
     */
    private function get_last_detection_info() {
        // 这里应该存储最后一次检测的详细信息
        // 为了简化，返回默认值
        return array(
            'confidence' => 0.7,
            'method' => 'user_agent'
        );
    }

    /**
     * 记录检测统计信息
     *
     * @param array $detection_info
     */
    private function record_detection_stats($detection_info) {
        // 记录检测方法和置信度统计
        $stats_key = 'webp_detection_stats_' . date('Y-m-d');
        $stats = wp_cache_get($stats_key, 'webp_stats') ?: array();

        $method = $detection_info['method'] ?? 'unknown';
        $stats['methods'][$method] = ($stats['methods'][$method] ?? 0) + 1;
        $stats['total_detections'] = ($stats['total_detections'] ?? 0) + 1;

        wp_cache_set($stats_key, $stats, 'webp_stats', 24 * 3600);
    }
    
    /**
     * 通过多层检测机制检测WebP支持
     *
     * @return array 返回检测结果和置信度
     */
    private function detect_webp_by_user_agent() {
        $detection_result = array(
            'supported' => false,
            'confidence' => 0.0,
            'method' => 'unknown',
            'details' => array()
        );

        // 第一层：Accept头部检测（最准确，置信度95%）
        $accept_header = $_SERVER['HTTP_ACCEPT'] ?? '';
        if (strpos($accept_header, 'image/webp') !== false) {
            $detection_result['supported'] = true;
            $detection_result['confidence'] = 0.95;
            $detection_result['method'] = 'accept_header';
            $detection_result['details']['accept_header'] = $accept_header;
            return $detection_result['supported']; // 保持向后兼容
        }

        // 第二层：Sec-CH-UA头部检测（现代浏览器，置信度90%）
        $sec_ch_ua = $_SERVER['HTTP_SEC_CH_UA'] ?? '';
        if (!empty($sec_ch_ua)) {
            $webp_support = $this->detect_webp_by_sec_ch_ua($sec_ch_ua);
            if ($webp_support !== null) {
                $detection_result['supported'] = $webp_support;
                $detection_result['confidence'] = 0.90;
                $detection_result['method'] = 'sec_ch_ua';
                $detection_result['details']['sec_ch_ua'] = $sec_ch_ua;
                return $detection_result['supported'];
            }
        }

        // 第三层：User-Agent检测（传统方法，置信度70-85%）
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($user_agent)) {
            return false;
        }

        $ua_result = $this->detect_webp_by_enhanced_user_agent($user_agent);
        if ($ua_result['supported']) {
            $detection_result = array_merge($detection_result, $ua_result);
            return $detection_result['supported'];
        }

        // 第四层：特殊情况检测（移动浏览器、WebView等）
        $special_result = $this->detect_webp_by_special_cases($user_agent);
        if ($special_result['supported']) {
            $detection_result = array_merge($detection_result, $special_result);
            return $detection_result['supported'];
        }

        return false;
    }

    /**
     * 通过Sec-CH-UA头部检测WebP支持
     *
     * @param string $sec_ch_ua
     * @return bool|null
     */
    private function detect_webp_by_sec_ch_ua($sec_ch_ua) {
        // 解析Sec-CH-UA头部
        // 格式示例: "Chromium";v="92", "Google Chrome";v="92", ";Not A Brand";v="99"

        // 获取所有浏览器信息
        $browsers = $this->parse_sec_ch_ua($sec_ch_ua);

        if (empty($browsers)) {
            return null;
        }

        // 检查每个浏览器的WebP支持
        foreach ($browsers as $browser_info) {
            $browser_name = strtolower($browser_info['name']);
            $version = $browser_info['version'];

            // 现代浏览器WebP支持版本（更全面的列表）
            $modern_webp_support = array(
                'chromium' => 23,
                'google chrome' => 23,
                'chrome' => 23,
                'microsoft edge' => 18,
                'edge' => 18,
                'opera' => 12,
                'firefox' => 65,
                'safari' => 14,
                'brave' => 23,
                'vivaldi' => 23,
                'samsung internet' => 4,
                'yandex' => 14,
                'uc browser' => 12
            );

            foreach ($modern_webp_support as $browser => $min_version) {
                if (strpos($browser_name, $browser) !== false && $version >= $min_version) {
                    return true;
                }
            }
        }

        // 检查移动平台特殊情况
        return $this->check_mobile_webp_support_by_client_hints();
    }

    /**
     * 解析Sec-CH-UA头部
     *
     * @param string $sec_ch_ua
     * @return array
     */
    private function parse_sec_ch_ua($sec_ch_ua) {
        $browsers = array();

        // 匹配所有浏览器信息
        if (preg_match_all('/"([^"]+)";v="(\d+)"/', $sec_ch_ua, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $name = $match[1];
                $version = intval($match[2]);

                // 过滤掉无效的浏览器名称
                if (!empty($name) && $name !== 'Not A Brand' && !preg_match('/^[;\s]*$/', $name)) {
                    $browsers[] = array(
                        'name' => $name,
                        'version' => $version
                    );
                }
            }
        }

        return $browsers;
    }

    /**
     * 通过Client Hints检查移动平台WebP支持
     *
     * @return bool|null
     */
    private function check_mobile_webp_support_by_client_hints() {
        $sec_ch_ua_mobile = $_SERVER['HTTP_SEC_CH_UA_MOBILE'] ?? '';
        $sec_ch_ua_platform = $_SERVER['HTTP_SEC_CH_UA_PLATFORM'] ?? '';
        $sec_ch_ua_platform_version = $_SERVER['HTTP_SEC_CH_UA_PLATFORM_VERSION'] ?? '';

        // 如果是移动设备
        if ($sec_ch_ua_mobile === '?1') {
            // Android设备
            if (strpos(strtolower($sec_ch_ua_platform), 'android') !== false) {
                // Android 4.0+ 通常支持WebP
                if (!empty($sec_ch_ua_platform_version)) {
                    $android_version = floatval($sec_ch_ua_platform_version);
                    return $android_version >= 4.0;
                }
                return true; // 假设现代Android支持WebP
            }

            // iOS设备
            if (strpos(strtolower($sec_ch_ua_platform), 'ios') !== false) {
                // iOS 14+ 支持WebP
                if (!empty($sec_ch_ua_platform_version)) {
                    $ios_version = floatval($sec_ch_ua_platform_version);
                    return $ios_version >= 14.0;
                }
                return false; // 保守估计
            }
        }

        return null;
    }

    /**
     * 获取增强的浏览器信息（包含Client Hints）
     *
     * @return array
     */
    public function get_enhanced_browser_info() {
        $info = $this->get_browser_info();

        // 添加Client Hints信息
        $info['client_hints'] = array(
            'sec_ch_ua' => $_SERVER['HTTP_SEC_CH_UA'] ?? '',
            'sec_ch_ua_mobile' => $_SERVER['HTTP_SEC_CH_UA_MOBILE'] ?? '',
            'sec_ch_ua_platform' => $_SERVER['HTTP_SEC_CH_UA_PLATFORM'] ?? '',
            'sec_ch_ua_platform_version' => $_SERVER['HTTP_SEC_CH_UA_PLATFORM_VERSION'] ?? ''
        );

        // 解析Client Hints中的浏览器信息
        if (!empty($info['client_hints']['sec_ch_ua'])) {
            $info['parsed_browsers'] = $this->parse_sec_ch_ua($info['client_hints']['sec_ch_ua']);
        }

        return $info;
    }

    /**
     * 增强的User-Agent检测
     *
     * @param string $user_agent
     * @return array
     */
    private function detect_webp_by_enhanced_user_agent($user_agent) {
        $result = array(
            'supported' => false,
            'confidence' => 0.0,
            'method' => 'user_agent',
            'details' => array('user_agent' => $user_agent)
        );

        // 更精确的浏览器检测模式
        $enhanced_patterns = array(
            'chrome' => array(
                'pattern' => '/Chrome\/(\d+)/',
                'min_version' => 23,
                'confidence' => 0.85
            ),
            'firefox' => array(
                'pattern' => '/Firefox\/(\d+)/',
                'min_version' => 65,
                'confidence' => 0.80
            ),
            'safari' => array(
                'pattern' => '/Version\/(\d+).*Safari/',
                'min_version' => 14,
                'confidence' => 0.75
            ),
            'edge' => array(
                'pattern' => '/Edg\/(\d+)/',
                'min_version' => 18,
                'confidence' => 0.85
            ),
            'opera' => array(
                'pattern' => '/OPR\/(\d+)/',
                'min_version' => 12,
                'confidence' => 0.80
            )
        );

        foreach ($enhanced_patterns as $browser => $config) {
            if (preg_match($config['pattern'], $user_agent, $matches)) {
                $version = intval($matches[1]);

                if ($version >= $config['min_version']) {
                    $result['supported'] = true;
                    $result['confidence'] = $config['confidence'];
                    $result['details']['browser'] = $browser;
                    $result['details']['version'] = $version;
                    $result['details']['min_version'] = $config['min_version'];
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * 特殊情况检测（移动浏览器、WebView等）
     *
     * @param string $user_agent
     * @return array
     */
    private function detect_webp_by_special_cases($user_agent) {
        $result = array(
            'supported' => false,
            'confidence' => 0.0,
            'method' => 'special_cases',
            'details' => array()
        );

        // Android WebView
        if (preg_match('/Android.*Chrome\/(\d+)/', $user_agent, $matches)) {
            $chrome_version = intval($matches[1]);
            if ($chrome_version >= 23) {
                $result['supported'] = true;
                $result['confidence'] = 0.75;
                $result['details']['type'] = 'android_webview';
                $result['details']['chrome_version'] = $chrome_version;
            }
        }

        // iOS Safari (iOS 14+)
        elseif (preg_match('/iPhone.*OS (\d+)_/', $user_agent, $matches)) {
            $ios_version = intval($matches[1]);
            if ($ios_version >= 14) {
                $result['supported'] = true;
                $result['confidence'] = 0.70;
                $result['details']['type'] = 'ios_safari';
                $result['details']['ios_version'] = $ios_version;
            }
        }

        // Samsung Internet
        elseif (preg_match('/SamsungBrowser\/(\d+)/', $user_agent, $matches)) {
            $version = intval($matches[1]);
            if ($version >= 4) {
                $result['supported'] = true;
                $result['confidence'] = 0.75;
                $result['details']['type'] = 'samsung_internet';
                $result['details']['version'] = $version;
            }
        }

        return $result;
    }
    
    /**
     * 处理AJAX WebP检测请求
     */
    public function handle_webp_detection() {
        // 验证nonce
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'webp_detection_nonce')) {
            wp_die('安全验证失败');
        }
        
        $webp_supported = $_POST['webp_supported'] ?? 'false';
        $webp_supported = ($webp_supported === 'true') ? '1' : '0';
        
        // 设置Cookie
        $expire_time = time() + (self::COOKIE_EXPIRE_DAYS * 24 * 60 * 60);
        setcookie(self::WEBP_SUPPORT_COOKIE, $webp_supported, $expire_time, '/');
        
        // 记录检测结果
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
        
        light_fixture_log_webp_message(
            "WebP检测结果: " . ($webp_supported ? '支持' : '不支持'),
            'info',
            array(
                'user_agent' => $user_agent,
                'ip_address' => $ip_address,
                'detection_method' => 'javascript'
            )
        );
        
        wp_send_json_success(array(
            'webp_supported' => $webp_supported,
            'message' => 'WebP检测完成'
        ));
    }
    
    /**
     * 获取WebP检测统计信息
     * 
     * @return array
     */
    public function get_detection_statistics() {
        // 这里可以实现更复杂的统计逻辑
        // 比如从数据库中读取检测历史记录
        
        return array(
            'total_detections' => 0,
            'webp_supported_count' => 0,
            'webp_not_supported_count' => 0,
            'support_rate' => 0,
            'last_detection' => null
        );
    }
    
    /**
     * 清理过期的检测数据
     */
    public function cleanup_expired_data() {
        // 这里可以实现清理过期Cookie或数据库记录的逻辑
        // 目前Cookie会自动过期，所以暂时不需要额外处理
        
        return true;
    }
    
    /**
     * 获取浏览器信息
     * 
     * @return array
     */
    public function get_browser_info() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        $browser_info = array(
            'user_agent' => $user_agent,
            'browser' => 'Unknown',
            'version' => 'Unknown',
            'webp_supported' => false,
            'detection_method' => 'user_agent'
        );
        
        foreach ($this->webp_user_agents as $browser => $pattern) {
            if (preg_match($pattern, $user_agent, $matches)) {
                $version = intval($matches[1]);
                $min_version = $this->min_versions[$browser] ?? 999;
                
                $browser_info['browser'] = $browser;
                $browser_info['version'] = $version;
                $browser_info['webp_supported'] = $version >= $min_version;
                break;
            }
        }
        
        // 检查Cookie检测结果
        if (isset($_COOKIE[self::WEBP_SUPPORT_COOKIE])) {
            $browser_info['webp_supported'] = $_COOKIE[self::WEBP_SUPPORT_COOKIE] === '1';
            $browser_info['detection_method'] = 'cookie';
        }

        return $browser_info;
    }

    /**
     * 清理WebP检测缓存
     */
    public function clear_detection_cache() {
        // 清理对象缓存中的所有WebP检测结果
        wp_cache_flush_group('webp_detection');

        // 清理Cookie
        if (!headers_sent()) {
            setcookie(self::WEBP_SUPPORT_COOKIE, '', time() - 3600, '/');
        }
    }

    /**
     * 获取缓存统计信息
     */
    public function get_cache_stats() {
        $stats = array(
            'cache_group' => 'webp_detection',
            'cookie_name' => self::WEBP_SUPPORT_COOKIE,
            'cookie_set' => isset($_COOKIE[self::WEBP_SUPPORT_COOKIE]),
            'cookie_value' => $_COOKIE[self::WEBP_SUPPORT_COOKIE] ?? null,
        );

        // 添加今日缓存统计
        $today_stats_key = 'webp_cache_stats_' . date('Y-m-d');
        $today_stats = wp_cache_get($today_stats_key, 'webp_stats') ?: array();
        $stats['today_cache_hits'] = $today_stats;

        // 添加检测方法统计
        $detection_stats_key = 'webp_detection_stats_' . date('Y-m-d');
        $detection_stats = wp_cache_get($detection_stats_key, 'webp_stats') ?: array();
        $stats['today_detection_methods'] = $detection_stats;

        return $stats;
    }

    /**
     * 预热常见浏览器的WebP支持缓存
     */
    public function warm_up_cache() {
        // 检查是否已经预热过（避免重复预热）
        $warmup_key = 'webp_cache_warmed_' . date('Y-m-d');
        if (wp_cache_get($warmup_key, 'webp_detection')) {
            return;
        }

        $common_browsers = $this->get_common_browser_patterns();

        $warmed_count = 0;
        foreach ($common_browsers as $browser) {
            $cache_key = $this->generate_cache_key_for_browser($browser);

            $cache_duration = $this->calculate_cache_duration($browser['confidence']);
            $success = wp_cache_set(
                $cache_key,
                $browser['webp_supported'] ? '1' : '0',
                'webp_detection',
                $cache_duration
            );

            if ($success) {
                $warmed_count++;
            }
        }

        // 标记今日已预热
        wp_cache_set($warmup_key, $warmed_count, 'webp_detection', 24 * 3600);

        // 记录预热统计
        $this->record_warmup_stats($warmed_count, count($common_browsers));
    }

    /**
     * 获取常见浏览器模式
     */
    private function get_common_browser_patterns() {
        return array(
            // Chrome浏览器（多个版本）
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.95,
                'browser' => 'chrome',
                'version' => 91
            ),
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.95,
                'browser' => 'chrome',
                'version' => 96
            ),
            // Firefox浏览器
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.90,
                'browser' => 'firefox',
                'version' => 89
            ),
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:95.0) Gecko/20100101 Firefox/95.0',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.90,
                'browser' => 'firefox',
                'version' => 95
            ),
            // Safari浏览器（支持WebP）
            array(
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.85,
                'browser' => 'safari',
                'version' => 14
            ),
            array(
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.1 Safari/605.1.15',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.90,
                'browser' => 'safari',
                'version' => 15
            ),
            // Edge浏览器
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.95,
                'browser' => 'edge',
                'version' => 91
            ),
            // 移动浏览器
            array(
                'user_agent' => 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.80,
                'browser' => 'mobile_safari',
                'version' => 14
            ),
            array(
                'user_agent' => 'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'webp_supported' => true,
                'confidence' => 0.90,
                'browser' => 'android_chrome',
                'version' => 91
            ),
            // 旧版浏览器（不支持WebP）
            array(
                'user_agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'webp_supported' => false,
                'confidence' => 0.90,
                'browser' => 'safari_old',
                'version' => 13
            ),
            array(
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:60.0) Gecko/20100101 Firefox/60.0',
                'accept' => 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'webp_supported' => false,
                'confidence' => 0.85,
                'browser' => 'firefox_old',
                'version' => 60
            )
        );
    }

    /**
     * 为浏览器生成缓存键
     */
    private function generate_cache_key_for_browser($browser) {
        $browser_fingerprint = $this->generate_browser_fingerprint(
            $browser['user_agent'],
            $browser['accept'],
            ''
        );

        return 'webp_support_v2_' . md5($browser_fingerprint);
    }

    /**
     * 记录预热统计信息
     */
    private function record_warmup_stats($warmed_count, $total_count) {
        $stats_key = 'webp_warmup_stats_' . date('Y-m-d');
        $stats = array(
            'warmed_count' => $warmed_count,
            'total_count' => $total_count,
            'success_rate' => $total_count > 0 ? round(($warmed_count / $total_count) * 100, 2) : 0,
            'timestamp' => time()
        );

        wp_cache_set($stats_key, $stats, 'webp_stats', 24 * 3600);
    }

    /**
     * 获取WebP检测统计报告
     */
    public function get_detection_report() {
        $report = array(
            'cache_performance' => array(),
            'detection_methods' => array(),
            'browser_distribution' => array(),
            'webp_support_rate' => 0
        );

        // 获取最近7天的统计数据
        for ($i = 0; $i < 7; $i++) {
            $date = date('Y-m-d', strtotime("-$i days"));

            $cache_stats_key = 'webp_cache_stats_' . $date;
            $cache_stats = wp_cache_get($cache_stats_key, 'webp_stats') ?: array();
            $report['cache_performance'][$date] = $cache_stats;

            $detection_stats_key = 'webp_detection_stats_' . $date;
            $detection_stats = wp_cache_get($detection_stats_key, 'webp_stats') ?: array();
            $report['detection_methods'][$date] = $detection_stats;
        }

        return $report;
    }

    /**
     * 清理过期的统计数据
     */
    public function cleanup_old_stats() {
        // 清理7天前的统计数据
        for ($i = 7; $i < 30; $i++) {
            $date = date('Y-m-d', strtotime("-$i days"));

            wp_cache_delete('webp_cache_stats_' . $date, 'webp_stats');
            wp_cache_delete('webp_detection_stats_' . $date, 'webp_stats');
        }
    }
}

/**
 * 全局函数：检查WebP支持
 * 
 * @return bool
 */
function light_fixture_is_webp_browser_supported() {
    static $detector = null;
    
    if ($detector === null) {
        $detector = new Light_Fixture_WebP_Browser_Detection();
    }
    
    return $detector->is_webp_supported();
}

/**
 * 全局函数：获取浏览器信息
 * 
 * @return array
 */
function light_fixture_get_browser_webp_info() {
    static $detector = null;
    
    if ($detector === null) {
        $detector = new Light_Fixture_WebP_Browser_Detection();
    }
    
    return $detector->get_browser_info();
}

// 注意：light_fixture_get_optimized_image_url() 函数已在 webp-utils.php 中定义

/**
 * 全局函数：生成响应式图片的srcset属性
 * 
 * @param int $attachment_id 附件ID
 * @param string $size 图片尺寸
 * @return string
 */
function light_fixture_get_webp_srcset($attachment_id, $size = 'full') {
    $srcset = wp_get_attachment_image_srcset($attachment_id, $size);
    
    if (!$srcset || !light_fixture_is_webp_browser_supported()) {
        return $srcset;
    }
    
    // 将srcset中的图片URL替换为WebP版本
    $srcset = preg_replace_callback(
        '/(\S+\.(jpe?g|png|gif))(\s+\d+w)/i',
        function($matches) {
            $original_url = $matches[1];
            $webp_url = preg_replace('/\.(jpe?g|png|gif)$/i', '.webp', $original_url);
            
            // 检查WebP文件是否存在
            $upload_dir = wp_upload_dir();
            $webp_path = str_replace($upload_dir['baseurl'], $upload_dir['basedir'], $webp_url);
            
            if (file_exists($webp_path)) {
                return $webp_url . $matches[3];
            }
            
            return $matches[0];
        },
        $srcset
    );
    
    return $srcset;
}

// 初始化浏览器检测系统
function light_fixture_init_webp_browser_detection() {
    global $light_fixture_webp_browser_detection;
    $light_fixture_webp_browser_detection = new Light_Fixture_WebP_Browser_Detection();
}

// 在WordPress初始化时启动浏览器检测
add_action('init', 'light_fixture_init_webp_browser_detection');
