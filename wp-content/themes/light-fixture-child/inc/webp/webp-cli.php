<?php
/**
 * WebP WP-CLI Commands
 * WebP WP-CLI命令行工具
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 检查WP-CLI是否可用
if (!defined('WP_CLI') || !WP_CLI) {
    return;
}

/**
 * WebP WP-CLI命令类
 */
class Light_Fixture_WebP_CLI_Commands extends WP_CLI_Command {
    
    /**
     * 转换历史图片为WebP格式
     * 
     * ## OPTIONS
     * 
     * [--batch-size=<number>]
     * : 每批处理的图片数量
     * ---
     * default: 50
     * ---
     * 
     * [--quality=<number>]
     * : WebP质量设置 (1-100)
     * ---
     * default: 85
     * ---
     * 
     * [--force]
     * : 强制重新转换已存在的WebP文件
     * 
     * [--dry-run]
     * : 仅显示将要处理的文件，不执行实际转换
     * 
     * [--include-sizes=<sizes>]
     * : 包含的图片尺寸，用逗号分隔 (如: thumbnail,medium,large)
     * 
     * [--exclude-sizes=<sizes>]
     * : 排除的图片尺寸，用逗号分隔
     * 
     * [--mime-types=<types>]
     * : 处理的MIME类型，用逗号分隔
     * ---
     * default: image/jpeg,image/png,image/gif
     * ---
     * 
     * ## EXAMPLES
     * 
     *     # 转换所有历史图片
     *     wp webp convert
     * 
     *     # 批量转换，每批10张图片
     *     wp webp convert --batch-size=10
     * 
     *     # 强制重新转换所有图片
     *     wp webp convert --force
     * 
     *     # 仅预览将要转换的文件
     *     wp webp convert --dry-run
     * 
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function convert($args, $assoc_args) {
        $batch_size = intval($assoc_args['batch-size'] ?? 50);
        $quality = intval($assoc_args['quality'] ?? 85);
        $force = isset($assoc_args['force']);
        $dry_run = isset($assoc_args['dry-run']);
        
        $include_sizes = isset($assoc_args['include-sizes']) ? 
            explode(',', $assoc_args['include-sizes']) : null;
        $exclude_sizes = isset($assoc_args['exclude-sizes']) ? 
            explode(',', $assoc_args['exclude-sizes']) : array();
        
        $mime_types = explode(',', $assoc_args['mime-types'] ?? 'image/jpeg,image/png,image/gif');
        
        // 验证参数
        if ($batch_size < 1 || $batch_size > 1000) {
            WP_CLI::error('批次大小必须在1-1000之间');
        }
        
        if ($quality < 1 || $quality > 100) {
            WP_CLI::error('质量设置必须在1-100之间');
        }
        
        // 检查WebP支持
        $converter = light_fixture_get_webp_converter();
        if (!$converter->is_webp_supported()) {
            WP_CLI::error('服务器不支持WebP转换。推荐引擎: ' . $converter->get_preferred_editor());
        }
        
        WP_CLI::log('开始批量转换历史图片为WebP格式...');
        WP_CLI::log('配置: 批次大小=' . $batch_size . ', 质量=' . $quality . ', 强制=' . ($force ? '是' : '否'));
        
        if ($dry_run) {
            WP_CLI::log('*** 预览模式 - 不会执行实际转换 ***');
        }
        
        $this->process_attachments($batch_size, $quality, $force, $dry_run, $include_sizes, $exclude_sizes, $mime_types);
    }
    
    /**
     * 显示WebP转换状态统计
     * 
     * ## OPTIONS
     * 
     * [--format=<format>]
     * : 输出格式
     * ---
     * default: table
     * options:
     *   - table
     *   - json
     *   - csv
     * ---
     * 
     * ## EXAMPLES
     * 
     *     # 显示转换统计
     *     wp webp status
     * 
     *     # 以JSON格式输出
     *     wp webp status --format=json
     * 
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function status($args, $assoc_args) {
        $format = $assoc_args['format'] ?? 'table';
        
        WP_CLI::log('正在收集WebP转换统计信息...');
        
        $stats = light_fixture_get_webp_statistics();
        
        if ($format === 'json') {
            WP_CLI::print_value($stats, $assoc_args);
            return;
        }
        
        $data = array(
            array('指标', '数值'),
            array('总图片数', $stats['total_images']),
            array('已转换数', $stats['webp_converted']),
            array('转换率', $stats['conversion_rate'] . '%'),
            array('原始总大小', $stats['total_original_size_formatted']),
            array('WebP总大小', $stats['total_webp_size_formatted']),
            array('节省空间', $stats['total_savings_formatted']),
        );
        
        WP_CLI\Utils\format_items($format, $data, array('指标', '数值'));
    }
    
    /**
     * 清理孤立的WebP文件
     * 
     * ## OPTIONS
     * 
     * [--dry-run]
     * : 仅显示将要删除的文件，不执行实际删除
     * 
     * ## EXAMPLES
     * 
     *     # 清理孤立的WebP文件
     *     wp webp cleanup
     * 
     *     # 预览将要清理的文件
     *     wp webp cleanup --dry-run
     * 
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function cleanup($args, $assoc_args) {
        $dry_run = isset($assoc_args['dry-run']);
        
        WP_CLI::log('开始清理孤立的WebP文件...');
        
        if ($dry_run) {
            WP_CLI::log('*** 预览模式 - 不会执行实际删除 ***');
        }
        
        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'];
        
        $webp_files = light_fixture_find_webp_files($base_dir);
        $orphaned_files = array();
        
        $progress = WP_CLI\Utils\make_progress_bar('检查WebP文件', count($webp_files));
        
        foreach ($webp_files as $webp_file) {
            $original_file = light_fixture_get_original_from_webp($webp_file);
            
            if (!file_exists($original_file)) {
                $orphaned_files[] = $webp_file;
            }
            
            $progress->tick();
        }
        
        $progress->finish();
        
        if (empty($orphaned_files)) {
            WP_CLI::success('没有发现孤立的WebP文件');
            return;
        }
        
        WP_CLI::log('发现 ' . count($orphaned_files) . ' 个孤立的WebP文件');
        
        if ($dry_run) {
            foreach ($orphaned_files as $file) {
                WP_CLI::log('将删除: ' . str_replace($base_dir, '', $file));
            }
            return;
        }
        
        $cleaned_count = 0;
        $progress = WP_CLI\Utils\make_progress_bar('清理孤立文件', count($orphaned_files));
        
        foreach ($orphaned_files as $file) {
            if (wp_delete_file($file)) {
                $cleaned_count++;
            }
            $progress->tick();
        }
        
        $progress->finish();
        
        WP_CLI::success("成功清理了 $cleaned_count 个孤立的WebP文件");
    }
    
    /**
     * 验证WebP文件完整性
     * 
     * ## OPTIONS
     * 
     * [--fix]
     * : 自动修复损坏的WebP文件
     * 
     * ## EXAMPLES
     * 
     *     # 验证WebP文件完整性
     *     wp webp verify
     * 
     *     # 验证并修复损坏的文件
     *     wp webp verify --fix
     * 
     * @param array $args 位置参数
     * @param array $assoc_args 关联参数
     */
    public function verify($args, $assoc_args) {
        $fix = isset($assoc_args['fix']);
        
        WP_CLI::log('开始验证WebP文件完整性...');
        
        $upload_dir = wp_upload_dir();
        $base_dir = $upload_dir['basedir'];
        
        $webp_files = light_fixture_find_webp_files($base_dir);
        $corrupted_files = array();
        
        $progress = WP_CLI\Utils\make_progress_bar('验证WebP文件', count($webp_files));
        
        foreach ($webp_files as $webp_file) {
            if (!light_fixture_validate_webp_file($webp_file)) {
                $corrupted_files[] = $webp_file;
            }
            $progress->tick();
        }
        
        $progress->finish();
        
        if (empty($corrupted_files)) {
            WP_CLI::success('所有WebP文件都完整有效');
            return;
        }
        
        WP_CLI::warning('发现 ' . count($corrupted_files) . ' 个损坏的WebP文件');
        
        if (!$fix) {
            foreach ($corrupted_files as $file) {
                WP_CLI::log('损坏文件: ' . str_replace($base_dir, '', $file));
            }
            WP_CLI::log('使用 --fix 参数自动修复这些文件');
            return;
        }
        
        $fixed_count = 0;
        $converter = light_fixture_get_webp_converter();
        
        $progress = WP_CLI\Utils\make_progress_bar('修复损坏文件', count($corrupted_files));
        
        foreach ($corrupted_files as $webp_file) {
            $original_file = light_fixture_get_original_from_webp($webp_file);
            
            if (file_exists($original_file)) {
                // 删除损坏的WebP文件
                wp_delete_file($webp_file);
                
                // 重新生成WebP文件
                $result = $converter->convert_to_webp($original_file);
                
                if (!is_wp_error($result)) {
                    $fixed_count++;
                }
            }
            
            $progress->tick();
        }
        
        $progress->finish();
        
        WP_CLI::success("成功修复了 $fixed_count 个损坏的WebP文件");
    }
    
    /**
     * 处理附件转换
     */
    private function process_attachments($batch_size, $quality, $force, $dry_run, $include_sizes, $exclude_sizes, $mime_types) {
        global $wpdb;
        
        // 获取所有图片附件
        $mime_type_placeholders = implode(',', array_fill(0, count($mime_types), '%s'));
        
        $query = $wpdb->prepare(
            "SELECT ID FROM {$wpdb->posts} 
             WHERE post_type = 'attachment' 
             AND post_mime_type IN ($mime_type_placeholders)
             ORDER BY ID ASC",
            $mime_types
        );
        
        $attachment_ids = $wpdb->get_col($query);
        $total_attachments = count($attachment_ids);
        
        if ($total_attachments === 0) {
            WP_CLI::warning('没有找到符合条件的图片附件');
            return;
        }
        
        WP_CLI::log("找到 $total_attachments 个图片附件");
        
        $processed = 0;
        $converted = 0;
        $skipped = 0;
        $errors = 0;
        
        $converter = light_fixture_get_webp_converter();
        
        // 分批处理
        $batches = array_chunk($attachment_ids, $batch_size);
        $total_batches = count($batches);
        
        foreach ($batches as $batch_index => $batch_ids) {
            WP_CLI::log("处理批次 " . ($batch_index + 1) . "/$total_batches");
            
            $progress = WP_CLI\Utils\make_progress_bar('转换图片', count($batch_ids));
            
            foreach ($batch_ids as $attachment_id) {
                $file_path = get_attached_file($attachment_id);
                
                if (!$file_path || !file_exists($file_path)) {
                    $progress->tick();
                    continue;
                }
                
                $processed++;
                
                // 检查是否需要转换
                if (!$force && light_fixture_webp_exists($file_path)) {
                    $skipped++;
                    $progress->tick();
                    continue;
                }
                
                if ($dry_run) {
                    WP_CLI::log('将转换: ' . basename($file_path));
                    $progress->tick();
                    continue;
                }
                
                // 执行转换
                $result = $converter->convert_to_webp($file_path, null, $quality);
                
                if (is_wp_error($result)) {
                    $errors++;
                    WP_CLI::debug('转换失败: ' . $result->get_error_message());
                } else {
                    $converted++;
                    
                    // 转换所有尺寸
                    $this->convert_attachment_sizes($attachment_id, $converter, $quality, $include_sizes, $exclude_sizes);
                }
                
                $progress->tick();
                
                // 避免内存泄漏
                if ($processed % 100 === 0) {
                    WP_CLI::debug('已处理 ' . $processed . ' 个附件，清理内存...');
                    wp_cache_flush();
                }
            }
            
            $progress->finish();
            
            // 批次间暂停，避免服务器过载
            if ($batch_index < $total_batches - 1) {
                WP_CLI::debug('批次完成，暂停1秒...');
                sleep(1);
            }
        }
        
        // 显示最终统计
        WP_CLI::log('');
        WP_CLI::log('转换完成统计:');
        WP_CLI::log("总处理数: $processed");
        WP_CLI::log("成功转换: $converted");
        WP_CLI::log("跳过数量: $skipped");
        WP_CLI::log("错误数量: $errors");
        
        if ($dry_run) {
            WP_CLI::log('*** 这是预览模式，没有执行实际转换 ***');
        } else {
            WP_CLI::success('批量转换完成！');
        }
    }
    
    /**
     * 转换附件的所有尺寸
     */
    private function convert_attachment_sizes($attachment_id, $converter, $quality, $include_sizes, $exclude_sizes) {
        $metadata = wp_get_attachment_metadata($attachment_id);
        
        if (!isset($metadata['sizes']) || !is_array($metadata['sizes'])) {
            return;
        }
        
        $file_path = get_attached_file($attachment_id);
        $base_dir = dirname($file_path);
        
        foreach ($metadata['sizes'] as $size_name => $size_data) {
            // 检查尺寸过滤
            if ($include_sizes && !in_array($size_name, $include_sizes)) {
                continue;
            }
            
            if (in_array($size_name, $exclude_sizes)) {
                continue;
            }
            
            $size_file_path = $base_dir . '/' . $size_data['file'];
            
            if (file_exists($size_file_path)) {
                $converter->convert_to_webp($size_file_path, null, $quality);
            }
        }
    }
}

// 注册WP-CLI命令
if (class_exists('WP_CLI')) {
    WP_CLI::add_command('webp', 'Light_Fixture_WebP_CLI_Commands');
}
