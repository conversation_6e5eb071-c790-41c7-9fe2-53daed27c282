<?php
/**
 * 社交媒体链接管理系统
 *
 * @package Light_Fixture_Child
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 社交媒体设置类
 */
class Light_Fixture_Social_Media_Settings {
    
    /**
     * 支持的社交媒体平台
     */
    private $social_platforms = array(
        'facebook' => array(
            'name' => 'Facebook',
            'icon' => '<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>',
            'placeholder' => 'https://www.facebook.com/yourpage'
        ),
        'twitter' => array(
            'name' => 'Twitter (X)',
            'icon' => '<path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>',
            'placeholder' => 'https://twitter.com/yourusername'
        ),
        'instagram' => array(
            'name' => 'Instagram',
            'icon' => '<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>',
            'placeholder' => 'https://www.instagram.com/yourusername'
        ),
        'linkedin' => array(
            'name' => 'LinkedIn',
            'icon' => '<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle>',
            'placeholder' => 'https://www.linkedin.com/company/yourcompany'
        ),
        'youtube' => array(
            'name' => 'YouTube',
            'icon' => '<path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>',
            'placeholder' => 'https://www.youtube.com/channel/yourchannel'
        ),
        'pinterest' => array(
            'name' => 'Pinterest',
            'icon' => '<circle cx="12" cy="12" r="3"></circle><path d="M12 1a11 11 0 0 0-3.5 21.5c-.1-.9-.2-2.3 0-3.3l1.3-5.4s-.3-.6-.3-1.5c0-1.4.8-2.4 1.8-2.4.8 0 1.2.6 1.2 1.3 0 .8-.5 2-.8 3.1-.2 1 .5 1.8 1.5 1.8 1.8 0 3.2-1.9 3.2-4.6 0-2.4-1.7-4.1-4.2-4.1-2.9 0-4.6 2.2-4.6 4.4 0 .9.3 1.8.7 2.3a.3.3 0 0 1 .1.3c-.1.3-.2 1-.2 1.1a.2.2 0 0 1-.3.2c-1-.5-1.6-1.9-1.6-3.1 0-3.2 2.3-6.1 6.6-6.1 3.5 0 6.2 2.5 6.2 5.8 0 3.5-2.2 6.2-5.2 6.2-1 0-2-.5-2.3-1.2l-.6 2.4c-.2.8-.8 1.8-1.2 2.4A11 11 0 1 0 12 1z"></path>',
            'placeholder' => 'https://www.pinterest.com/yourusername'
        ),
        'tiktok' => array(
            'name' => 'TikTok',
            'icon' => '<path d="M9 12a4 4 0 1 0 4 4V8.5a7.5 7.5 0 0 0 7.5-7.5h-3a4.5 4.5 0 0 1-4.5 4.5V12z"></path>',
            'placeholder' => 'https://www.tiktok.com/@yourusername'
        ),
        'whatsapp' => array(
            'name' => 'WhatsApp',
            'icon' => '<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>',
            'placeholder' => 'https://wa.me/1234567890'
        )
    );

    /**
     * 构造函数
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'settings_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * 添加管理菜单
     */
    public function add_admin_menu() {
        add_options_page(
            '社交媒体设置',
            '社交媒体',
            'manage_options',
            'social-media-settings',
            array($this, 'settings_page')
        );
    }

    /**
     * 初始化设置
     */
    public function settings_init() {
        // 注册设置
        register_setting('social_media_settings', 'social_media_links');
        register_setting('social_media_settings', 'social_media_enabled');

        // 添加设置区域
        add_settings_section(
            'social_media_section',
            '社交媒体链接配置',
            array($this, 'settings_section_callback'),
            'social_media_settings'
        );

        // 为每个平台添加设置字段
        foreach ($this->social_platforms as $platform => $data) {
            add_settings_field(
                'social_media_' . $platform,
                $data['name'],
                array($this, 'settings_field_callback'),
                'social_media_settings',
                'social_media_section',
                array(
                    'platform' => $platform,
                    'data' => $data
                )
            );
        }
    }

    /**
     * 设置区域回调
     */
    public function settings_section_callback() {
        echo '<p>在这里配置您的社交媒体链接。启用的链接将在网站前端显示。</p>';
    }

    /**
     * 设置字段回调
     */
    public function settings_field_callback($args) {
        $platform = $args['platform'];
        $data = $args['data'];
        
        $links = get_option('social_media_links', array());
        $enabled = get_option('social_media_enabled', array());
        
        $url = isset($links[$platform]) ? $links[$platform] : '';
        $is_enabled = isset($enabled[$platform]) ? $enabled[$platform] : false;
        
        echo '<div class="social-media-field">';
        echo '<div class="social-media-icon">';
        echo '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">';
        echo $data['icon'];
        echo '</svg>';
        echo '</div>';
        echo '<div class="social-media-inputs">';
        echo '<input type="url" name="social_media_links[' . $platform . ']" value="' . esc_attr($url) . '" placeholder="' . esc_attr($data['placeholder']) . '" class="regular-text" />';
        echo '<label><input type="checkbox" name="social_media_enabled[' . $platform . ']" value="1" ' . checked($is_enabled, true, false) . ' /> 启用</label>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * 加载管理脚本和样式
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook !== 'settings_page_social-media-settings') {
            return;
        }
        
        wp_add_inline_style('wp-admin', $this->get_admin_styles());
        wp_add_inline_script('wp-common', $this->get_admin_scripts());
    }

    /**
     * 获取管理样式
     */
    private function get_admin_styles() {
        return '
        .social-media-field {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .social-media-icon {
            margin-right: 15px;
            color: #666;
        }
        .social-media-inputs {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .social-media-inputs input[type="url"] {
            flex: 1;
        }
        .social-media-inputs label {
            white-space: nowrap;
            font-weight: 500;
        }
        .social-media-preview {
            margin-top: 20px;
            padding: 20px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .social-media-preview h3 {
            margin-top: 0;
        }
        .social-media-preview-icons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .social-media-preview-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            background: #f0f0f0;
            border-radius: 4px;
            color: #666;
            text-decoration: none;
        }
        .social-media-preview-icon:hover {
            background: #d6ad60;
            color: #000;
        }
        ';
    }

    /**
     * 获取管理脚本
     */
    private function get_admin_scripts() {
        return '
        document.addEventListener("DOMContentLoaded", function() {
            // 实时预览功能
            const updatePreview = function() {
                const previewContainer = document.querySelector(".social-media-preview-icons");
                if (!previewContainer) return;
                
                previewContainer.innerHTML = "";
                
                const platforms = ' . json_encode($this->social_platforms) . ';
                
                Object.keys(platforms).forEach(function(platform) {
                    const urlInput = document.querySelector("input[name=\"social_media_links[" + platform + "]\"]");
                    const enabledInput = document.querySelector("input[name=\"social_media_enabled[" + platform + "]\"]");
                    
                    if (urlInput && enabledInput && urlInput.value && enabledInput.checked) {
                        const icon = document.createElement("a");
                        icon.href = urlInput.value;
                        icon.target = "_blank";
                        icon.className = "social-media-preview-icon";
                        icon.title = platforms[platform].name;
                        icon.innerHTML = "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"16\" height=\"16\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">" + platforms[platform].icon + "</svg>";
                        previewContainer.appendChild(icon);
                    }
                });
            };
            
            // 监听输入变化
            document.querySelectorAll("input[name^=\"social_media_\"]").forEach(function(input) {
                input.addEventListener("input", updatePreview);
                input.addEventListener("change", updatePreview);
            });
            
            // 初始预览
            updatePreview();
        });
        ';
    }

    /**
     * 设置页面
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1>社交媒体设置</h1>
            <form action="options.php" method="post">
                <?php
                settings_fields('social_media_settings');
                do_settings_sections('social_media_settings');
                ?>
                
                <div class="social-media-preview">
                    <h3>预览效果</h3>
                    <p>以下是启用的社交媒体图标预览：</p>
                    <div class="social-media-preview-icons"></div>
                </div>
                
                <?php submit_button('保存设置'); ?>
            </form>
        </div>
        <?php
    }
}

// 初始化社交媒体设置
new Light_Fixture_Social_Media_Settings();

/**
 * 获取社交媒体链接的全局函数
 *
 * @param string $platform 平台名称，如果为空则返回所有启用的平台
 * @return array|string|false
 */
function get_social_media_links($platform = '') {
    $links = get_option('social_media_links', array());
    $enabled = get_option('social_media_enabled', array());
    
    if (!empty($platform)) {
        // 返回特定平台的链接
        if (isset($enabled[$platform]) && $enabled[$platform] && isset($links[$platform]) && !empty($links[$platform])) {
            return $links[$platform];
        }
        return false;
    }
    
    // 返回所有启用的平台
    $result = array();
    foreach ($enabled as $platform => $is_enabled) {
        if ($is_enabled && isset($links[$platform]) && !empty($links[$platform])) {
            $result[$platform] = $links[$platform];
        }
    }
    
    return $result;
}

/**
 * 检查社交媒体平台是否启用
 *
 * @param string $platform 平台名称
 * @return bool
 */
function is_social_media_enabled($platform) {
    $enabled = get_option('social_media_enabled', array());
    return isset($enabled[$platform]) && $enabled[$platform];
}

/**
 * 获取社交媒体图标SVG
 *
 * @param string $platform 平台名称
 * @return string|false
 */
function get_social_media_icon($platform) {
    $icons = array(
        'facebook' => '<path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>',
        'twitter' => '<path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>',
        'instagram' => '<rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>',
        'linkedin' => '<path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path><rect x="2" y="9" width="4" height="12"></rect><circle cx="4" cy="4" r="2"></circle>',
        'youtube' => '<path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon>',
        'pinterest' => '<circle cx="12" cy="12" r="3"></circle><path d="M12 1a11 11 0 0 0-3.5 21.5c-.1-.9-.2-2.3 0-3.3l1.3-5.4s-.3-.6-.3-1.5c0-1.4.8-2.4 1.8-2.4.8 0 1.2.6 1.2 1.3 0 .8-.5 2-.8 3.1-.2 1 .5 1.8 1.5 1.8 1.8 0 3.2-1.9 3.2-4.6 0-2.4-1.7-4.1-4.2-4.1-2.9 0-4.6 2.2-4.6 4.4 0 .9.3 1.8.7 2.3a.3.3 0 0 1 .1.3c-.1.3-.2 1-.2 1.1a.2.2 0 0 1-.3.2c-1-.5-1.6-1.9-1.6-3.1 0-3.2 2.3-6.1 6.6-6.1 3.5 0 6.2 2.5 6.2 5.8 0 3.5-2.2 6.2-5.2 6.2-1 0-2-.5-2.3-1.2l-.6 2.4c-.2.8-.8 1.8-1.2 2.4A11 11 0 1 0 12 1z"></path>',
        'tiktok' => '<path d="M9 12a4 4 0 1 0 4 4V8.5a7.5 7.5 0 0 0 7.5-7.5h-3a4.5 4.5 0 0 1-4.5 4.5V12z"></path>',
        'whatsapp' => '<path d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z"></path>'
    );
    
    return isset($icons[$platform]) ? $icons[$platform] : false;
}
