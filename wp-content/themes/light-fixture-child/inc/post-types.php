<?php
/**
 * 产品自定义文章类型和分类法
 *
 * @package Light_Fixture_Child
 */

/**
 * 注册产品自定义文章类型
 */
function light_fixture_register_product_cpt() {
    $labels = array(
        'name'                  => _x('产品', 'Post type general name', 'light-fixture-child'),
        'singular_name'         => _x('产品', 'Post type singular name', 'light-fixture-child'),
        'menu_name'            => _x('产品', 'Admin Menu text', 'light-fixture-child'),
        'name_admin_bar'       => _x('产品', 'Add New on Toolbar', 'light-fixture-child'),
        'add_new'              => __('添加新产品', 'light-fixture-child'),
        'add_new_item'         => __('添加新产品', 'light-fixture-child'),
        'new_item'             => __('新产品', 'light-fixture-child'),
        'edit_item'            => __('编辑产品', 'light-fixture-child'),
        'view_item'            => __('查看产品', 'light-fixture-child'),
        'all_items'            => __('所有产品', 'light-fixture-child'),
        'search_items'         => __('搜索产品', 'light-fixture-child'),
        'parent_item_colon'    => __('父级产品:', 'light-fixture-child'),
        'not_found'            => __('未找到产品', 'light-fixture-child'),
        'not_found_in_trash'   => __('回收站中未找到产品', 'light-fixture-child'),
        'featured_image'       => _x('产品封面图片', 'Overrides the "Featured Image" phrase', 'light-fixture-child'),
        'set_featured_image'   => _x('设置产品封面图片', 'Overrides the "Set featured image" phrase', 'light-fixture-child'),
        'remove_featured_image'=> _x('移除产品封面图片', 'Overrides the "Remove featured image" phrase', 'light-fixture-child'),
        'use_featured_image'   => _x('使用作为产品封面图片', 'Overrides the "Use as featured image" phrase', 'light-fixture-child'),
        'archives'             => _x('产品归档', 'The post type archive label used in nav menus', 'light-fixture-child'),
        'insert_into_item'     => _x('插入到产品', 'Overrides the "Insert into post" phrase', 'light-fixture-child'),
        'uploaded_to_this_item'=> _x('上传到此产品', 'Overrides the "Uploaded to this post" phrase', 'light-fixture-child'),
        'filter_items_list'    => _x('筛选产品列表', 'Screen reader text for the filter links heading on the post type listing screen', 'light-fixture-child'),
        'items_list_navigation'=> _x('产品列表导航', 'Screen reader text for the pagination heading on the post type listing screen', 'light-fixture-child'),
        'items_list'           => _x('产品列表', 'Screen reader text for the items list heading on the post type listing screen', 'light-fixture-child'),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'products'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'menu_icon'          => 'dashicons-lightbulb',
        'supports'           => array(
            'title',           // 标题
            'editor',          // 编辑器
            'thumbnail',       // 特色图片
            'excerpt',         // 摘要
            'custom-fields',   // 自定义字段
            'revisions',       // 修订版本
            'page-attributes', // 页面属性
        ),
        'show_in_rest'       => true,  // 启用 Gutenberg 编辑器
        'rest_base'          => 'products',
        'rest_controller_class' => 'WP_REST_Posts_Controller',
    );

    register_post_type('product', $args);
}
add_action('init', 'light_fixture_register_product_cpt');

/**
 * 注册产品分类法
 */
function light_fixture_register_product_taxonomy() {
    $labels = array(
        'name'              => '产品系列',
        'singular_name'     => '产品系列',
        'search_items'      => '搜索产品系列',
        'all_items'         => '所有产品系列',
        'parent_item'       => '父级产品系列',
        'parent_item_colon' => '父级产品系列:',
        'edit_item'         => '编辑产品系列',
        'update_item'       => '更新产品系列',
        'add_new_item'      => '添加新产品系列',
        'new_item_name'     => '新产品系列名称',
        'menu_name'         => '产品系列',
        'not_found'         => '未找到产品系列',
        'no_terms'          => '没有产品系列',
        'filter_by_item'    => '按产品系列筛选',
        'back_to_items'     => '← 返回产品系列',
        'item_link'         => '产品系列链接',
        'item_link_description' => '产品系列链接描述',
    );

    $args = array(
        'labels'            => $labels,
        'hierarchical'      => true, // 设置为分层结构，类似文章分类
        'public'            => true,
        'show_ui'           => true,
        'show_admin_column' => true, // 在文章列表中显示分类列
        'show_in_nav_menus' => true,
        'show_tagcloud'     => false,
        'query_var'         => true,
        'rewrite'           => array(
            'slug'          => 'product-category', // 自定义分类的URL别名
            'with_front'    => true,
            'hierarchical'  => true
        ),
        'capabilities'      => array(
            'manage_terms'  => 'manage_categories',
            'edit_terms'    => 'manage_categories',
            'delete_terms'  => 'manage_categories',
            'assign_terms'  => 'edit_posts'
        ),
        'show_in_rest'      => true, // 在古腾堡编辑器中显示
        'rest_base'         => 'product-category', // REST API 基础路径
        'rest_controller_class' => 'WP_REST_Terms_Controller',
    );

    register_taxonomy('product_category', array('product'), $args);
    
    // 确保产品分类的自定义字段在 REST API 中可用
    register_meta('term', 'product_category_order', array(
        'object_subtype' => 'product_category',
        'show_in_rest' => true,
        'type' => 'number',
        'single' => true,
        'sanitize_callback' => 'absint',
        'auth_callback' => function() {
            return current_user_can('edit_posts');
        }
    ));
    
    // 确保产品分类的图片在 REST API 中可用
    register_meta('term', 'category_image', array(
        'object_subtype' => 'product_category',
        'show_in_rest' => true,
        'type' => 'number',
        'single' => true,
        'sanitize_callback' => 'absint',
        'auth_callback' => function() {
            return current_user_can('edit_posts');
        }
    ));
}
add_action('init', 'light_fixture_register_product_taxonomy');

/**
 * 添加产品分类排序字段
 */
function light_fixture_add_product_category_fields() {
    ?>
    <div class="form-field term-order-wrap">
        <label for="product_category_order"><?php _e('排序顺序', 'light-fixture-child'); ?></label>
        <input type="number" name="product_category_order" id="product_category_order" value="0" min="0" />
        <p class="description"><?php _e('数值越小，排序越靠前。', 'light-fixture-child'); ?></p>
    </div>
    <?php
}
add_action('product_category_add_form_fields', 'light_fixture_add_product_category_fields');

/**
 * 编辑产品分类时显示排序字段
 */
function light_fixture_edit_product_category_fields($term) {
    $order = get_term_meta($term->term_id, 'product_category_order', true);
    if (!$order) {
        $order = 0;
    }
    ?>
    <tr class="form-field term-order-wrap">
        <th scope="row"><label for="product_category_order"><?php _e('排序顺序', 'light-fixture-child'); ?></label></th>
        <td>
            <input type="number" name="product_category_order" id="product_category_order" value="<?php echo esc_attr($order); ?>" min="0" />
            <p class="description"><?php _e('数值越小，排序越靠前。', 'light-fixture-child'); ?></p>
        </td>
    </tr>
    <?php
}
add_action('product_category_edit_form_fields', 'light_fixture_edit_product_category_fields');

/**
 * 保存产品分类排序字段
 */
function light_fixture_save_product_category_fields($term_id) {
    if (isset($_POST['product_category_order'])) {
        $order = intval($_POST['product_category_order']);
        update_term_meta($term_id, 'product_category_order', $order);
    }
}
add_action('created_product_category', 'light_fixture_save_product_category_fields');
add_action('edited_product_category', 'light_fixture_save_product_category_fields');

/**
 * 添加排序列到产品分类管理页面
 */
function light_fixture_add_product_category_columns($columns) {
    $new_columns = array();
    foreach ($columns as $key => $value) {
        $new_columns[$key] = $value;
        if ($key === 'name') {
            $new_columns['order'] = __('排序顺序', 'light-fixture-child');
        }
    }
    return $new_columns;
}
add_filter('manage_edit-product_category_columns', 'light_fixture_add_product_category_columns');

/**
 * 显示排序列内容
 */
function light_fixture_manage_product_category_columns($out, $column, $term_id) {
    if ($column === 'order') {
        $order = get_term_meta($term_id, 'product_category_order', true);
        if (!$order) {
            $order = 0;
        }
        $out = $order;
    }
    return $out;
}
add_filter('manage_product_category_custom_column', 'light_fixture_manage_product_category_columns', 10, 3);

/**
 * 添加排序列的可排序功能
 */
function light_fixture_product_category_sortable_columns($columns) {
    $columns['order'] = 'product_category_order';
    return $columns;
}
add_filter('manage_edit-product_category_sortable_columns', 'light_fixture_product_category_sortable_columns');

/**
 * 处理产品分类的排序
 */
function light_fixture_product_category_orderby($pieces, $taxonomies, $args) {
    if (is_admin() && isset($_GET['orderby']) && $_GET['orderby'] === 'product_category_order' && in_array('product_category', $taxonomies)) {
        global $wpdb;
        
        // 添加排序条件
        $pieces['join'] .= " LEFT JOIN {$wpdb->termmeta} AS tm ON t.term_id = tm.term_id AND tm.meta_key = 'product_category_order'";
        
        // 确保没有排序值的项目也能正常显示
        $pieces['where'] .= " OR tm.meta_key IS NULL";
        
        // 设置排序方式
        $order = isset($_GET['order']) ? strtoupper($_GET['order']) : 'ASC';
        $order = in_array($order, array('ASC', 'DESC')) ? $order : 'ASC';
        $pieces['orderby'] = "CAST(tm.meta_value AS SIGNED) {$order}, t.name ASC";
        
        // 移除分组，避免重复
        $pieces['group'] = '';
    }
    return $pieces;
}
add_filter('terms_clauses', 'light_fixture_product_category_orderby', 10, 3);

/**
 * 在快速编辑中添加排序字段
 */
function light_fixture_quick_edit_product_category($column_name, $screen, $name) {
    if ($column_name !== 'order') {
        return;
    }
    ?>
    <fieldset>
        <div class="inline-edit-col">
            <label>
                <span class="title"><?php _e('排序顺序', 'light-fixture-child'); ?></span>
                <span class="input-text-wrap">
                    <input type="number" name="product_category_order" class="ptitle" value="" min="0" />
                </span>
            </label>
        </div>
    </fieldset>
    <?php
}
add_action('quick_edit_custom_box', 'light_fixture_quick_edit_product_category', 10, 3);

/**
 * 添加JavaScript以支持快速编辑功能
 */
function light_fixture_product_category_quick_edit_js() {
    $screen = get_current_screen();
    
    // 只在产品分类管理页面添加脚本
    if ($screen && $screen->taxonomy === 'product_category') {
        ?>
        <script type="text/javascript">
        (function($) {
            // 保存每个分类的排序值
            var productCategoryOrders = {};
            
            // 当页面加载完成时，获取所有分类的排序值
            $(document).ready(function() {
                $('.column-order').each(function() {
                    var termId = $(this).closest('tr').attr('id').replace('tag-', '');
                    var orderValue = $.trim($(this).text());
                    productCategoryOrders[termId] = orderValue;
                });
            });
            
            // 当快速编辑按钮被点击时
            $(document).on('click', '.editinline', function() {
                // 获取当前行的分类ID
                var termId = $(this).closest('tr').attr('id').replace('tag-', '');
                
                // 设置快速编辑表单中的排序值
                $('input[name="product_category_order"]').val(productCategoryOrders[termId]);
            });
        })(jQuery);
        </script>
        <?php
    }
}
add_action('admin_footer', 'light_fixture_product_category_quick_edit_js'); 