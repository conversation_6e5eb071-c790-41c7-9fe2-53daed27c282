<?php
/**
 * 主题脚本与样式加载
 *
 * @package Light_Fixture_Child
 */

// 前端脚本与样式加载
function light_fixture_child_assets_setup() {
    // 加载 Playfair Display 字体
    wp_enqueue_style('playfair-display-font', 'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600;1,700&display=swap', array(), null);

    // 加载父主题和子主题样式
    wp_enqueue_style('parent-style', get_template_directory_uri() . '/style.css');
    wp_enqueue_style('child-style', get_stylesheet_uri(), array('parent-style', 'playfair-display-font'));

    // 确保 jQuery 作为依赖项
    wp_enqueue_script('jquery');
    
    // 加载区块样式
    wp_enqueue_style('light-fixture-blocks', get_stylesheet_directory_uri() . '/assets/css/blocks/blocks.css', array(), '1.0');

    // 加载 Slick 轮播图库 - 使用本地资源
    wp_enqueue_style('slick-css', get_stylesheet_directory_uri() . '/assets/css/vendor/slick.css', array(), '1.8.1');
    wp_enqueue_style('slick-theme-css', get_stylesheet_directory_uri() . '/assets/css/vendor/slick-theme.css', array('slick-css'), '1.8.1');
    wp_enqueue_script('slick-js', get_stylesheet_directory_uri() . '/assets/js/vendor/slick.min.js', array('jquery'), '1.8.1', true);

    // 加载自定义动画脚本
    wp_enqueue_script('light-fixture-animation', get_stylesheet_directory_uri() . '/js/animation.js', array('jquery'), '1.0', true);

    // Load slider script - ensure it loads after Slick
    wp_enqueue_script('light-fixture-slider', get_stylesheet_directory_uri() . '/assets/js/slider.js', array('jquery', 'slick-js'), '1.0', true);

    // Add inline CSS to optimize page loading experience
    $custom_css = "
        /* Page transition effects */
        .fade-in, .slide-up {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.5s ease, transform 0.5s ease;
        }
        /* 延迟动画 */
        .delay-1 { transition-delay: 0.1s; }
        .delay-2 { transition-delay: 0.2s; }
        .delay-3 { transition-delay: 0.3s; }
        .delay-4 { transition-delay: 0.4s; }
        .delay-5 { transition-delay: 0.5s; }
    ";
    wp_add_inline_style('child-style', $custom_css);

    // Optimize product archive page header spacing to avoid overlap with fixed navigation
    $archive_header_css = "
        .site-main .page-header {
            margin-top: 20px;
            padding-top: 20px;
        }
        @media screen and (max-width: 782px) {
            body.admin-bar .site-main .page-header {
                margin-top: 100px;
            }
        }
    ";
    wp_add_inline_style('child-style', $archive_header_css);

    // 加载自定义脚本
    wp_enqueue_script(
        'light-fixture-custom-js',
        get_stylesheet_directory_uri() . '/assets/js/custom.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // 加载前端脚本
    wp_enqueue_script(
        'light-fixture-frontend-js',
        get_stylesheet_directory_uri() . '/assets/js/frontend-scripts.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // 确保 admin-ajax.php 的 URL 正确
    wp_localize_script('light-fixture-frontend-js', 'ajax_object', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('light_fixture_nonce')
    ));

    // 加载WebP检测脚本
    wp_enqueue_script(
        'light-fixture-webp-detection',
        get_stylesheet_directory_uri() . '/inc/webp/webp-detection.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // 为WebP检测脚本提供配置
    wp_localize_script('light-fixture-webp-detection', 'webpDetection', array(
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('webp_detection_nonce'),
        'cookieName' => 'webp_support',
        'cookieExpire' => 30
    ));

    // Add inline CSS for critical rendering path optimization
    $inline_css = "
        .site-header {
            transition: all 0.3s ease;
        }
        .site-header.scrolled {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        .fade-in {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }
        .slide-up {
            opacity: 0;
            transform: translateY(40px);
            transition: opacity 0.8s ease, transform 0.8s ease;
        }
        .loading {
            position: relative;
            min-height: 200px;
        }
        .loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            margin: -20px 0 0 -20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    ";
    wp_add_inline_style('child-style', $inline_css);
    
    // Add styles for the left main menu panel itself
    $left_panel_container_css = "
        /* --- Left main menu panel and overlay styles --- */

        /* Overlay default state (hidden) */
        .left-panel-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            opacity: 0;
            visibility: hidden;
            z-index: 1099; /* Updated: significantly increased z-index */
            transition: opacity 0.2s ease, visibility 0s 0.2s; /* Faster response */
        }

        /* Overlay active state (visible) - immediate response to clicks */
        body.left-panel-open .left-panel-overlay {
            opacity: 1;
            visibility: visible;
            transition: opacity 0.2s ease, visibility 0s; /* 立即显示，快速淡入 */
        }

        /* 菜单容器默认状态 (隐藏在左侧屏幕外) */
        #left-panel-menu-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 320px; /* 您可以按需调整宽度 */
            max-width: 85%; /* 在小屏幕上不要占满全屏 */
            height: 100%;
            background-color: #ffffff; /* 纯白色背景 */
            border-right: 3px solid #000000; /* 黑色右边框 */
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15); /* 更明显的阴影 */
            transform: translateX(-100%);
            transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            z-index: 1100; /* 已更新：大幅提高 z-index，确保在最顶层 */
            overflow-y: auto; /* 当菜单项过多时可以滚动 */
            padding-top: 60px; /* 留出顶部空间 */
        }

        /* 菜单容器激活状态 (滑入屏幕) */
        #left-panel-menu-container.active {
            transform: translateX(0);
        }

        /* 菜单项的基础样式 */
        #left-panel-menu-container .left-panel-menu {
            padding: 0 20px;
            margin: 0;
            list-style: none;
        }
        #left-panel-menu-container .left-panel-menu a {
            display: block;
            padding: 12px 0;
            color: #000000; /* 黑色字体 */
            text-decoration: none;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1); /* 深色分隔线 */
            border-left: 3px solid transparent;
            padding-left: 1rem;
            margin-left: -1rem;
            font-family: 'Playfair Display', 'Georgia', serif;
            transition: all 0.3s ease;
            font-size: 15px; /* 缩小一级字体 */
        }
        #left-panel-menu-container .left-panel-menu a:hover {
            background-color: rgba(0, 0, 0, 0.05); /* 悬停效果微调 */
            color: #d6ad60; /* 金色文字 */
            border-left-color: #d6ad60; /* 金色左边框 */
        }

        /* 已更新：使用更高特异性的选择器来强制覆盖子菜单样式 */
        #left-panel-menu-container .sub-menu {
            background-color: rgba(0, 0, 0, 0.03); /* 浅灰色背景 */
            border-left: 2px solid #d6ad60; /* 金色左边框 */
            padding-left: 2rem;
        }

        #left-panel-menu-container .sub-menu a {
            font-size: 14px; /* 缩小二级字体 */
            color: #666 !important; /* 已更新：使用 !important 确保颜色生效 */
            padding-left: 0.5rem;
            margin-left: -0.5rem;
        }

        #left-panel-menu-container .sub-menu a:hover {
            color: #d6ad60 !important; /* 已更新：确保悬停颜色生效 */
            background-color: rgba(214, 173, 96, 0.1) !important; /* 浅金色背景 */
        }
    ";
    wp_add_inline_style('child-style', $left_panel_container_css);
    
    // 为左侧面板菜单的展开/折叠功能添加样式
    $left_panel_menu_css = "
        /* --- 左侧面板菜单展开/折叠样式 --- */

        /* 为包含子菜单的父菜单项链接腾出图标空间 */
        #left-panel-menu .menu-item-has-children > a {
            position: relative; /* 这是定位图标的必要条件 */
            padding-right: 50px; /* 确保文本不会与图标重叠 */
        }

        /* '+'/'-' 图标的默认样式 */
        #left-panel-menu .menu-item-has-children > a::after {
            content: '+'; /* 默认显示加号 */
            position: absolute;
            right: 15px; /* 图标距离右侧的距离 */
            top: 50%;
            transform: translateY(-50%);
            font-size: 22px;
            font-weight: 300;
            color: inherit; /* 继承链接的颜色 */
            transition: transform 0.3s ease-in-out;
            line-height: 1;
        }

        /* 当菜单展开时，将'+'变为'-' */
        #left-panel-menu .menu-item-has-children.open > a::after {
            content: '\\2013'; /* 使用一个更美观的减号 */
            transform: translateY(-50%) rotate(360deg);
        }

        /* 子菜单的默认状态 (折叠) */
        #left-panel-menu .sub-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease-out; /* 关键：平滑过渡效果 */
            list-style: none; /* 移除默认的项目符号 */
            padding-left: 20px; /* 为子菜单添加缩进 */
        }

        /* 子菜单项的样式 */
        #left-panel-menu .sub-menu li a {
            font-size: 0.95em; /* 让子菜单字体稍小 */
            color: rgba(255, 255, 255, 0.7); /* 假设是深色背景下的浅色文字 */
        }

        #left-panel-menu .sub-menu li a:hover {
            color: #fff;
        }
    ";
    wp_add_inline_style('child-style', $left_panel_menu_css);
}
add_action('wp_enqueue_scripts', 'light_fixture_child_assets_setup');

// 后台脚本加载
function light_fixture_admin_scripts($hook) {
    // 仅在产品编辑页面加载
    if ($hook == 'post.php' || $hook == 'post-new.php') {
        global $post;
        
        if ($post && $post->post_type === 'product') {
            // 确保加载WordPress编辑器相关脚本
            wp_enqueue_editor();
        }
    }
}
add_action('admin_enqueue_scripts', 'light_fixture_admin_scripts');

// 后台产品图集脚本和样式
function light_fixture_enqueue_gallery_scripts($hook) {
    global $post_type;
    if ($hook == 'post-new.php' || $hook == 'post.php') {
        if ($post_type == 'product') {
            wp_enqueue_media();
            wp_enqueue_script(
                'light-fixture-gallery-admin',
                get_stylesheet_directory_uri() . '/js/gallery-admin.js',
                array('jquery', 'jquery-ui-sortable'),
                '1.0.0',
                true
            );

            // 加载产品特点管理脚本
            wp_enqueue_script(
                'light-fixture-features-admin',
                get_stylesheet_directory_uri() . '/js/features-admin.js',
                array('jquery'),
                '1.0.0',
                true
            );

            // 加载技术规格管理脚本
            wp_enqueue_script(
                'light-fixture-specs-admin',
                get_stylesheet_directory_uri() . '/js/specs-admin.js',
                array('jquery'),
                '1.0.0',
                true
            );

            // 加载定制选项管理脚本
            wp_enqueue_script(
                'light-fixture-options-admin',
                get_stylesheet_directory_uri() . '/js/options-group-admin.js',
                array('jquery'),
                '1.0.0',
                true
            );
            $custom_css = "
                .light-fixture-gallery-images {
                    display: flex;
                    flex-wrap: wrap;
                    gap: 10px;
                    margin: 10px 0;
                }
                .light-fixture-gallery-images li {
                    position: relative;
                    width: 150px;
                    height: 150px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    overflow: hidden;
                }
                .light-fixture-gallery-images img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }
                .light-fixture-gallery-remove {
                    position: absolute;
                    top: 5px;
                    right: 5px;
                    color: #fff;
                    background: rgba(0,0,0,0.5);
                    border-radius: 50%;
                    padding: 2px;
                    text-decoration: none;
                    line-height: 1;
                }
                .light-fixture-gallery-remove:hover {
                    background: rgba(0,0,0,0.7);
                    color: #fff;
                }
            ";
            wp_add_inline_style('wp-admin', $custom_css);
        }
    }
}
add_action('admin_enqueue_scripts', 'light_fixture_enqueue_gallery_scripts');

/**
 * 加载区块编辑器脚本和样式
 */
function light_fixture_block_editor_assets() {
    // 轮播图区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-blocks-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/slider-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 产品类目导航区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-product-categories-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/product-categories-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );
    
    // 产品列表区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-products-list-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/products-list-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n', 'wp-api-fetch'),
        '1.0',
        true
    );

    // 关于英雄区域区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-about-hero-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/about-hero-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 关于品牌故事区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-about-brand-story-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/about-brand-story-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 关于我们的理念区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-about-philosophy-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/about-philosophy-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 关于设计团队区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-about-team-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/about-team-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 工艺展示卡片区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-craftsmanship-cards-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/craftsmanship-cards-block.js',
        array('wp-blocks', 'wp-element', 'wp-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 图片画廊墙区块编辑器脚本
    wp_enqueue_script(
        'light-fixture-gallery-wall-editor',
        get_stylesheet_directory_uri() . '/assets/js/blocks/gallery-wall-block.js',
        array('wp-blocks', 'wp-element', 'wp-block-editor', 'wp-components', 'wp-i18n'),
        '1.0',
        true
    );

    // 轮播图区块编辑器样式
    wp_enqueue_style(
        'light-fixture-blocks-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/slider-block-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 产品类目导航区块编辑器样式
    wp_enqueue_style(
        'light-fixture-product-categories-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/product-categories-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );
    
    // 产品列表区块编辑器样式
    wp_enqueue_style(
        'light-fixture-products-list-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/products-list-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 关于英雄区域区块编辑器样式
    wp_enqueue_style(
        'light-fixture-about-hero-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/about-hero-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 关于品牌故事区块编辑器样式
    wp_enqueue_style(
        'light-fixture-about-brand-story-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/about-brand-story-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 关于我们的理念区块编辑器样式
    wp_enqueue_style(
        'light-fixture-about-philosophy-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/about-philosophy-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 关于设计团队区块编辑器样式
    wp_enqueue_style(
        'light-fixture-about-team-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/about-team-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 工艺展示卡片区块编辑器样式
    wp_enqueue_style(
        'light-fixture-craftsmanship-cards-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/craftsmanship-cards-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );

    // 图片画廊墙区块编辑器样式
    wp_enqueue_style(
        'light-fixture-gallery-wall-editor',
        get_stylesheet_directory_uri() . '/assets/css/blocks/gallery-wall-editor.css',
        array('wp-edit-blocks'),
        '1.0'
    );
}
add_action('enqueue_block_editor_assets', 'light_fixture_block_editor_assets');

/**
 * 添加编辑器样式
 */
function light_fixture_add_editor_styles() {
    // 添加编辑器样式文件
    add_editor_style( 'assets/css/editor-style.css' );

    // 添加Google字体到编辑器
    add_editor_style( 'https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500&display=swap' );
}
add_action( 'after_setup_theme', 'light_fixture_add_editor_styles' );