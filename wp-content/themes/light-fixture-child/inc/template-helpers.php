<?php
/**
 * 模板辅助函数
 *
 * @package Light_Fixture_Child
 */

/**
 * 强制 WordPress 使用自定义的 archive-product.php 模板
 *
 * 当访问 'product' 文章类型归档或 'product_category' 分类归档时，
 * 确保使用子主题中的 archive-product.php 文件来渲染。
 * 这对于覆盖块主题的默认行为尤其重要。
 *
 * @param string $template 当前要加载的模板文件路径。
 * @return string 修改后的模板文件路径。
 */
function light_fixture_force_product_archive_template( $template ) {
    // 如果当前是 'product' 自定义文章类型归档页
    // 或者当前是 'product_category' 自定义分类法归档页
    if ( is_post_type_archive( 'product' ) || is_tax( 'product_category' ) ) {
        $custom_template_path = get_stylesheet_directory() . '/archive-product.php';
        // 检查我们的自定义模板文件是否存在
        if ( file_exists( $custom_template_path ) ) {
            return $custom_template_path;
        }
    }
    // 否则，返回默认的模板路径
    return $template;
}
add_filter( 'template_include', 'light_fixture_force_product_archive_template', 99 ); // 使用高优先级确保生效

/**
 * 生成自定义面包屑导航
 *
 * 根据当前页面类型（产品详情页或产品分类归档页）生成面包屑导航 HTML。
 *
 * @param array $args 可选参数，用于自定义面包屑的输出。
 */
function light_fixture_breadcrumbs( $args = array() ) {
    $defaults = array(
        'separator'   => ' › ', // 面包屑分隔符
        'before'      => '<nav class="light-fixture-breadcrumbs" aria-label="Breadcrumb">', // 面包屑容器开始标签
        'after'       => '</nav>', // 面包屑容器结束标签
        'show_home'   => true, // 是否显示首页链接
        'show_current'=> true, // 是否显示当前页面
        'home_text'   => 'Home', // 首页链接文本
        'product_archive_text' => 'All Products', // 产品归档页文本
    );
    $args = wp_parse_args( $args, $defaults );

    $breadcrumbs = array();

    // 首页
    if ( $args['show_home'] ) {
        $breadcrumbs[] = '<a href="' . esc_url( home_url( '/' ) ) . '">' . esc_html( $args['home_text'] ) . '</a>';
    }

    // 产品归档页或产品分类归档页
    if ( is_singular( 'product' ) ) {
        // 产品详情页
        $product_archive_link = get_post_type_archive_link( 'product' );
        if ( $product_archive_link ) {
            $breadcrumbs[] = '<a href="' . esc_url( $product_archive_link ) . '">' . esc_html( $args['product_archive_text'] ) . '</a>';
        }

        // 产品分类
        $terms = get_the_terms( get_the_ID(), 'product_category' );
        if ( $terms && ! is_wp_error( $terms ) ) {
            $term = array_shift( $terms ); // 只取第一个分类
            $breadcrumbs[] = '<a href="' . esc_url( get_term_link( $term->term_id, 'product_category' ) ) . '">' . esc_html( $term->name ) . '</a>';
        }

        // 当前产品标题
        if ( $args['show_current'] ) {
            $breadcrumbs[] = '<span class="current">' . esc_html( get_the_title() ) . '</span>';
        }
    } elseif ( is_post_type_archive( 'product' ) ) {
        // 产品归档主页 (e.g., /products/)
        if ( $args['show_current'] ) {
            $breadcrumbs[] = '<span class="current">' . esc_html( $args['product_archive_text'] ) . '</span>';
        }
    } elseif ( is_tax( 'product_category' ) ) {
        // 产品分类归档页 (e.g., /product-category/mirrors/)
        $term = get_queried_object();
        $product_archive_link = get_post_type_archive_link( 'product' );
        if ( $product_archive_link ) {
            $breadcrumbs[] = '<a href="' . esc_url( $product_archive_link ) . '">' . esc_html( $args['product_archive_text'] ) . '</a>';
        }
        if ( $args['show_current'] ) {
            $breadcrumbs[] = '<span class="current">' . esc_html( $term->name ) . '</span>';
        }
    }

    // 输出面包屑
    if ( ! empty( $breadcrumbs ) ) {
        echo $args['before'];
        echo implode( $args['separator'], $breadcrumbs );
        echo $args['after'];
    }
} 