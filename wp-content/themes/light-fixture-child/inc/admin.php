<?php
/**
 * 后台管理功能
 *
 * @package Light_Fixture_Child
 */

/**
 * 仅对非管理员隐藏后台管理栏
 */
if ( ! current_user_can( 'manage_options' ) ) {
    add_filter('show_admin_bar', '__return_false');
}

/**
 * 清理 WordPress 缓存
 */
function light_fixture_clear_cache() {
    global $wpdb;
    
    // 清理对象缓存
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
    
    // 清理瞬态缓存
    $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_transient_%'");
    $wpdb->query("DELETE FROM $wpdb->options WHERE option_name LIKE '%_site_transient_%'");
    
    // 清理重写规则
    delete_option('rewrite_rules');
    
    // 清理主题缓存
    delete_site_transient('update_themes');
    delete_site_transient('update_plugins');
    delete_site_transient('update_core');
    
    // 清理菜单缓存
    delete_site_transient('wp_nav_menu_cache');
    
    // 清理分类缓存
    $taxonomies = get_taxonomies();
    if (!empty($taxonomies)) {
        foreach ($taxonomies as $taxonomy) {
            $terms = get_terms(array(
                'taxonomy' => $taxonomy,
                'hide_empty' => false,
                'fields' => 'ids'
            ));
            
            if (!is_wp_error($terms) && !empty($terms)) {
                foreach ($terms as $term_id) {
                    clean_term_cache($term_id, $taxonomy);
                }
            }
        }
    }
    
    // 清理评论缓存
    $comments = get_comments(array(
        'fields' => 'ids',
        'number' => -1
    ));
    
    if (!empty($comments)) {
        foreach ($comments as $comment_id) {
            clean_comment_cache($comment_id);
        }
    }
    
    // 清理文章缓存
    $posts = get_posts(array(
        'post_type' => 'any',
        'posts_per_page' => -1,
        'fields' => 'ids'
    ));
    
    if (!empty($posts)) {
        foreach ($posts as $post_id) {
            clean_post_cache($post_id);
        }
    }
    
    // 清理用户缓存
    $users = get_users(array(
        'fields' => 'ID'
    ));
    
    if (!empty($users)) {
        foreach ($users as $user_id) {
            clean_user_cache($user_id);
        }
    }
    
    // 刷新重写规则
    flush_rewrite_rules();
    
    // 清理其他可能的缓存
    if (function_exists('wp_cache_clear_cache')) {
        wp_cache_clear_cache();
    }
    
    // 清理对象缓存（再次确保）
    if (function_exists('wp_cache_flush')) {
        wp_cache_flush();
    }
}

/**
 * 添加缓存管理菜单到工具菜单下
 */
function light_fixture_add_cache_menu() {
    add_submenu_page(
        'tools.php', // 父菜单 slug
        '缓存管理',
        '缓存管理',
        'manage_options',
        'light-fixture-cache',
        'light_fixture_cache_page'
    );
}
add_action('admin_menu', 'light_fixture_add_cache_menu');

/**
 * 缓存管理页面
 */
function light_fixture_cache_page() {
    if (isset($_POST['clear_cache']) && check_admin_referer('light_fixture_clear_cache')) {
        try {
            light_fixture_clear_cache();
            echo '<div class="notice notice-success"><p>缓存已成功清理！</p></div>';
        } catch (Exception $e) {
            echo '<div class="notice notice-error"><p>清理缓存时发生错误：' . esc_html($e->getMessage()) . '</p></div>';
        }
    }
    ?>
    <div class="wrap">
        <h1>WordPress 缓存管理</h1>
        <form method="post" action="">
            <?php wp_nonce_field('light_fixture_clear_cache'); ?>
            <p>点击下面的按钮清理所有 WordPress 缓存：</p>
            <input type="submit" name="clear_cache" class="button button-primary" value="清理所有缓存">
        </form>
    </div>
    <?php
}

// 在主题切换时清理缓存
add_action('switch_theme', 'light_fixture_clear_cache');

// 在插件激活/停用时清理缓存
add_action('activated_plugin', 'light_fixture_clear_cache');
add_action('deactivated_plugin', 'light_fixture_clear_cache');

/**
 * 添加产品分类排序字段的样式
 */
function light_fixture_product_category_admin_styles() {
    $screen = get_current_screen();
    
    // 只在产品分类管理页面添加样式
    if ($screen && $screen->taxonomy === 'product_category') {
        ?>
        <style type="text/css">
            .column-order {
                width: 100px;
                text-align: center;
            }
            #product_category_order {
                width: 100px;
            }
            .term-order-wrap .description {
                margin-top: 5px;
            }
        </style>
        <?php
    }
}
add_action('admin_head', 'light_fixture_product_category_admin_styles'); 