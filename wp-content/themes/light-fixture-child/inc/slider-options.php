<?php
/**
 * 首页轮播图设置
 *
 * @package Light_Fixture_Child
 */

/**
 * 注册轮播图设置
 */
function light_fixture_slider_settings() {
    // 注册设置
    register_setting(
        'light_fixture_slider_options',
        'light_fixture_slider_settings',
        array(
            'sanitize_callback' => 'light_fixture_sanitize_slider_settings',
            'default' => array()
        )
    );

    // 添加设置区域
    add_settings_section(
        'light_fixture_slider_section',
        '首页轮播图设置',
        'light_fixture_slider_section_callback',
        'light_fixture_slider'
    );

    // 添加轮播图数量字段
    add_settings_field(
        'slider_count',
        '轮播图数量',
        'light_fixture_slider_count_callback',
        'light_fixture_slider',
        'light_fixture_slider_section'
    );

    // 动态添加轮播图字段
    $slider_settings = get_option('light_fixture_slider_settings');
    $slider_count = isset($slider_settings['slider_count']) ? intval($slider_settings['slider_count']) : 3;
    
    for ($i = 1; $i <= $slider_count; $i++) {
        add_settings_field(
            'slider_' . $i,
            '轮播图 #' . $i,
            'light_fixture_slider_field_callback',
            'light_fixture_slider',
            'light_fixture_slider_section',
            array('index' => $i)
        );
    }
}
add_action('admin_init', 'light_fixture_slider_settings');

/**
 * 轮播图设置区域回调
 */
function light_fixture_slider_section_callback() {
    echo '<p>配置首页轮播图的图片、标题、描述和链接。</p>';
}

/**
 * 轮播图数量字段回调
 */
function light_fixture_slider_count_callback() {
    $slider_settings = get_option('light_fixture_slider_settings');
    $slider_count = isset($slider_settings['slider_count']) ? intval($slider_settings['slider_count']) : 3;
    
    echo '<input type="number" id="slider_count" name="light_fixture_slider_settings[slider_count]" value="' . esc_attr($slider_count) . '" min="1" max="10" />';
    echo '<p class="description">设置轮播图的数量（保存后将显示相应数量的轮播图设置字段）</p>';
}

/**
 * 轮播图字段回调
 */
function light_fixture_slider_field_callback($args) {
    $index = $args['index'];
    $slider_settings = get_option('light_fixture_slider_settings');
    
    $image = isset($slider_settings['slider_' . $index . '_image']) ? $slider_settings['slider_' . $index . '_image'] : '';
    $title = isset($slider_settings['slider_' . $index . '_title']) ? $slider_settings['slider_' . $index . '_title'] : '';
    $subtitle = isset($slider_settings['slider_' . $index . '_subtitle']) ? $slider_settings['slider_' . $index . '_subtitle'] : '';
    $description = isset($slider_settings['slider_' . $index . '_description']) ? $slider_settings['slider_' . $index . '_description'] : '';
    $button_text = isset($slider_settings['slider_' . $index . '_button_text']) ? $slider_settings['slider_' . $index . '_button_text'] : '';
    $button_url = isset($slider_settings['slider_' . $index . '_button_url']) ? $slider_settings['slider_' . $index . '_button_url'] : '';
    $button2_text = isset($slider_settings['slider_' . $index . '_button2_text']) ? $slider_settings['slider_' . $index . '_button2_text'] : '';
    $button2_url = isset($slider_settings['slider_' . $index . '_button2_url']) ? $slider_settings['slider_' . $index . '_button2_url'] : '';
    
    // 图片上传字段
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_image">背景图片</label><br>';
    echo '<input type="text" id="slider_' . $index . '_image" name="light_fixture_slider_settings[slider_' . $index . '_image]" value="' . esc_attr($image) . '" class="regular-text slider-image-url" />';
    echo '<input type="button" class="button slider-upload-button" value="选择图片" data-target="slider_' . $index . '_image" />';
    if (!empty($image)) {
        // 优化预览图片URL
        $preview_image = $image;
        if (function_exists('light_fixture_get_optimized_image_url')) {
            $preview_image = light_fixture_get_optimized_image_url($image);
        }
        echo '<div class="slider-image-preview"><img src="' . esc_url($preview_image) . '" style="max-width: 200px; margin-top: 10px;" onerror="this.src=\'' . esc_url($image) . '\'; this.onerror=null;" /></div>';
    }
    echo '</div>';
    
    // 标题字段
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_title">标题</label><br>';
    echo '<input type="text" id="slider_' . $index . '_title" name="light_fixture_slider_settings[slider_' . $index . '_title]" value="' . esc_attr($title) . '" class="regular-text" />';
    echo '</div>';
    
    // 副标题字段
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_subtitle">副标题</label><br>';
    echo '<input type="text" id="slider_' . $index . '_subtitle" name="light_fixture_slider_settings[slider_' . $index . '_subtitle]" value="' . esc_attr($subtitle) . '" class="regular-text" />';
    echo '</div>';
    
    // 描述字段
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_description">描述</label><br>';
    echo '<textarea id="slider_' . $index . '_description" name="light_fixture_slider_settings[slider_' . $index . '_description]" class="large-text" rows="3">' . esc_textarea($description) . '</textarea>';
    echo '</div>';
    
    // 按钮1文本和URL
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_button_text">按钮1文本</label><br>';
    echo '<input type="text" id="slider_' . $index . '_button_text" name="light_fixture_slider_settings[slider_' . $index . '_button_text]" value="' . esc_attr($button_text) . '" class="regular-text" />';
    echo '</div>';
    
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_button_url">按钮1链接</label><br>';
    echo '<input type="text" id="slider_' . $index . '_button_url" name="light_fixture_slider_settings[slider_' . $index . '_button_url]" value="' . esc_attr($button_url) . '" class="regular-text" />';
    echo '</div>';
    
    // 按钮2文本和URL
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_button2_text">按钮2文本</label><br>';
    echo '<input type="text" id="slider_' . $index . '_button2_text" name="light_fixture_slider_settings[slider_' . $index . '_button2_text]" value="' . esc_attr($button2_text) . '" class="regular-text" />';
    echo '</div>';
    
    echo '<div class="slider-field-group">';
    echo '<label for="slider_' . $index . '_button2_url">按钮2链接</label><br>';
    echo '<input type="text" id="slider_' . $index . '_button2_url" name="light_fixture_slider_settings[slider_' . $index . '_button2_url]" value="' . esc_attr($button2_url) . '" class="regular-text" />';
    echo '</div>';
    
    echo '<hr>';
}

/**
 * 数据清理
 */
function light_fixture_sanitize_slider_settings($input) {
    $sanitized_input = array();
    
    // 清理轮播图数量
    $sanitized_input['slider_count'] = isset($input['slider_count']) ? intval($input['slider_count']) : 3;
    
    // 限制最大数量为10
    if ($sanitized_input['slider_count'] > 10) {
        $sanitized_input['slider_count'] = 10;
    }
    
    // 清理每个轮播图的数据
    for ($i = 1; $i <= $sanitized_input['slider_count']; $i++) {
        $sanitized_input['slider_' . $i . '_image'] = isset($input['slider_' . $i . '_image']) ? esc_url_raw($input['slider_' . $i . '_image']) : '';
        $sanitized_input['slider_' . $i . '_title'] = isset($input['slider_' . $i . '_title']) ? sanitize_text_field($input['slider_' . $i . '_title']) : '';
        $sanitized_input['slider_' . $i . '_subtitle'] = isset($input['slider_' . $i . '_subtitle']) ? sanitize_text_field($input['slider_' . $i . '_subtitle']) : '';
        $sanitized_input['slider_' . $i . '_description'] = isset($input['slider_' . $i . '_description']) ? wp_kses_post($input['slider_' . $i . '_description']) : '';
        $sanitized_input['slider_' . $i . '_button_text'] = isset($input['slider_' . $i . '_button_text']) ? sanitize_text_field($input['slider_' . $i . '_button_text']) : '';
        $sanitized_input['slider_' . $i . '_button_url'] = isset($input['slider_' . $i . '_button_url']) ? esc_url_raw($input['slider_' . $i . '_button_url']) : '';
        $sanitized_input['slider_' . $i . '_button2_text'] = isset($input['slider_' . $i . '_button2_text']) ? sanitize_text_field($input['slider_' . $i . '_button2_text']) : '';
        $sanitized_input['slider_' . $i . '_button2_url'] = isset($input['slider_' . $i . '_button2_url']) ? esc_url_raw($input['slider_' . $i . '_button2_url']) : '';
    }
    
    return $sanitized_input;
}

/**
 * 添加轮播图设置菜单
 */
function light_fixture_add_slider_menu() {
    add_theme_page(
        '首页轮播图设置',
        '首页轮播图',
        'manage_options',
        'light-fixture-slider',
        'light_fixture_slider_page'
    );
}
add_action('admin_menu', 'light_fixture_add_slider_menu');

/**
 * 轮播图设置页面
 */
function light_fixture_slider_page() {
    ?>
    <div class="wrap">
        <h1>首页轮播图设置</h1>
        <form method="post" action="options.php">
            <?php
            settings_fields('light_fixture_slider_options');
            do_settings_sections('light_fixture_slider');
            submit_button('保存设置');
            ?>
        </form>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        // 图片上传功能
        $('.slider-upload-button').click(function(e) {
            e.preventDefault();
            
            var button = $(this);
            var targetId = button.data('target');
            
            var mediaUploader = wp.media({
                title: '选择图片',
                button: {
                    text: '使用此图片'
                },
                multiple: false
            });
            
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                $('#' + targetId).val(attachment.url);
                
                // 更新预览
                var previewContainer = button.siblings('.slider-image-preview');
                if (previewContainer.length === 0) {
                    button.after('<div class="slider-image-preview"><img src="' + attachment.url + '" style="max-width: 200px; margin-top: 10px;" /></div>');
                } else {
                    previewContainer.html('<img src="' + attachment.url + '" style="max-width: 200px; margin-top: 10px;" />');
                }
            });
            
            mediaUploader.open();
        });
    });
    </script>
    
    <style>
    .slider-field-group {
        margin-bottom: 15px;
    }
    </style>
    <?php
}

/**
 * 加载媒体上传脚本
 */
function light_fixture_slider_admin_scripts($hook) {
    if ('appearance_page_light-fixture-slider' !== $hook) {
        return;
    }
    
    wp_enqueue_media();
}
add_action('admin_enqueue_scripts', 'light_fixture_slider_admin_scripts'); 