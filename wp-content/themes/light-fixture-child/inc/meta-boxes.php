<?php
/**
 * 产品元数据框管理
 *
 * @package Light_Fixture_Child
 */

/**
 * 强制编辑器默认为HTML（文本）模式的辅助函数
 * @return string
 */
function light_fixture_force_html_editor() {
    return 'html';
}

/**
 * 注册所有产品元数据框
 */
function light_fixture_register_all_meta_boxes() {
    // 产品图集元框
    add_meta_box(
        'light_fixture_product_gallery',
        '产品图集',
        'light_fixture_product_gallery_meta_box_callback',
        'product',
        'normal',
        'high'
    );

    // 产品基本信息元框
    add_meta_box(
        'light_fixture_product_info',
        '产品基本信息',
        'light_fixture_product_meta_box_callback',
        'product',
        'normal',
        'high'
    );

    // 产品固定描述元框
    add_meta_box(
        'product_fixed_desc',
        '产品固定描述',
        'product_fixed_desc_callback',
        'product',
        'normal',
        'high'
    );

    // 产品特点元框
    add_meta_box(
        'light_fixture_product_features',
        '产品特点',
        'light_fixture_product_features_meta_box_callback',
        'product',
        'normal',
        'high'
    );

    // 产品技术规格元框
    add_meta_box(
        'light_fixture_product_specifications',
        '技术规格',
        'light_fixture_product_specs_meta_box_callback',
        'product',
        'normal',
        'high'
    );

    // 产品定制选项元框
    add_meta_box(
        'light_fixture_product_customization',
        '定制选项',
        'light_fixture_product_options_meta_box_callback',
        'product',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes_product', 'light_fixture_register_all_meta_boxes');

/**
 * 渲染产品图集元框内容
 */
function light_fixture_product_gallery_meta_box_callback($post) {
    wp_nonce_field('light_fixture_product_gallery_meta_box', 'light_fixture_product_gallery_nonce');

    $product_gallery_ids = get_post_meta($post->ID, '_product_gallery_ids', true);
    $gallery_ids_array = !empty($product_gallery_ids) ? explode(',', $product_gallery_ids) : array();
    ?>
    <div id="light-fixture-gallery-container">
        <ul class="light-fixture-gallery-images">
            <?php
            if (!empty($gallery_ids_array)) {
                foreach ($gallery_ids_array as $image_id) {
                    if (!empty($image_id)) {
                        echo '<li data-id="' . esc_attr($image_id) . '">';
                        echo wp_get_attachment_image($image_id, 'thumbnail');
                        echo '<a href="#" class="light-fixture-gallery-remove dashicons dashicons-no-alt"></a>';
                        echo '</li>';
                    }
                }
            }
            ?>
        </ul>
        <input type="hidden" id="light_fixture_product_gallery_ids" name="_product_gallery_ids" value="<?php echo esc_attr($product_gallery_ids); ?>">
        <button class="button button-secondary" id="light-fixture-add-gallery-image">添加或选择图片</button>
    </div>
    <p class="description">点击"添加或选择图片"按钮来管理产品图集。您可以拖放图片以重新排序，点击"X"移除。</p>
    <?php
}

/**
 * 渲染产品基本信息元框内容
 */
function light_fixture_product_meta_box_callback($post) {
    wp_nonce_field('light_fixture_product_info_meta_box', 'light_fixture_product_info_nonce');

    $product_sku = get_post_meta($post->ID, '_product_sku', true);
    $product_status = get_post_meta($post->ID, '_product_status', true);
    ?>
    <p>
        <label for="light_fixture_product_sku">产品 SKU:</label>
        <br>
        <input type="text" id="light_fixture_product_sku" name="_product_sku" value="<?php echo esc_attr($product_sku); ?>" class="large-text">
    </p>
    <p>
        <label for="light_fixture_product_status">产品状态:</label>
        <br>
        <input type="text" id="light_fixture_product_status" name="_product_status" value="<?php echo esc_attr($product_status); ?>" class="large-text">
    </p>
    <?php
}

/**
 * 产品固定描述元框回调函数
 */
function product_fixed_desc_callback($post) {
    // 创建nonce
    wp_nonce_field('product_fixed_desc_nonce', 'product_fixed_desc_nonce');
    
    // 获取已保存的值
    $value = get_post_meta($post->ID, '_product_fixed_desc', true);
    
    // 使用 wp_kses_post 清理内容，防止恶意或损坏的HTML破坏编辑器
    $value = wp_kses_post($value);

    // 如果没有已保存的值，添加示例文字
    if (empty($value)) {
        $value = '<h3>产品介绍</h3><p>这里可以添加产品的详细介绍，包括产品的特点、用途、设计理念等内容。</p><p>您可以使用编辑器工具栏添加格式，如<strong>粗体</strong>、<em>斜体</em>、列表、链接等。</p><p>也可以通过媒体按钮插入图片，展示产品的细节。</p>';
    }
    
    // 使用最基本的WordPress编辑器设置
    ?>
    <div class="product-fixed-desc-wrapper">
        <?php
        // 核心修复：强制此编辑器实例默认以HTML（文本）模式加载，以规避竞争条件错误。
        add_filter('wp_default_editor', 'light_fixture_force_html_editor');

        wp_editor(
            $value,
            'lf_fixed_desc_editor',
            array(
                'textarea_name' => '_product_fixed_desc',
                'editor_height' => 300,
                'media_buttons' => true,
                'teeny'         => false,
                'wpautop'       => false
            )
        );

        // 移除过滤器，避免影响页面上其他的编辑器实例
        remove_filter('wp_default_editor', 'light_fixture_force_html_editor');
        ?>
        <p><small>添加产品固定描述内容，将显示在产品详情页的主要内容区域上方。</small></p>
    </div>
    <?php
}

/**
 * 渲染产品特点元框内容
 */
function light_fixture_product_features_meta_box_callback($post) {
    wp_nonce_field('light_fixture_product_features_meta_box', 'light_fixture_product_features_nonce');

    $product_features = get_post_meta($post->ID, '_product_features', true);
    $features_array = !empty($product_features) && is_array($product_features) ? $product_features : array();
    ?>
    <div id="light-fixture-features-container">
        <ul class="light-fixture-features-list">
            <?php if (!empty($features_array)) : ?>
                <?php foreach ($features_array as $index => $feature) : ?>
                    <li>
                        <input type="text" name="_product_features[]" value="<?php echo esc_attr($feature); ?>" class="large-text" placeholder="输入产品特点">
                        <button type="button" class="button button-secondary light-fixture-remove-feature">移除</button>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>
        <button type="button" class="button button-primary" id="light-fixture-add-feature">添加特点</button>
    </div>
    <p class="description">点击"添加特点"按钮添加新的特点。输入文本后，点击"移除"可以删除。</p>
    <?php
}

/**
 * 渲染产品技术规格元框内容
 */
function light_fixture_product_specs_meta_box_callback($post) {
    wp_nonce_field('light_fixture_product_specs_meta_box', 'light_fixture_product_specs_nonce');

    $product_specs = get_post_meta($post->ID, '_product_specifications', true);
    $specs_array = !empty($product_specs) && is_array($product_specs) ? $product_specs : array();
    ?>
    <div id="light-fixture-specifications-container">
        <table class="light-fixture-specifications-table widefat fixed striped">
            <thead>
                <tr>
                    <th style="width: 45%;">规格名称</th>
                    <th style="width: 45%;">规格值</th>
                    <th style="width: 10%;"></th>
                </tr>
            </thead>
            <tbody class="light-fixture-specs-list">
                <?php if (!empty($specs_array)) : ?>
                    <?php foreach ($specs_array as $index => $spec) : ?>
                        <tr>
                            <td><input type="text" name="_product_specifications[<?php echo $index; ?>][label]" value="<?php echo esc_attr($spec['label']); ?>" class="large-text" placeholder="例如: 尺寸"></td>
                            <td><input type="text" name="_product_specifications[<?php echo $index; ?>][value]" value="<?php echo esc_attr($spec['value']); ?>" class="large-text" placeholder="例如: 直径 60cm × 高 45cm"></td>
                            <td><button type="button" class="button button-secondary light-fixture-remove-spec">移除</button></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
        <button type="button" class="button button-primary" id="light-fixture-add-spec">添加规格行</button>
    </div>
    <p class="description">点击"添加规格行"按钮添加新的规格。填写名称和值后，点击"移除"可以删除。</p>
    <?php
}

/**
 * 渲染产品定制选项元框内容
 */
function light_fixture_product_options_meta_box_callback($post) {
    wp_nonce_field('light_fixture_product_options_meta_box', 'light_fixture_product_options_nonce');

    $product_customization = get_post_meta($post->ID, '_product_customization', true);
    $options_array = !empty($product_customization) && is_array($product_customization) ? $product_customization : array();
    ?>
    <div id="light-fixture-options-container">
        <ul class="light-fixture-option-groups">
            <?php if (!empty($options_array)) : ?>
                <?php foreach ($options_array as $group_index => $option_group) : ?>
                    <li class="option-group-item">
                        <div class="option-group-header">
                            <input type="text" name="_product_customization[<?php echo $group_index; ?>][label]" value="<?php echo esc_attr($option_group['label']); ?>" class="large-text option-group-label" placeholder="例如: 颜色温度">
                            <button type="button" class="button button-secondary light-fixture-remove-option-group">移除选项组</button>
                        </div>
                        <ul class="light-fixture-option-values">
                            <?php if (!empty($option_group['values']) && is_array($option_group['values'])) : ?>
                                <?php foreach ($option_group['values'] as $value_index => $option_value) : ?>
                                    <li class="option-value-item">
                                        <input type="text" name="_product_customization[<?php echo $group_index; ?>][values][<?php echo $value_index; ?>][label]" value="<?php echo esc_attr($option_value['label']); ?>" class="regular-text" placeholder="显示名称 (例如: 暖白)">
                                        <input type="text" name="_product_customization[<?php echo $group_index; ?>][values][<?php echo $value_index; ?>][value]" value="<?php echo esc_attr($option_value['value']); ?>" class="regular-text" placeholder="实际值 (例如: 2700K)">
                                        <label>
                                            <input type="checkbox" name="_product_customization[<?php echo $group_index; ?>][values][<?php echo $value_index; ?>][default]" value="1" <?php checked($option_value['default'], 1); ?>>
                                            默认
                                        </label>
                                        <button type="button" class="button button-secondary light-fixture-remove-option-value">移除</button>
                                    </li>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </ul>
                        <button type="button" class="button button-secondary light-fixture-add-option-value">添加选项值</button>
                    </li>
                <?php endforeach; ?>
            <?php endif; ?>
        </ul>
        <button type="button" class="button button-primary" id="light-fixture-add-option-group">添加选项组</button>
    </div>
    <p class="description">点击"添加选项组"创建新的定制选项组。在每个组内，可以添加多个选项值。</p>
    <?php
}

/**
 * 保存所有产品元数据
 */
function light_fixture_save_all_product_meta($post_id, $post) {
    // 统一的安全检查
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE || !current_user_can('edit_post', $post_id)) {
        return;
    }

    // 检查基本信息nonce
    $info_nonce_valid = isset($_POST['light_fixture_product_info_nonce']) && 
                        wp_verify_nonce($_POST['light_fixture_product_info_nonce'], 'light_fixture_product_info_meta_box');
    
    // 检查固定描述nonce
    $desc_nonce_valid = isset($_POST['product_fixed_desc_nonce']) && 
                        wp_verify_nonce($_POST['product_fixed_desc_nonce'], 'product_fixed_desc_nonce');

    // 保存产品图集数据
    if ($info_nonce_valid && isset($_POST['_product_gallery_ids'])) {
        update_post_meta($post_id, '_product_gallery_ids', sanitize_text_field($_POST['_product_gallery_ids']));
    }

    // 保存产品基本信息
    if ($info_nonce_valid) {
        if (isset($_POST['_product_sku'])) {
            update_post_meta($post_id, '_product_sku', sanitize_text_field($_POST['_product_sku']));
        }
        if (isset($_POST['_product_status'])) {
            update_post_meta($post_id, '_product_status', sanitize_text_field($_POST['_product_status']));
        }
    }

    // 保存产品固定描述
    if ($desc_nonce_valid && isset($_POST['_product_fixed_desc'])) {
        update_post_meta($post_id, '_product_fixed_desc', wp_kses_post($_POST['_product_fixed_desc']));
    }

    // 保存产品特点
    if (isset($_POST['_product_features']) && is_array($_POST['_product_features'])) {
        $features_to_save = array_map('sanitize_text_field', $_POST['_product_features']);
        $features_to_save = array_filter($features_to_save);
        update_post_meta($post_id, '_product_features', $features_to_save);
    } else {
        delete_post_meta($post_id, '_product_features');
    }

    // 保存技术规格
    if (isset($_POST['_product_specifications']) && is_array($_POST['_product_specifications'])) {
        $specs_to_save = array();
        foreach ($_POST['_product_specifications'] as $spec_row) {
            if (isset($spec_row['label']) && isset($spec_row['value'])) {
                $label = sanitize_text_field($spec_row['label']);
                $value = sanitize_text_field($spec_row['value']);
                if (!empty($label) || !empty($value)) {
                    $specs_to_save[] = array(
                        'label' => $label,
                        'value' => $value,
                    );
                }
            }
        }
        update_post_meta($post_id, '_product_specifications', $specs_to_save);
    } else {
        delete_post_meta($post_id, '_product_specifications');
    }

    // 保存定制选项
    if (isset($_POST['_product_customization']) && is_array($_POST['_product_customization'])) {
        $options_to_save = array();
        foreach ($_POST['_product_customization'] as $group_data) {
            $group_label = isset($group_data['label']) ? sanitize_text_field($group_data['label']) : '';
            $group_values = array();

            if (isset($group_data['values']) && is_array($group_data['values'])) {
                foreach ($group_data['values'] as $value_data) {
                    $value_label = isset($value_data['label']) ? sanitize_text_field($value_data['label']) : '';
                    $actual_value = isset($value_data['value']) ? sanitize_text_field($value_data['value']) : '';
                    $is_default = isset($value_data['default']) ? 1 : 0;

                    if (!empty($value_label) || !empty($actual_value)) {
                        $group_values[] = array(
                            'label'   => $value_label,
                            'value'   => $actual_value,
                            'default' => $is_default,
                        );
                    }
                }
            }
            if (!empty($group_label) || !empty($group_values)) {
                $options_to_save[] = array(
                    'label'  => $group_label,
                    'values' => $group_values,
                );
            }
        }
        update_post_meta($post_id, '_product_customization', $options_to_save);
    } else {
        delete_post_meta($post_id, '_product_customization');
    }
}
add_action('save_post_product', 'light_fixture_save_all_product_meta', 10, 2); 