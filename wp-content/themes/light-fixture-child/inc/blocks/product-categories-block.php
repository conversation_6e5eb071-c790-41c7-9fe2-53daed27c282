<?php
/**
 * 产品类目导航区块
 *
 * @package Light_Fixture_Child
 */

/**
 * 渲染产品类目导航区块
 *
 * @param array $attributes 区块属性
 * @return string 区块HTML
 */
function light_fixture_render_product_categories_block($attributes) {
    // 提取属性
    $title = isset($attributes['title']) ? $attributes['title'] : '发现我们的产品系列';
    $subtitle = isset($attributes['subtitle']) ? $attributes['subtitle'] : '精选系列';
    $description = isset($attributes['description']) ? $attributes['description'] : '每一款灯具都是艺术品，为您的空间增添无与伦比的美感和温暖。';
    $columns = isset($attributes['columns']) ? intval($attributes['columns']) : 3;
    $show_view_all_button = isset($attributes['showViewAllButton']) ? $attributes['showViewAllButton'] : true;
    $view_all_button_text = isset($attributes['viewAllButtonText']) ? $attributes['viewAllButtonText'] : '浏览所有系列';
    $view_all_button_link = isset($attributes['viewAllButtonLink']) ? $attributes['viewAllButtonLink'] : '/index.php/productslist/';
    $use_product_categories = isset($attributes['useProductCategories']) ? $attributes['useProductCategories'] : true;
    $selected_categories = isset($attributes['selectedCategories']) ? $attributes['selectedCategories'] : array();
    $custom_categories = isset($attributes['categories']) ? $attributes['categories'] : array();
    $class_name = isset($attributes['className']) ? $attributes['className'] : '';
    
    // 根据列数设置产品网格的类名
    $grid_class = 'products-grid';
    if ($columns !== 3) {
        $grid_class .= ' products-grid--' . $columns;
    }
    
    // 开始输出缓冲
    ob_start();
    ?>
    <section class="section <?php echo esc_attr($class_name); ?>">
        <div class="container">
            <?php if (!empty($title) || !empty($subtitle) || !empty($description)) : ?>
            <header class="section-header text-center fade-in">
                <?php if (!empty($subtitle)) : ?>
                    <span class="section-subtitle"><?php echo esc_html($subtitle); ?></span>
                <?php endif; ?>
                
                <?php if (!empty($title)) : ?>
                    <h2 class="section-title"><?php echo esc_html($title); ?></h2>
                <?php endif; ?>
                
                <?php if (!empty($description)) : ?>
                    <p class="section-description"><?php echo esc_html($description); ?></p>
                <?php endif; ?>
            </header>
            <?php endif; ?>
            
            <div class="<?php echo esc_attr($grid_class); ?>">
                <?php
                // 获取产品分类
                $product_categories = array();
                
                if ($use_product_categories) {
                    // 使用产品分类
                    $args = array(
                        'taxonomy' => 'product_category',
                        'hide_empty' => false,
                    );
                    
                    if (!empty($selected_categories)) {
                        $args['include'] = $selected_categories;
                    }
                    
                    $terms = get_terms($args);
                    
                    if (!is_wp_error($terms) && !empty($terms)) {
                        $product_categories = $terms;
                    }
                } else {
                    // 使用自定义分类
                    if (!empty($custom_categories)) {
                        foreach ($custom_categories as $index => $cat) {
                            $product_categories[] = (object) array(
                                'term_id' => $index,
                                'name' => $cat['title'],
                                'description' => $cat['description'],
                                'custom_image' => $cat['image'],
                                'custom_link' => $cat['link'],
                                'custom_button' => isset($cat['buttonText']) ? $cat['buttonText'] : '查看系列',
                                'is_custom' => true,
                            );
                        }
                    }
                }
                
                // 如果没有分类，显示测试分类
                if (empty($product_categories)) {
                    echo '<!-- 没有找到产品分类，显示测试分类 -->';
                    
                    // 测试分类数据
                    $test_categories = array(
                        array(
                            'name' => '吊灯系列',
                            'description' => '优雅的吊灯系列，适合各种室内空间。',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                        array(
                            'name' => '壁灯系列',
                            'description' => '现代风格的壁灯系列，为您的墙面增添亮点。',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                        array(
                            'name' => '台灯系列',
                            'description' => '精致的台灯系列，为您的桌面带来温馨光源。',
                            'image' => get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg',
                        ),
                    );
                    
                    // 尝试获取随机产品图片
                    $products = get_posts(array(
                        'post_type' => 'product',
                        'posts_per_page' => 3,
                        'orderby' => 'rand',
                    ));
                    
                    // 如果有产品，使用产品图片替换测试分类图片
                    if (!empty($products)) {
                        foreach ($products as $index => $product) {
                            if (isset($test_categories[$index]) && has_post_thumbnail($product->ID)) {
                                // 获取原始图片URL而不是缩略图
                                $original_image_url = wp_get_attachment_url(get_post_thumbnail_id($product->ID));
                                if ($original_image_url) {
                                    $test_categories[$index]['image'] = $original_image_url;
                                }
                            }
                        }
                    }
                    
                    $delay = 1;
                    foreach ($test_categories as $category) {
                        ?>
                        <div class="product-card slide-up delay-<?php echo esc_attr($delay); ?>">
                            <div class="product-card__image-container">
                                <img src="<?php echo esc_url($category['image']); ?>" alt="<?php echo esc_attr($category['name']); ?>" class="product-card__image">
                                <div class="product-card__overlay">
                                    <a href="#" class="product-card__view-button"><?php echo esc_html__('查看系列', 'light-fixture-child'); ?></a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h3 class="product-card__title"><?php echo esc_html($category['name']); ?></h3>
                                <p class="product-card__description"><?php echo esc_html($category['description']); ?></p>
                            </div>
                        </div>
                        <?php
                        $delay++;
                        if ($delay > 3) {
                            $delay = 1;
                        }
                    }
                } else {
                    // 显示实际分类
                    $delay = 1;
                    foreach ($product_categories as $category) {
                        $is_custom = isset($category->is_custom) && $category->is_custom;
                        
                        if ($is_custom) {
                            // 自定义分类
                            $category_image = !empty($category->custom_image) ? $category->custom_image : get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                            $category_link = !empty($category->custom_link) ? $category->custom_link : '#';
                            $button_text = !empty($category->custom_button) ? $category->custom_button : '查看系列';
                        } else {
                            // 产品分类
                            $category_link = get_term_link($category);
                            $category_image = '';
                            
                            // 获取分类图片
                            $category_image = light_fixture_get_category_image($category->term_id);
                            
                            $button_text = __('查看系列', 'light-fixture-child');
                        }
                        ?>
                        <div class="product-card slide-up delay-<?php echo esc_attr($delay); ?>">
                            <div class="product-card__image-container">
                                <img src="<?php echo esc_url($category_image); ?>" alt="<?php echo esc_attr($category->name); ?>" class="product-card__image" onerror="this.src='<?php echo esc_url(get_stylesheet_directory_uri()); ?>/assets/images/placeholder.jpg'; this.onerror=null;">
                                <div class="product-card__overlay">
                                    <a href="<?php echo esc_url($category_link); ?>" class="product-card__view-button"><?php echo esc_html($button_text); ?></a>
                                </div>
                            </div>
                            <div class="product-card__content">
                                <h3 class="product-card__title"><?php echo esc_html($category->name); ?></h3>
                                <?php if (!empty($category->description)) : ?>
                                    <p class="product-card__description"><?php echo esc_html($category->description); ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php
                        $delay++;
                        if ($delay > 3) {
                            $delay = 1;
                        }
                    }
                }
                ?>
            </div>
            
            <?php if ($show_view_all_button && !empty($view_all_button_text) && !empty($view_all_button_link)) : ?>
            <div class="text-center mt-5">
                <a href="<?php echo esc_url(home_url($view_all_button_link)); ?>" class="btn btn-secondary"><?php echo esc_html($view_all_button_text); ?></a>
            </div>
            <?php endif; ?>
        </div>
    </section>
    <?php
    
    // 返回输出缓冲的内容
    return ob_get_clean();
}

/**
 * 获取产品图片
 * 
 * @param int $category_id 分类ID
 * @return string 图片URL
 */
function light_fixture_get_category_image($category_id) {
    $category_image = '';
    
    // 获取分类图片
    $term_meta = get_term_meta($category_id, 'category_image', true);
    if (!empty($term_meta)) {
        // 获取原始图片URL而不是缩略图
        $original_image_url = wp_get_attachment_url($term_meta);
        if ($original_image_url) {
            $category_image = $original_image_url;
        }
    }
    
    // 如果没有分类图片，尝试获取该分类下随机一个产品的特色图片
    if (empty($category_image)) {
        $products = get_posts(array(
            'post_type' => 'product',
            'posts_per_page' => -1,
            'orderby' => 'rand',
            'tax_query' => array(
                array(
                    'taxonomy' => 'product_category',
                    'field' => 'term_id',
                    'terms' => $category_id,
                ),
            ),
        ));
        
        foreach ($products as $product) {
            if (has_post_thumbnail($product->ID)) {
                // 获取原始图片URL而不是缩略图
                $original_image_url = wp_get_attachment_url(get_post_thumbnail_id($product->ID));
                if ($original_image_url) {
                    $category_image = $original_image_url;
                    break;
                }
            }
        }
    }
    
    // 如果仍然没有图片，使用默认图片
    if (empty($category_image)) {
        $category_image = get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
    }
    
    return $category_image;
}
