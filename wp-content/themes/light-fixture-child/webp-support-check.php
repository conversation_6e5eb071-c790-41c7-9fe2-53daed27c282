<?php
/**
 * WebP Support Detection Script
 * 检查服务器WebP支持情况
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 检查服务器WebP支持情况
 */
function light_fixture_check_webp_support() {
    $support_info = array(
        'gd_extension' => false,
        'gd_webp_support' => false,
        'imagick_extension' => false,
        'imagick_webp_support' => false,
        'php_version' => PHP_VERSION,
        'wordpress_version' => get_bloginfo('version'),
        'recommended_engine' => '',
        'support_status' => 'none'
    );
    
    // 检查GD扩展
    if (extension_loaded('gd')) {
        $support_info['gd_extension'] = true;
        $gd_info = gd_info();
        
        // 检查GD WebP支持
        if (isset($gd_info['WebP Support']) && $gd_info['WebP Support']) {
            $support_info['gd_webp_support'] = true;
        }
        
        // 检查函数是否存在
        if (function_exists('imagewebp')) {
            $support_info['gd_webp_support'] = true;
        }
    }
    
    // 检查Imagick扩展
    if (extension_loaded('imagick')) {
        $support_info['imagick_extension'] = true;
        
        // 检查Imagick WebP支持
        if (class_exists('Imagick')) {
            $imagick = new Imagick();
            $formats = $imagick->queryFormats('WEBP');
            if (!empty($formats)) {
                $support_info['imagick_webp_support'] = true;
            }
        }
    }
    
    // 确定推荐引擎和支持状态
    if ($support_info['imagick_webp_support']) {
        $support_info['recommended_engine'] = 'imagick';
        $support_info['support_status'] = 'full';
    } elseif ($support_info['gd_webp_support']) {
        $support_info['recommended_engine'] = 'gd';
        $support_info['support_status'] = 'basic';
    } else {
        $support_info['support_status'] = 'none';
    }
    
    return $support_info;
}

/**
 * 显示WebP支持检查结果
 */
function light_fixture_display_webp_support_info() {
    $support = light_fixture_check_webp_support();
    
    echo "<h3>WebP支持检查结果</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>检查项目</th><th>状态</th><th>说明</th></tr>\n";
    
    // PHP版本
    echo "<tr><td>PHP版本</td><td>{$support['php_version']}</td><td>" . 
         (version_compare($support['php_version'], '7.4', '>=') ? '✅ 支持' : '❌ 建议升级到7.4+') . "</td></tr>\n";
    
    // WordPress版本
    echo "<tr><td>WordPress版本</td><td>{$support['wordpress_version']}</td><td>" . 
         (version_compare($support['wordpress_version'], '5.8', '>=') ? '✅ 支持' : '❌ 建议升级到5.8+') . "</td></tr>\n";
    
    // GD扩展
    echo "<tr><td>GD扩展</td><td>" . ($support['gd_extension'] ? '已安装' : '未安装') . "</td><td>" . 
         ($support['gd_extension'] ? '✅ 可用' : '❌ 需要安装') . "</td></tr>\n";
    
    // GD WebP支持
    echo "<tr><td>GD WebP支持</td><td>" . ($support['gd_webp_support'] ? '支持' : '不支持') . "</td><td>" . 
         ($support['gd_webp_support'] ? '✅ 可用' : '❌ 需要重新编译GD') . "</td></tr>\n";
    
    // Imagick扩展
    echo "<tr><td>Imagick扩展</td><td>" . ($support['imagick_extension'] ? '已安装' : '未安装') . "</td><td>" . 
         ($support['imagick_extension'] ? '✅ 可用' : '⚠️ 可选') . "</td></tr>\n";
    
    // Imagick WebP支持
    echo "<tr><td>Imagick WebP支持</td><td>" . ($support['imagick_webp_support'] ? '支持' : '不支持') . "</td><td>" . 
         ($support['imagick_webp_support'] ? '✅ 推荐' : '⚠️ 可选') . "</td></tr>\n";
    
    // 推荐引擎
    echo "<tr><td>推荐引擎</td><td>{$support['recommended_engine']}</td><td>";
    switch ($support['support_status']) {
        case 'full':
            echo '✅ 完全支持，推荐使用Imagick';
            break;
        case 'basic':
            echo '✅ 基础支持，使用GD库';
            break;
        case 'none':
            echo '❌ 不支持WebP，需要配置服务器';
            break;
    }
    echo "</td></tr>\n";
    
    echo "</table>\n";
    
    // 显示建议
    echo "<h4>配置建议：</h4>\n";
    echo "<ul>\n";
    
    if ($support['support_status'] === 'none') {
        echo "<li>❌ 服务器不支持WebP，请联系主机商启用GD WebP支持或安装Imagick扩展</li>\n";
    } elseif ($support['support_status'] === 'basic') {
        echo "<li>✅ 基础WebP支持已启用，建议安装Imagick扩展以获得更好的性能</li>\n";
    } else {
        echo "<li>✅ 完整WebP支持已启用，可以开始实施WebP转换功能</li>\n";
    }
    
    echo "<li>建议定期检查WordPress和PHP版本更新</li>\n";
    echo "<li>建议在测试环境中先验证WebP转换功能</li>\n";
    echo "</ul>\n";
    
    return $support;
}

// 如果直接访问此文件，显示检查结果
if (basename($_SERVER['PHP_SELF']) === 'webp-support-check.php') {
    if (defined('ABSPATH')) {
        light_fixture_display_webp_support_info();
    }
}
