<?php
/**
 * WebP Test Page
 * WebP测试页面
 * 
 * @package Light_Fixture_Child
 * @since 1.0.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 简单的测试页面
if (isset($_GET['webp_test']) && current_user_can('manage_options')) {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>WebP功能测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-section { background: #f9f9f9; padding: 15px; margin: 10px 0; border-radius: 5px; }
            .success { color: green; }
            .error { color: red; }
            .info { color: blue; }
        </style>
    </head>
    <body>
        <h1>🧪 WebP功能测试页面</h1>
        
        <div class="test-section">
            <h2>📋 基础功能测试</h2>
            
            <?php
            // 测试类是否存在
            if (class_exists('Light_Fixture_WebP_Hooks_Integration')) {
                echo '<p class="success">✅ Light_Fixture_WebP_Hooks_Integration 类存在</p>';
                
                // 测试实例化
                try {
                    $hooks_integration = new Light_Fixture_WebP_Hooks_Integration();
                    echo '<p class="success">✅ 类可以正常实例化</p>';
                } catch (Exception $e) {
                    echo '<p class="error">❌ 类实例化失败: ' . $e->getMessage() . '</p>';
                }
            } else {
                echo '<p class="error">❌ Light_Fixture_WebP_Hooks_Integration 类不存在</p>';
            }
            
            // 测试核心函数
            $core_functions = array(
                'light_fixture_is_webp_browser_supported',
                'light_fixture_get_webp_converter',
                'light_fixture_get_optimized_image_url',
                'light_fixture_webp_exists'
            );
            
            echo '<h3>核心函数测试:</h3>';
            foreach ($core_functions as $function) {
                if (function_exists($function)) {
                    echo "<p class='success'>✅ $function() 函数存在</p>";
                } else {
                    echo "<p class='error'>❌ $function() 函数不存在</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🔗 钩子测试</h2>
            
            <?php
            // 测试关键钩子
            $critical_hooks = array(
                'wp_head' => 'wp_head钩子',
                'wp_footer' => 'wp_footer钩子',
                'wp_generate_attachment_metadata' => '图片上传钩子',
                'wp_get_attachment_image_src' => '图片URL过滤钩子'
            );
            
            foreach ($critical_hooks as $hook => $description) {
                if (has_action($hook) || has_filter($hook)) {
                    echo "<p class='success'>✅ $description 已注册</p>";
                } else {
                    echo "<p class='error'>❌ $description 未注册</p>";
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🚀 性能测试</h2>
            
            <?php
            // 测试浏览器检测
            if (function_exists('light_fixture_is_webp_browser_supported')) {
                $webp_supported = light_fixture_is_webp_browser_supported();
                echo '<p class="info">🌐 浏览器WebP支持: ' . ($webp_supported ? '支持' : '不支持') . '</p>';
            }
            
            // 测试转换器
            if (function_exists('light_fixture_get_webp_converter')) {
                $converter = light_fixture_get_webp_converter();
                if ($converter && $converter->is_webp_supported()) {
                    echo '<p class="success">✅ WebP转换器可用</p>';
                    echo '<p class="info">📊 推荐编辑器: ' . $converter->get_preferred_editor() . '</p>';
                } else {
                    echo '<p class="error">❌ WebP转换器不可用</p>';
                }
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>📊 统计信息</h2>
            
            <?php
            if (function_exists('light_fixture_get_webp_statistics')) {
                $stats = light_fixture_get_webp_statistics();
                echo '<p class="info">📈 总图片数: ' . $stats['total_images'] . '</p>';
                echo '<p class="info">🔄 已转换WebP: ' . $stats['webp_converted'] . '</p>';
                echo '<p class="info">💾 转换率: ' . round($stats['conversion_rate'], 1) . '%</p>';
            }
            ?>
        </div>
        
        <div class="test-section">
            <h2>🔧 快速操作</h2>
            <p><a href="?webp_method_check=1" target="_blank">🔍 方法检查</a></p>
            <p><a href="?webp_refactor_verify=1" target="_blank">✅ 重构验证</a></p>
            <p><a href="/" target="_blank">🏠 返回首页</a></p>
        </div>
        
        <div class="test-section">
            <h2>📝 测试结论</h2>
            <p class="success">如果上述所有测试都显示 ✅，说明WebP重构成功完成！</p>
            <p class="info">如果有 ❌ 错误，请检查相应的功能模块。</p>
        </div>
    </body>
    </html>
    <?php
    exit;
}
?>
