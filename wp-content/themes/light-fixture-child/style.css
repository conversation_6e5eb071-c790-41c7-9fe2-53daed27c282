/*
Theme Name: Light Fixture Child
Theme URI: https://wp.light-fixture.cn/
Description: Light Fixture Website Child Theme for Twenty Twenty-Five.
Author: Your Name
Author URI: https://wp.light-fixture.cn/
Template: twentytwentyfive
Version: 1.0.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: light-fixture-child
*/

/* 全局变量 - MOD Lighting 风格 */
:root {
    /* 主色调 - 金色、黑白配色方案 */
    --color-background: #ffffff;     /* 纯白色背景 */
    --color-surface: #ffffff;        /* 纯白色表面 */
    --color-text-dark: #000000;      /* 纯黑色文本 */
    --color-text-secondary: #333333; /* 深灰色辅助文本 */

    /* 边框和分隔线 */
    --color-border: #e0e0e0;         /* 浅灰色边框 */
    --color-overlay: rgba(0, 0, 0, 0.1); /* 黑色叠加层，用于图片上文字背景 */

    /* 强调色 - 金色系统 */
    --color-accent: #d6ad60;         /* 主金色 rgb(214, 173, 96) */
    --color-accent-light: #e4c078;   /* 亮金色 */
    --color-accent-hover: #c19a4a;   /* 深金色 */

    /* 状态色 */
    --color-success: #4caf50;        /* 标准绿色 */
    --color-warning: #ff9800;        /* 标准橙色 */
    --color-error: #f44336;          /* 标准红色 */
    
    /* 间距 */
    --spacing-xs: 1.25rem;   /* 20px */
    --spacing-sm: 2.5rem;    /* 40px */
    --spacing-md: 5rem;      /* 80px */
    --spacing-lg: 7.5rem;    /* 120px */
    --spacing-xl: 10rem;     /* 160px */
    --spacing-xxl: 15rem;    /* 240px */
    
    /* 容器宽度 */
    --container-width: 1600px;
    --container-padding: 2.5rem;
    
    /* 过渡动画 */
    --transition-slow: 0.5s ease;
    --transition-medium: 0.3s ease;
    --transition-fast: 0.15s ease;
    
    /* 阴影 */
    --shadow-soft: 0 10px 30px rgba(0, 0, 0, 0.05);
    --shadow-medium: 0 15px 35px rgba(0, 0, 0, 0.1);
}

/* =========================================
   全局样式
   ========================================= */

html {
    font-size: 16px;
    scroll-behavior: smooth;
    scroll-padding-top: 100px; /* 为固定导航栏留出空间 */
}

body {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-size: 1.0625rem; /* 17px - 欧美网站常用稍大的基础字体 */
    line-height: 1.7;
    color: var(--color-text-dark);
    background-color: var(--color-background);
    margin: 0;
    padding: 0;
    padding-top: 80px; /* 为固定头部留出空间 */
    overflow-x: hidden;
    transition: overflow 0s linear 0.3s;
}

/* 确保首页不受影响 */
body.home {
    padding-top: 0;
}

/* 确保前端页面不受影响 */
body.page-template-front-page {
    padding-top: 0;
}

body.left-panel-open {
    overflow: hidden;
}

/* 容器样式 */
.container {
    width: 100%;
    max-width: var(--container-width);
    margin: 0 auto;
    padding: 0 var(--container-padding);
    box-sizing: border-box;
}

/* 排版系统 */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 400; /* Playfair Display 的优雅权重 */
    letter-spacing: 0.01em; /* 适合衬线字体的字间距 */
    line-height: 1.2;
    color: var(--color-text-dark);
    margin-top: 0;
    margin-bottom: 1.5rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 400;
    letter-spacing: 0.01em;
}

h2 {
    font-size: clamp(2rem, 4vw, 2.75rem);
    font-weight: 400;
    letter-spacing: 0.01em;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    font-weight: 400;
    letter-spacing: 0.01em;
}

h4 {
    font-size: clamp(1.25rem, 2vw, 1.5rem);
    font-weight: 400;
}

h5 {
    font-size: 1.125rem;
    font-weight: 500;
}

h6 {
    font-size: 1rem;
    font-weight: 500;
}

p {
    font-size: 1rem;
    line-height: 1.8;
    margin-top: 0;
    margin-bottom: 1.5rem;
}

a {
    color: var(--color-text-dark);
    text-decoration: none;
    transition: color var(--transition-medium);
}

a:hover {
    color: var(--color-accent);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* =========================================
   网格系统
   ========================================= */

.grid {
    display: grid;
    grid-template-columns: repeat(12, 1fr);
    gap: 2rem;
}

@media (max-width: 991px) {
    .grid {
        gap: 1.5rem;
    }
}

@media (max-width: 767px) {
    .grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
    }
}

/* =========================================
   导航样式
   ========================================= */

/* 确保头部导航栏在所有页面都有统一样式 */
.site-header,
header.site-header,
.wp-site-blocks .site-header,
body .site-header {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    background-color: rgba(255, 255, 255, 0.95) !important; /* 半透明白色背景 */
    backdrop-filter: blur(10px) !important; /* 毛玻璃效果 */
    -webkit-backdrop-filter: blur(10px) !important; /* Safari 兼容性 */
    z-index: 1000 !important;
    padding: 1.5rem 0 !important;
    transition: all var(--transition-medium) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important; /* 淡黑色底边框 */
}

.site-header.scrolled,
header.site-header.scrolled,
.wp-site-blocks .site-header.scrolled,
body .site-header.scrolled {
    background-color: #ffffff !important; /* 滚动时变为纯白色 */
    padding: 1rem 0 !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1) !important; /* 更明显的阴影 */
    border-bottom: 1px solid rgba(0, 0, 0, 0.15) !important;
}

.site-header-inner {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.site-branding {
    flex: 0 0 auto;
}

/* 确保网站标志在所有页面都有统一样式 */
.site-logo a,
.site-branding a,
.wp-site-blocks .site-logo a,
body .site-logo a {
    display: block !important;
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    font-family: 'Playfair Display', 'Georgia', serif !important;
    color: var(--color-text-dark) !important;
    transition: color var(--transition-medium) !important;
    text-decoration: none !important;
}

.site-logo a:hover,
.site-branding a:hover,
.wp-site-blocks .site-logo a:hover,
body .site-logo a:hover {
    color: var(--color-accent) !important; /* 悬停时显示金色 */
}

.main-navigation {
    flex: 1 1 auto;
    margin-left: 2rem;
}

.main-navigation > ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    justify-content: flex-end;
}

.main-navigation li {
    position: relative;
    margin: 0 1rem;
}

/* 确保主导航菜单在所有页面都有统一样式 */
.main-navigation a,
.wp-site-blocks .main-navigation a,
body .main-navigation a,
nav.main-navigation a {
    display: block !important;
    padding: 0.5rem 0 !important;
    color: var(--color-text-dark) !important;
    font-weight: 500 !important;
    font-family: 'Playfair Display', 'Georgia', serif !important;
    font-size: 1rem !important;
    letter-spacing: 0.02em !important;
    transition: color var(--transition-medium) !important;
    text-decoration: none !important;
}

/* 确保导航菜单悬停效果在所有页面都一致 */
.main-navigation a::after,
.wp-site-blocks .main-navigation a::after,
body .main-navigation a::after,
nav.main-navigation a::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 0 !important;
    height: 2px !important;
    background-color: var(--color-accent) !important; /* 金色下划线 */
    transition: width var(--transition-medium) !important;
}

.main-navigation a:hover::after,
.wp-site-blocks .main-navigation a:hover::after,
body .main-navigation a:hover::after,
nav.main-navigation a:hover::after {
    width: 100% !important;
}

/* 确保子菜单在所有页面都有统一的方形设计 */
.main-navigation ul ul,
.wp-site-blocks .main-navigation ul ul,
body .main-navigation ul ul,
nav.main-navigation ul ul {
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 220px !important;
    background-color: #ffffff !important; /* 纯白色背景 */
    border: 2px solid #000000 !important; /* 黑色边框 */
    border-radius: 0 !important; /* 方形设计 */
    padding: 0.5rem 0 !important;
    list-style: none !important;
    margin: 0 !important;
    z-index: 10 !important;
    flex-direction: column !important;
    opacity: 0 !important;
    max-height: 0 !important;
    overflow: hidden !important;
    pointer-events: none !important;
    transition: opacity 0.5s ease-out, max-height 0.5s ease-out !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important; /* 更明显的阴影 */
}

.main-navigation li:hover > ul {
    opacity: 1;
    max-height: 500px;
    pointer-events: auto;
}

/* 针对第二级子菜单（如果有的话） */
.main-navigation ul ul ul {
    top: 0;
    left: 100%;
}

.main-navigation ul ul li:hover > ul {
    opacity: 1;
    max-height: 500px;
    pointer-events: auto;
}

/* 确保子菜单项样式在所有页面都一致 */
.main-navigation ul ul a,
.wp-site-blocks .main-navigation ul ul a,
body .main-navigation ul ul a,
nav.main-navigation ul ul a {
    padding: 0.75rem 1.5rem !important;
    font-size: 0.9rem !important;
    font-weight: 400 !important;
    font-family: 'Playfair Display', 'Georgia', serif !important;
    color: var(--color-text-dark) !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1) !important;
    transition: all var(--transition-medium) !important;
    text-decoration: none !important;
}

.main-navigation ul ul a:hover,
.wp-site-blocks .main-navigation ul ul a:hover,
body .main-navigation ul ul a:hover,
nav.main-navigation ul ul a:hover {
    background-color: #000000 !important; /* 黑色背景 */
    color: var(--color-accent) !important; /* 金色文字 */
}

.main-navigation ul ul a::after,
.wp-site-blocks .main-navigation ul ul a::after,
body .main-navigation ul ul a::after,
nav.main-navigation ul ul a::after {
    display: none !important; /* 隐藏子菜单的下划线效果 */
}

.menu-toggle {
    display: none;
    background: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    padding: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.menu-toggle:hover {
    background: #000000; /* 悬停时黑色背景 */
}

.hamburger,
.hamburger::before,
.hamburger::after {
    display: block;
    width: 24px;
    height: 2px;
    background-color: var(--color-text-dark);
    transition: all var(--transition-medium);
}

.menu-toggle:hover .hamburger,
.menu-toggle:hover .hamburger::before,
.menu-toggle:hover .hamburger::after {
    background-color: var(--color-accent); /* 悬停时金色线条 */
}

.hamburger {
    position: relative;
}

.hamburger::before {
    content: '';
    position: absolute;
    top: -8px;
}

.hamburger::after {
    content: '';
    position: absolute;
    bottom: -8px;
}

.menu-toggle.active .hamburger {
    background-color: transparent;
}

.menu-toggle.active .hamburger::before {
    transform: rotate(45deg);
    top: 0;
}

.menu-toggle.active .hamburger::after {
    transform: rotate(-45deg);
    bottom: 0;
}

@media (max-width: 991px) {
    .menu-toggle {
        display: block;
    }

    .main-navigation > ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: #ffffff; /* 纯白色背景 */
        border: 2px solid #000000; /* 黑色边框 */
        border-top: none; /* 移除顶部边框避免重复 */
        border-radius: 0; /* 方形设计 */
        padding: 1rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .main-navigation > ul.active {
        display: block;
    }

    .main-navigation li {
        margin: 0;
    }

    .main-navigation a {
        padding: 0.75rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        transition: all var(--transition-medium);
    }

    .main-navigation a:hover {
        background-color: #000000; /* 黑色背景 */
        color: var(--color-accent); /* 金色文字 */
    }

    /* 移动端子菜单样式 */
    .main-navigation ul ul {
        position: static;
        box-shadow: none;
        border: none;
        padding-left: 1.5rem;
        background-color: rgba(0, 0, 0, 0.05); /* 浅灰色背景区分子菜单 */
        opacity: 1;
        visibility: visible;
        display: block;
        max-height: none;
        overflow: visible;
    }

    .main-navigation ul ul li a {
        padding: 0.5rem 1.5rem;
        font-size: 0.9rem;
        color: var(--color-text-secondary);
    }

    .main-navigation ul ul li a:hover {
        background-color: #000000;
        color: var(--color-accent);
    }

    .left-panel-toggle {
        margin-right: 1rem;
    }
}

/* 左侧滑出菜单触发器按钮样式 */
.left-panel-toggle {
    display: block;
    margin-right: 1.5rem;
    background: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    padding: 0.5rem;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.left-panel-toggle:hover {
    background: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.left-panel-toggle .hamburger,
.left-panel-toggle .hamburger::before,
.left-panel-toggle .hamburger::after {
    background-color: var(--color-text-dark);
    transition: all var(--transition-medium);
}

.left-panel-toggle:hover .hamburger,
.left-panel-toggle:hover .hamburger::before,
.left-panel-toggle:hover .hamburger::after {
    background-color: var(--color-accent); /* 悬停时金色线条 */
}

/* 左侧滑出菜单样式 */
.left-panel-menu-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: 320px;
    background-color: #ffffff; /* 纯白色背景 */
    border-right: 3px solid #000000; /* 右侧黑色边框 */
    transition: transform var(--transition-medium);
    transform: translateX(-100%);
    z-index: 99999;
    overflow-y: auto;
    visibility: visible;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15); /* 更明显的阴影 */
}

.left-panel-menu-container.active {
    transform: translateX(0);
}

body.left-panel-open .left-panel-menu-container {
    transform: translateX(0);
}

.left-panel-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99990;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0s 0.2s; /* 更快的淡入效果 */
}

body.left-panel-open .left-panel-overlay {
    opacity: 1;
    visibility: visible;
    transition: opacity 0.2s ease, visibility 0s; /* 立即显示，快速淡入 */
}

.left-panel-content {
    position: relative;
    height: 100%;
    width: 100%;
}

.left-panel-menu {
    list-style: none;
    margin: 0;
    padding: 2.5rem 1.5rem;
    color: var(--color-text-dark);
}

.left-panel-menu li {
    margin-bottom: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.left-panel-menu li a {
    display: block;
    padding: 1rem 0;
    text-decoration: none;
    color: var(--color-text-dark);
    font-size: 1.1rem;
    font-weight: 500;
    font-family: 'Playfair Display', 'Georgia', serif;
    letter-spacing: 0.02em;
    transition: all var(--transition-medium);
    border-left: 3px solid transparent;
    padding-left: 1rem;
    margin-left: -1rem;
}

.left-panel-menu li a:hover {
    color: var(--color-accent); /* 金色文字 */
    background-color: rgba(0, 0, 0, 0.05); /* 浅灰色背景 */
    border-left-color: var(--color-accent); /* 金色左边框 */
}

.left-panel-menu ul {
    list-style: none;
    margin: 0;
    padding-left: 2rem;
    background-color: rgba(0, 0, 0, 0.03); /* 非常浅的灰色背景 */
    box-shadow: none;
    display: block;
    opacity: 1;
    max-height: none;
    pointer-events: auto;
    transition: none;
    border-left: 2px solid var(--color-accent); /* 金色左边框标识子菜单 */
}

.left-panel-menu ul li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05); /* 更浅的分隔线 */
}

.left-panel-menu ul li a {
    font-size: 0.95rem;
    font-weight: 400;
    color: var(--color-text-secondary);
    padding: 0.75rem 0;
    padding-left: 0.5rem;
    margin-left: -0.5rem;
}

.left-panel-menu ul li a:hover {
    color: var(--color-accent);
    background-color: rgba(214, 173, 96, 0.1); /* 浅金色背景 */
    border-left-color: var(--color-accent);
}

@media (max-width: 991px) {
    .left-panel-menu li {
        margin-bottom: 1rem;
    }
}

/* =========================================
   页脚样式
   ========================================= */

.site-footer {
    background-color: #000000; /* 纯黑色页脚 */
    color: #ffffff;
    padding: var(--spacing-lg) 0 var(--spacing-sm);
    margin-top: var(--spacing-xl);
}

.footer-widgets {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-bottom: var(--spacing-md);
}

.footer-widget h3 {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.75rem;
    color: #ffffff;
}

.footer-widget h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px; /* 增加高度使其更明显 */
    background-color: var(--color-accent); /* 金色装饰线 */
}

.footer-widget p,
.footer-widget ul li a,
.copyright {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.footer-widget ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-widget ul li {
    margin-bottom: 0.75rem;
}

.footer-widget ul li a {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    transition: color var(--transition-medium);
}

.footer-widget ul li a:hover {
    color: var(--color-accent);
}

.newsletter-form {
    display: flex;
    margin-top: 1.5rem;
}

.newsletter-form input[type="email"] {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--color-border);
    background-color: transparent;
    font-size: 0.875rem;
    color: var(--color-text-dark);
}

.newsletter-form input[type="email"]:focus {
    outline: none;
    border-color: var(--color-accent);
}

.newsletter-form button {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    padding: 0 1.25rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.newsletter-form button:hover {
    background-color: var(--color-accent); /* 金色背景 */
    color: #000000; /* 黑色文字 */
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--color-border);
}

.social-icons {
    display: flex;
    gap: 0.75rem;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border-radius: 0; /* 方形设计 */
    background-color: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
    border: 2px solid rgba(255, 255, 255, 0.3); /* 半透明白色边框 */
    color: #ffffff; /* 白色图标 */
    font-size: 1.2rem; /* 增大图标尺寸 */
    transition: all var(--transition-medium);
}

.social-icons a:hover {
    background-color: var(--color-accent); /* 金色背景 */
    border-color: var(--color-accent); /* 金色边框 */
    color: #000000; /* 黑色图标 */
    transform: translateY(-2px); /* 轻微上移效果 */
    box-shadow: 0 4px 15px rgba(214, 173, 96, 0.3); /* 金色阴影 */
}

.copyright {
    font-size: 0.75rem;
    color: var(--color-text-secondary);
    margin: 0;
}

@media (max-width: 991px) {
    .footer-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .footer-widgets {
        grid-template-columns: 1fr;
    }
    
    .footer-bottom {
    flex-direction: column;
    }
    
    .social-icons {
        margin-bottom: 1rem;
    }
}

/* =========================================
   按钮样式
   ========================================= */

.btn {
    display: inline-block;
    padding: 0.9375rem 2.5rem; /* 更大的按钮尺寸 */
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Playfair Display', 'Georgia', serif;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    cursor: pointer;
    transition: all var(--transition-medium);
    border-radius: 0; /* 方形设计，直角边框 */
    border: 2px solid transparent;
}

.btn-primary {
    background-color: #ffffff; /* 白色背景 */
    color: #000000; /* 黑色文字 */
    border-color: #000000; /* 黑色边框 */
}

.btn-primary:hover {
    background-color: #000000; /* 黑色背景填充 */
    color: var(--color-accent); /* 金色文字 */
    border-color: #000000;
}

.btn-secondary {
    background-color: #ffffff; /* 白色背景 */
    color: #000000; /* 黑色文字 */
    border-color: #000000; /* 黑色边框 */
}

.btn-secondary:hover {
    background-color: #000000; /* 黑色背景填充 */
    color: var(--color-accent); /* 金色文字 */
    border-color: #000000;
}

/* =========================================
   表单元素
   ========================================= */

input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="search"],
input[type="url"],
textarea,
select {
        width: 100%;
    padding: 0.9375rem 1.25rem; /* 更宽敞的内边距 */
    border: 2px solid #000000; /* 黑色边框 */
    background-color: #ffffff; /* 白色背景 */
    font-size: 0.875rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    color: var(--color-text-dark);
    transition: border-color var(--transition-medium);
    border-radius: 0; /* 方形设计 */
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="url"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--color-accent); /* 金色聚焦边框 */
    box-shadow: 0 0 0 1px var(--color-accent);
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

/* =========================================
   产品卡片样式
   ========================================= */

.products-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

@media (max-width: 991px) {
    .products-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
}

.product-card {
    position: relative;
    background-color: var(--color-surface);
    box-shadow: var(--shadow-soft);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 6px; /* 更明显的圆角 */
}

.product-card:hover {
    transform: translateY(-8px);
}

.product-card__image-container {
    position: relative;
    overflow: hidden;
    padding-bottom: 100%; /* 1:1 比例 */
}

.product-card__image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.product-card:hover .product-card__image {
    transform: scale(1.05);
}

.product-card__overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: background-color var(--transition-medium), opacity var(--transition-medium);
}

.product-card:hover .product-card__overlay {
    background-color: rgba(0, 0, 0, 0.2);
    opacity: 1;
}

.product-card__view-button {
    padding: 0.75rem 1.5rem;
    background-color: #ffffff; /* 白色背景 */
    color: #000000; /* 黑色文字 */
    font-size: 0.75rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    transform: translateY(20px);
    opacity: 0;
    transition: transform var(--transition-medium), opacity var(--transition-medium), background-color var(--transition-medium), color var(--transition-medium);
}

.product-card:hover .product-card__view-button {
    transform: translateY(0);
    opacity: 1;
}

.product-card__view-button:hover {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.product-card__content {
    padding: 1.5rem;
}

.product-card__title {
    font-size: 1.125rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.product-card__description {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-bottom: 0;
}

/* =========================================
   Hero 区域样式
   ========================================= */

.hero-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-top: 30px;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-content {
    max-width: 600px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.hero-subtitle {
    font-size: 1rem;
    font-weight: 400;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--color-accent);
    margin-bottom: 1rem;
}

.hero-title {
    margin-bottom: 1.5rem;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
}

@media (max-width: 767px) {
    .hero-section {
        height: 80vh;
    }
    
    .hero-content {
        padding: 1.5rem;
    }
    
    .hero-buttons {
        flex-direction: column;
    }
}

/* =========================================
   通用区块样式
   ========================================= */

.section {
    padding: var(--spacing-lg) 0;
}

.section-header {
    text-align: center;
    max-width: 800px;
    margin: 0 auto var(--spacing-md);
}

.section-subtitle {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.2em;
    text-transform: uppercase;
    color: var(--color-accent);
    margin-bottom: 1rem;
    display: block;
}

.section-title {
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1.125rem;
    color: var(--color-text-secondary);
}

/* =========================================
   产品详情页样式
   ========================================= */

.product-detail {
    display: grid;
    grid-template-columns: 7fr 5fr;
    gap: 4rem;
    margin-top: var(--spacing-md);
    position: relative;
}

.product-detail__gallery {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.product-detail__main-image {
    position: relative;
    width: 100%;
    padding-bottom: 100%;
    overflow: hidden;
    /* background-color: var(--color-background); */
    border-radius: 4px;
    /* box-shadow: var(--shadow-soft); */
}

.product-main-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    padding: 1rem;
}

.product-detail__thumbnails {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 0.5rem;
    /* background-color: var(--color-background); */
    border-radius: 4px;
    /* box-shadow: var(--shadow-soft); */
    position: relative;
    margin-top: 2.5rem;
}

.thumbnail-item {
    position: relative;
    padding-bottom: 100%;
    cursor: pointer;
    opacity: 0.6;
    transition: all var(--transition-medium);
    overflow: hidden;
    background-color: var(--color-surface);
    border-radius: 4px;
    border: 2px solid transparent;
}

.thumbnail-item:hover {
    opacity: 0.8;
    transform: translateY(-2px);
}

.thumbnail-item.active {
    opacity: 1;
    border-color: var(--color-accent);
}

.thumbnail-item img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

/* 添加缩略图提示 */
.product-detail__thumbnails::before {
    content: 'More Views';
    position: absolute;
    top: -2rem;
    left: 0;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-secondary);
    letter-spacing: 0.05em;
    background-color: var(--color-background);
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    box-shadow: var(--shadow-soft);
}



.product-detail__info {
    padding-top: 2rem;
}

.product-category {
    font-size: 0.875rem;
    font-weight: 500;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    color: var(--color-accent);
    margin-bottom: 1rem;
}

.product-title {
    margin-bottom: 1.5rem;
}

.product-description {
    margin-bottom: 2rem;
}

.product-features {
    margin-bottom: 2rem;
}

.product-features h3 {
    margin-bottom: 1rem;
}

.product-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.product-features li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.9375rem;
}

.product-features li:before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: var(--color-accent);
    margin-right: 1rem;
}

.specifications-table {
    margin-bottom: 2rem;
}

.spec-row {
    display: flex;
    border-bottom: 1px solid var(--color-border);
    padding: 1rem 0;
}

.spec-row:last-child {
    border-bottom: none;
}

.spec-label {
    flex: 0 0 40%;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--color-text-secondary);
}

.spec-value {
    flex: 0 0 60%;
    font-size: 0.875rem;
}

.product-actions {
    margin-top: 2.5rem;
}

.inquiry-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 1rem;
    background-color: #ffffff; /* 白色背景 */
    color: #000000; /* 黑色文字 */
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    font-family: 'Playfair Display', 'Georgia', serif;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all var(--transition-medium);
}

.inquiry-button:hover {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.inquiry-button i {
    margin-right: 0.5rem;
}

@media (max-width: 1200px) {
    .product-detail {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
    }
}

@media (max-width: 991px) {
    .product-detail {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .product-detail__gallery {
        margin-bottom: 2rem;
    }
}

@media (max-width: 767px) {
    .product-detail__thumbnails {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* =========================================
   关于我们页面样式
   ========================================= */

.about-hero {
    position: relative;
    height: 80vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.about-story {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4rem;
        align-items: center;
    margin: var(--spacing-lg) 0;
}

.about-story__image {
    position: relative;
}

.about-story__image img {
    width: 100%;
    height: auto;
}

.about-story__content {
    padding: 2rem 0;
}

.about-story__title {
    margin-bottom: 2rem;
}

.philosophy-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: var(--spacing-md) 0;
}

.philosophy-item {
    padding: 2rem;
    background-color: var(--color-surface);
    box-shadow: var(--shadow-soft);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.philosophy-item:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-medium);
}

.philosophy-icon {
    margin-bottom: 1.5rem;
    color: var(--color-accent);
    font-size: 2.5rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin: var(--spacing-md) 0;
}

.team-member {
    position: relative;
    overflow: hidden;
}

.team-member__image {
    position: relative;
    overflow: hidden;
}

.team-member__image img {
    width: 100%;
    height: auto;
    transition: transform var(--transition-slow);
}

.team-member:hover .team-member__image img {
    transform: scale(1.05);
}

.team-member__info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 1.5rem;
    background-color: var(--color-overlay);
    transform: translateY(100%);
    transition: transform var(--transition-medium);
}

.team-member:hover .team-member__info {
    transform: translateY(0);
}

.team-member__name {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
}

.team-member__title {
    font-size: 0.875rem;
    color: var(--color-accent);
    margin-bottom: 1rem;
}

@media (max-width: 991px) {
    .about-story {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .philosophy-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .philosophy-grid,
    .team-grid {
        grid-template-columns: 1fr;
    }
}

/* =========================================
   联系我们页面样式
   ========================================= */

.contact-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 4rem;
    margin: var(--spacing-lg) 0;
}

.contact-info {
    padding: 2rem 0;
}

.contact-item {
    display: flex;
    margin-bottom: 2rem;
}

.contact-item__icon {
    flex: 0 0 50px;
    font-size: 1.5rem;
    color: var(--color-accent);
}

.contact-item__content h4 {
    margin-bottom: 0.5rem;
}

.contact-item__content p {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-bottom: 0.5rem;
}

.social-links {
    margin-top: 3rem;
}

.social-links h4 {
    margin-bottom: 1rem;
}

.contact-form {
    background-color: var(--color-surface);
    padding: 3rem;
    box-shadow: var(--shadow-soft);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.form-submit {
    margin-top: 2rem;
}

.map-section {
    position: relative;
    height: 500px;
    margin-top: var(--spacing-lg);
}

.map-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@media (max-width: 991px) {
    .contact-section {
        grid-template-columns: 1fr;
        gap: 3rem;
    }
}

@media (max-width: 767px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .contact-form {
        padding: 2rem;
    }
}

/* =========================================
   动画效果
   ========================================= */

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(30px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.fade-in {
    animation: fadeIn 1s ease forwards;
}

.slide-up {
    animation: slideUp 1s ease forwards;
}

/* 交错动画延迟 */
.delay-1 { animation-delay: 0.1s; }
.delay-2 { animation-delay: 0.2s; }
.delay-3 { animation-delay: 0.3s; }
.delay-4 { animation-delay: 0.4s; }
.delay-5 { animation-delay: 0.5s; }

/* =========================================
   辅助类
   ========================================= */

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* =========================================
   Gutenberg区块样式
   ========================================= */

/* 标题区块文本对齐样式 */
.wp-block-heading.has-text-align-left,
h1.has-text-align-left,
h2.has-text-align-left,
h3.has-text-align-left,
h4.has-text-align-left,
h5.has-text-align-left,
h6.has-text-align-left {
    text-align: left;
}

.wp-block-heading.has-text-align-center,
h1.has-text-align-center,
h2.has-text-align-center,
h3.has-text-align-center,
h4.has-text-align-center,
h5.has-text-align-center,
h6.has-text-align-center {
    text-align: center;
}

.wp-block-heading.has-text-align-right,
h1.has-text-align-right,
h2.has-text-align-right,
h3.has-text-align-right,
h4.has-text-align-right,
h5.has-text-align-right,
h6.has-text-align-right {
    text-align: right;
}

/* 段落区块文本对齐样式 */
.wp-block-paragraph.has-text-align-left {
    text-align: left;
}

.wp-block-paragraph.has-text-align-center {
    text-align: center;
}

.wp-block-paragraph.has-text-align-right {
    text-align: right;
}

/* 通用区块对齐样式 */
.has-text-align-left {
    text-align: left;
}

.has-text-align-center {
    text-align: center;
}

.has-text-align-right {
    text-align: right;
}

/* 区块编辑器中的对齐预览 */
.block-editor-block-list__layout .wp-block-heading.has-text-align-left,
.block-editor-block-list__layout .wp-block-heading.has-text-align-center,
.block-editor-block-list__layout .wp-block-heading.has-text-align-right {
    text-align: inherit;
}

/* 确保在编辑器中也能正确显示对齐 */
.editor-styles-wrapper .wp-block-heading.has-text-align-left {
    text-align: left;
}

.editor-styles-wrapper .wp-block-heading.has-text-align-center {
    text-align: center;
}

.editor-styles-wrapper .wp-block-heading.has-text-align-right {
    text-align: right;
}

/* 响应式文本对齐 */
@media (max-width: 768px) {
    /* 在移动设备上，右对齐的标题改为左对齐以提高可读性 */
    .wp-block-heading.has-text-align-right {
        text-align: left;
    }

    /* 保持居中对齐在移动设备上的效果 */
    .wp-block-heading.has-text-align-center {
        text-align: center;
    }
}

/* 区块颜色支持 */
.wp-block-heading.has-primary-gold-color {
    color: var(--color-accent);
}

.wp-block-heading.has-primary-gold-background-color {
    background-color: var(--color-accent);
}

.wp-block-heading.has-light-gold-color {
    color: var(--color-accent-light);
}

.wp-block-heading.has-dark-gold-color {
    color: var(--color-accent-hover);
}

.wp-block-heading.has-pure-black-color {
    color: var(--color-text-dark);
}

.wp-block-heading.has-pure-white-color {
    color: var(--color-background);
}

.wp-block-heading.has-dark-gray-color {
    color: var(--color-text-secondary);
}

.mt-0 { margin-top: 0; }
.mb-0 { margin-bottom: 0; }
.mt-1 { margin-top: 0.5rem; }
.mb-1 { margin-bottom: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mb-2 { margin-bottom: 1rem; }
.mt-3 { margin-top: 2rem; }
.mb-3 { margin-bottom: 2rem; }
.mt-4 { margin-top: 3rem; }
.mb-4 { margin-bottom: 3rem; }
.mt-5 { margin-top: 4rem; }
.mb-5 { margin-bottom: 4rem; }

.pt-0 { padding-top: 0; }
.pb-0 { padding-bottom: 0; }
.pt-1 { padding-top: 0.5rem; }
.pb-1 { padding-bottom: 0.5rem; }
.pt-2 { padding-top: 1rem; }
.pb-2 { padding-bottom: 1rem; }
.pt-3 { padding-top: 2rem; }
.pb-3 { padding-bottom: 2rem; }
.pt-4 { padding-top: 3rem; }
.pb-4 { padding-bottom: 3rem; }
.pt-5 { padding-top: 4rem; }
.pb-5 { padding-bottom: 4rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.align-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.w-100 { width: 100%; }
.h-100 { height: 100%; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

.overflow-hidden { overflow: hidden; }

/* =========================================
   产品列表页样式
   ========================================= */

.filter-categories {
    margin-bottom: var(--spacing-md);
}

.filter-title h2 {
    margin-bottom: 1.5rem;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2.5rem;
}

.filter-option {
    padding: 0.75rem 1.25rem;
    background-color: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    color: #000000; /* 黑色文字 */
    font-size: 0.875rem;
    font-weight: 500;
    font-family: 'Playfair Display', 'Georgia', serif;
    letter-spacing: 0.05em;
    transition: all var(--transition-medium);
    cursor: pointer;
    border-radius: 0; /* 方形设计 */
    display: inline-block;
    text-align: center;
}

.filter-option:hover {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
    transform: translateY(-2px);
    box-shadow: var(--shadow-soft);
}

.filter-option.active {
    background-color: #000000; /* 黑色背景 */
    border-color: #000000;
    color: var(--color-accent); /* 金色文字 */
    box-shadow: var(--shadow-soft);
}

.category-header {
    margin-bottom: 2.5rem;
}

.category-header h3 {
    margin-bottom: 0.5rem;
}

.product-category {
    display: none;
}

.product-category.active {
    display: block;
}

/* 分页样式 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.pagination__prev,
.pagination__next,
.pagination__page,
.pagination__current,
.pagination__dots {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin: 0 0.25rem;
    border: 2px solid #000000; /* 黑色边框 */
    background-color: #ffffff; /* 白色背景 */
    color: #000000; /* 黑色文字 */
    font-size: 0.875rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    transition: all var(--transition-medium);
    border-radius: 0; /* 方形设计 */
}

.pagination__current {
    background-color: #000000; /* 黑色背景 */
    border-color: #000000;
    color: var(--color-accent); /* 金色文字 */
}

.pagination__page:hover {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.pagination__prev.disabled,
.pagination__next.disabled {
    opacity: 0.5;
    pointer-events: none;
}

/* CTA框样式 */
.cta-box {
    background-color: var(--color-surface);
    padding: 3rem;
    margin-top: var(--spacing-md);
    box-shadow: var(--shadow-soft);
    text-align: center;
}

.cta-box h2,
.cta-box h3 {
    margin-bottom: 1rem;
}

.cta-box p {
    max-width: 800px;
    margin: 0 auto 1.5rem;
}

/* =========================================
   产品详情页样式增强
   ========================================= */

/* 自定义选项组样式 */
.option-group {
    margin-bottom: 1.5rem;
}

.option-group label {
    display: block;
    margin-bottom: 0.75rem;
    font-size: 0.9375rem;
    font-weight: 500;
}

.option-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.option-button {
    padding: 0.75rem 1.25rem;
    background-color: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    color: #000000; /* 黑色文字 */
    font-size: 0.875rem;
    font-family: 'Playfair Display', 'Georgia', serif;
    cursor: pointer;
    transition: all var(--transition-medium);
    border-radius: 0; /* 方形设计 */
}

.option-button:hover {
    background-color: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.option-button.active {
    background-color: #000000; /* 黑色背景 */
    border-color: #000000;
    color: var(--color-accent); /* 金色文字 */
}

/* 展示画廊样式 */
.showcase-gallery {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2.5rem;
}

.showcase-image {
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-soft);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.showcase-image:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.showcase-image img {
    width: 100%;
    height: auto;
}

.showcase-caption {
    position: absolute;
    bottom: 0;
    left: 0;
        width: 100%;
    padding: 1rem;
    background-color: var(--color-overlay);
    font-size: 0.9375rem;
    font-weight: 500;
        text-align: center;
    transform: translateY(100%);
    transition: transform var(--transition-medium);
}

.showcase-image:hover .showcase-caption {
    transform: translateY(0);
}

/* 色温对比网格 */
.temp-comparison-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 2.5rem;
}

.temp-item {
    background-color: var(--color-surface);
    box-shadow: var(--shadow-soft);
    transition: transform var(--transition-medium), box-shadow var(--transition-medium);
}

.temp-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-medium);
}

.temp-image img {
    width: 100%;
    height: auto;
}

.temp-info {
    padding: 1.5rem;
}

.temp-info h3 {
    margin-bottom: 0.75rem;
    font-size: 1.125rem;
}

.temp-info p {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-bottom: 0;
}

/* 产品元数据 */
.product-meta {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.product-meta span {
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    margin-right: 1.5rem;
}

.product-status {
    color: var(--color-success) !important;
    font-weight: 500;
}

/* 响应式调整 */
@media (max-width: 991px) {
    .showcase-gallery,
    .temp-comparison-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .product-detail {
        gap: 3rem;
    }
}

@media (max-width: 767px) {
    .showcase-gallery,
    .temp-comparison-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-options {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .filter-option {
    width: 100%;
        text-align: center;
    }
    
    .cta-box {
        padding: 2rem;
    }
}

/* =========================================
   关于我们页面样式增强
   ========================================= */

.about-hero {
    position: relative;
    height: 80vh;
    min-height: 500px;
    display: flex;
    align-items: center;
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.about-hero-content {
    position: relative;
    z-index: 1;
    max-width: 600px;
    padding: 2.5rem;
    background-color: var(--color-overlay);
}

.about-story {
    margin: var(--spacing-lg) 0;
}

.about-story__image img {
    width: 100%;
    height: auto;
    box-shadow: var(--shadow-soft);
}

.about-section {
    padding: var(--spacing-md) 0;
}

.about-section:nth-child(even) {
    background-color: var(--color-background);
}

/* 团队成员样式增强 */
.team-grid {
    margin-top: 3rem;
}

.team-member {
    box-shadow: var(--shadow-soft);
}

.team-member__image {
    height: 0;
    padding-bottom: 100%;
    position: relative;
    overflow: hidden;
}

.team-member__image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member__info {
    background-color: var(--color-surface);
    padding: 1.5rem;
    transition: transform var(--transition-medium);
}

.team-member:hover .team-member__info {
    transform: translateY(0);
}

/* 理念项目样式增强 */
.philosophy-item {
    height: 100%;
    display: flex;
        flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 2.5rem;
}

.philosophy-icon {
    margin-bottom: 1.5rem;
}

/* =========================================
   联系我们页面样式增强
   ========================================= */

.contact-section {
    margin: var(--spacing-lg) 0;
}

.contact-info {
    padding-right: 2rem;
}

.contact-item {
    margin-bottom: 2.5rem;
}

.contact-item__icon {
    color: var(--color-accent);
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-form {
    background-color: var(--color-surface);
    padding: 3rem;
    box-shadow: var(--shadow-soft);
}

.form-row {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    font-size: 0.9375rem;
}

.form-submit {
    margin-top: 2.5rem;
}

.map-section {
    height: 450px;
    margin-top: var(--spacing-lg);
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 0; /* 方形设计 */
    background-color: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    color: #000000; /* 黑色文字 */
    transition: all var(--transition-medium);
}

.social-links a:hover {
    background-color: #000000; /* 黑色背景 */
    border-color: #000000;
    color: var(--color-accent); /* 金色文字 */
}

@media (max-width: 991px) {
    .contact-info {
        padding-right: 0;
        margin-bottom: 2.5rem;
    }
}

@media (max-width: 767px) {
    .contact-form {
        padding: 2rem;
    }
}

/* =========================================
   辅助类扩展
   ========================================= */

.bg-light {
    background-color: var(--color-background);
}

.bg-surface {
    background-color: var(--color-surface);
}

/* 照片查看器样式优化 */
.image-overlay-container img {
    max-height: 85vh;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
}

/* =========================================
   全局悬浮按钮组
   ========================================= */

/* 统一悬浮按钮组样式 */
.floating-buttons {
    position: fixed;
    right: 30px;
    bottom: 30px;
    display: flex;
    flex-direction: column;
    gap: 12px; /* 统一间距 */
    z-index: 999;
}

/* 确保所有悬浮按钮尺寸完全一致 - 使用最高特异性 */
.floating-buttons .floating-button,
.floating-buttons .floating-button.share-button,
.floating-buttons .floating-button.contact-button,
.floating-buttons .floating-button.back-to-top,
.floating-buttons button.floating-button,
.floating-buttons a.floating-button,
.floating-buttons button.share-button,
.floating-buttons a.contact-button,
.floating-buttons button.back-to-top {
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    min-height: 50px !important;
    max-width: 50px !important;
    max-height: 50px !important;
    border-radius: 0 !important; /* 方形设计 */
    border: 2px solid #000000 !important; /* 黑色边框 */
    background: #ffffff !important; /* 白色背景 */
    color: #000000 !important; /* 黑色文字 */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    cursor: pointer !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
    position: relative !important;
    padding: 0 !important;
    margin: 0 !important;
    font-size: 16px !important;
    line-height: 1 !important;
    text-decoration: none !important;
    box-sizing: border-box !important;
}

.floating-buttons .floating-button:hover,
.floating-buttons .floating-button.share-button:hover,
.floating-buttons .floating-button.contact-button:hover,
.floating-buttons .floating-button.back-to-top:hover,
.floating-buttons button.floating-button:hover,
.floating-buttons a.floating-button:hover,
.floating-buttons button.share-button:hover,
.floating-buttons a.contact-button:hover,
.floating-buttons button.back-to-top:hover {
    transform: translateY(-3px) !important;
    background: #000000 !important; /* 黑色背景 */
    color: var(--color-accent) !important; /* 金色文字 */
    border-color: #000000 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
    width: 50px !important; /* 确保悬停时尺寸不变 */
    height: 50px !important;
}

.floating-button[data-tooltip]:before {
    content: attr(data-tooltip);
    position: absolute;
    right: 60px;
    background: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.floating-button:hover[data-tooltip]:before {
    opacity: 1;
    visibility: visible;
}

/* 返回顶部按钮 */
.back-to-top {
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

/* 分享按钮 */
.share-button-wrapper {
    position: relative;
}

.share-menu {
    position: absolute;
    right: 60px;
    bottom: 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 10px;
    display: none;
    flex-direction: column;
    gap: 10px;
    min-width: 150px;
}

.share-menu.active {
    display: flex;
}

.share-menu-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.share-menu-item:hover {
    background: #f5f5f5;
}

.share-menu-item svg {
    width: 20px;
    height: 20px;
}

/* 联系我们按钮 - 特殊配色但尺寸与其他按钮一致 */
.floating-buttons .contact-button,
.floating-buttons a.contact-button,
.floating-buttons .floating-button.contact-button {
    background: var(--color-accent) !important; /* 金色背景 */
    color: #000000 !important; /* 黑色文字 */
    border: 2px solid var(--color-accent) !important;
    /* 确保尺寸与其他按钮完全一致 */
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    min-height: 50px !important;
    max-width: 50px !important;
    max-height: 50px !important;
    box-sizing: border-box !important;
}

.floating-buttons .contact-button:hover,
.floating-buttons a.contact-button:hover,
.floating-buttons .floating-button.contact-button:hover {
    background: #000000 !important; /* 黑色背景 */
    color: var(--color-accent) !important; /* 金色文字 */
    border-color: #000000 !important;
    width: 50px !important; /* 确保悬停时尺寸不变 */
    height: 50px !important;
}

/* 确保悬浮按钮内的图标大小一致 */
.floating-button i,
.floating-buttons .floating-button i,
.floating-buttons button i,
.floating-buttons a i,
.back-to-top i,
.share-button i,
.contact-button i {
    font-size: 16px !important;
    line-height: 1 !important;
    width: auto !important;
    height: auto !important;
}

/* 确保悬浮按钮内的文字大小一致 */
.floating-button span,
.floating-buttons .floating-button span,
.floating-buttons button span,
.floating-buttons a span {
    font-size: 12px !important;
    line-height: 1 !important;
    font-weight: 500 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .floating-buttons {
        right: 20px;
        bottom: 20px;
        gap: 10px;
    }

    /* 移动端所有悬浮按钮统一尺寸 */
    .floating-buttons .floating-button,
    .floating-buttons .floating-button.share-button,
    .floating-buttons .floating-button.contact-button,
    .floating-buttons .floating-button.back-to-top,
    .floating-buttons button.floating-button,
    .floating-buttons a.floating-button,
    .floating-buttons button.share-button,
    .floating-buttons a.contact-button,
    .floating-buttons button.back-to-top {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        min-height: 40px !important;
        max-width: 40px !important;
        max-height: 40px !important;
    }

    .floating-buttons .floating-button svg,
    .floating-buttons button svg,
    .floating-buttons a svg {
        width: 20px !important;
        height: 20px !important;
    }

    .share-menu {
        right: 50px;
        min-width: 130px;
    }
}

/* 左侧滑出菜单样式 */
.left-panel-menu-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    width: 300px;
    background-color: var(--color-surface);
    transition: transform var(--transition-medium);
    transform: translateX(-100%);
    z-index: 99999;
    overflow-y: auto;
    visibility: visible;
    box-shadow: var(--shadow-medium);
}

.left-panel-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
    background: #ffffff; /* 白色背景 */
    border: 2px solid #000000; /* 黑色边框 */
    border-radius: 0; /* 方形设计 */
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-text-dark);
    transition: all var(--transition-medium);
    z-index: 1;
}

.left-panel-close:hover {
    background: #000000; /* 黑色背景 */
    color: var(--color-accent); /* 金色文字 */
}

.left-panel-close::before,
.left-panel-close::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 2px;
    background-color: currentColor;
    transition: transform var(--transition-medium);
}

.left-panel-close::before {
    transform: rotate(45deg);
}

.left-panel-close::after {
    transform: rotate(-45deg);
}

.left-panel-close:hover::before {
    transform: rotate(225deg);
}

.left-panel-close:hover::after {
    transform: rotate(135deg);
}

/*
 * 以下为从 functions.php 迁移过来的关键渲染路径优化 CSS。
 * 优化网站头部、动画加载效果和页面加载状态。
 */
.site-header { opacity: 1; transition: background-color 0.3s ease, padding 0.3s ease, box-shadow 0.3s ease; }
.fade-in { opacity: 0; transition: opacity 1s ease; }
.slide-up { opacity: 0; transform: translateY(30px); transition: opacity 1s ease, transform 1s ease; }
.fade-in.animated, .slide-up.animated { opacity: 1; transform: translateY(0); }

/* 页面加载状态 */
.page-loading:before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--color-accent);
    animation: loading 1s ease infinite;
    z-index: 9999;
}

@keyframes loading {
    0% { width: 0; left: 0; }
    50% { width: 100%; left: 0; }
    100% { width: 0; left: 100%; }
}

/* 面包屑导航样式调整，避免与固定头部重叠 */
.light-fixture-breadcrumbs {
    margin-top: 120px; /* 默认顶部间距 */
    padding: 10px 0; /* 额外内边距 */
}

/* 确保在有 WordPress 管理条时，面包屑有足够空间 */
body.admin-bar .light-fixture-breadcrumbs {
    margin-top: 40px; /* 当管理条存在时，调整更大间距 */
}

/* 面包屑导航基础美化样式 */
.light-fixture-breadcrumbs {
    font-size: 0.9em; /* 字体大小稍小 */
    color: #666; /* 默认文字颜色 */
    padding: 10px 0; /* 垂直内边距 */
    margin-bottom: 20px; /* 与下方内容的间距 */
    /* background-color: #f8f8f8; /* 可选：背景色 */
    /* border-radius: 4px; /* 可选：圆角 */
    /* padding-left: 15px; /* 可选：左侧内边距 */
}

.light-fixture-breadcrumbs a {
    color: #333; /* 链接颜色 */
    text-decoration: none; /* 去除下划线 */
}

.light-fixture-breadcrumbs a:hover {
    text-decoration: underline; /* 鼠标悬停时显示下划线 */
}

.light-fixture-breadcrumbs span.current {
    font-weight: bold; /* 当前页面文字加粗 */
    color: #333; /* 当前页面颜色 */
}

/* 分隔符样式 */
.light-fixture-breadcrumbs .separator {
    margin: 0 5px; /* 分隔符左右间距 */
    color: #999; /* 分隔符颜色 */
}

/* 将默认的分隔符文本也样式化 */
.light-fixture-breadcrumbs > a + a:before,
.light-fixture-breadcrumbs > a + span:before,
.light-fixture-breadcrumbs > span + a:before,
.light-fixture-breadcrumbs > span + span:before {
    content: ""; /* 默认分隔符 */
    padding: 0 5px;
    color: #999;
    font-weight: normal;
}

/* 针对 implode 生成的分隔符 */
.light-fixture-breadcrumbs {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.light-fixture-breadcrumbs > *:not(:last-child):after {
    content: "";
    padding: 0 5px;
    color: #999;
}

.light-fixture-breadcrumbs > *:last-child:after {
    content: none;
}

/* =========================================
   轮播图样式 - 修复版
   ========================================= */

/* 轮播图容器 */
.hero-slider {
    position: relative;
    width: 100%;
    height: 100vh;
    min-height: 600px;
    max-height: 800px;
    overflow: hidden; /* 确保内容不会溢出 */
}

/* 轮播图幻灯片 */
.hero-slide {
    position: relative;
    width: 100%;
    height: 100%;
    display: block !important; /* 覆盖可能的冲突样式 */
    float: none !important; /* 覆盖可能的冲突样式 */
    overflow: hidden; /* 确保内容不会溢出 */
}

/* 轮播图背景 */
.hero-slide .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

/* 轮播图图片 */
.hero-slide .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover; /* 确保图片覆盖整个容器 */
    object-position: center; /* 图片居中 */
}

/* 轮播图内容容器 */
.hero-slide .container {
    position: relative;
    z-index: 2;
    height: 100%;
    display: flex;
    align-items: center;
}

/* 轮播图内容 */
.hero-slide .hero-content {
    max-width: 650px;
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    color: #fff; /* 确保文字颜色与背景对比 */
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); /* 添加文字阴影增加可读性 */
    padding: 20px; /* 添加内边距 */
    /* 添加半透明背景增加可读性 */
    /* background-color: rgba(0, 0, 0, 0.2); */
    border-radius: 5px; /* 圆角 */
}

/* 轮播图内容动画 */
.hero-slide .hero-content.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Slick 轮播图样式覆盖 */
.slick-slider {
    position: relative;
    display: block;
    box-sizing: border-box;
    user-select: none;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
    width: 100%;
    height: 100%;
}

.slick-list {
    position: relative;
    display: block;
    overflow: hidden;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}

.slick-track {
    position: relative;
    top: 0;
    left: 0;
    display: block;
    width: 100% !important;
    height: 100%;
    transform: translate3d(0, 0, 0) !important; /* 强制禁用水平滚动 */
}

.slick-slide {
    display: none;
    float: none !important; /* 覆盖可能的冲突样式 */
    height: 100%;
    min-height: 1px;
    width: 100% !important; /* 强制宽度为100% */
    position: absolute !important; /* 强制绝对定位 */
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.5s ease;
    z-index: 0;
}

.slick-slide.slick-active {
    display: block;
    z-index: 1; /* 确保活动幻灯片在最上层 */
    opacity: 1;
}

.slick-slide > div {
    height: 100%;
    width: 100%;
}

/* 轮播图导航样式 */
.hero-slider .slick-dots {
    position: absolute;
    bottom: 30px;
    left: 0;
    width: 100%;
    text-align: center;
    z-index: 10;
    margin: 0;
    padding: 0;
    list-style: none;
}

.hero-slider .slick-dots li {
    display: inline-block;
    margin: 0 5px;
    padding: 0;
}

.hero-slider .slick-dots li button {
    font-size: 0;
    line-height: 0;
    width: 12px;
    height: 12px;
    padding: 0;
    border: 2px solid #fff;
    border-radius: 50%;
    background: transparent;
    cursor: pointer;
    outline: none;
    transition: background-color 0.3s ease;
}

.hero-slider .slick-dots li button:before {
    position: unset!important;
    font-family: unset!important;
    font-size: unset!important;
}

.hero-slider .slick-dots li.slick-active button {
    background-color: #fff;
}

/* 轮播图箭头样式 */
.hero-slider .slick-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 50px;
    height: 50px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    color: #fff;
    font-size: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.hero-slider .slick-arrow:hover {
    background: rgba(255, 255, 255, 0.4);
}

.hero-slider .slick-prev {
    left: 30px;
}

.hero-slider .slick-next {
    right: 30px;
}

.hero-slider .slick-arrow svg {
    width: 24px;
    height: 24px;
}

/* 修复Slick轮播图的一些常见问题 */
.slick-initialized .slick-slide {
    display: block;
}

/* 强制覆盖可能的冲突样式 */
.hero-slider .slick-list,
.hero-slider .slick-track {
    height: 100% !important;
}

.hero-slider .slick-slide {
    visibility: hidden;
}

.hero-slider .slick-slide.slick-active {
    visibility: visible;
}

/* 响应式调整 */
@media (max-width: 991px) {
    .hero-slider {
        height: 80vh;
        min-height: 500px;
    }
    
    .hero-slider .slick-dots {
        bottom: 20px;
    }
}

@media (max-width: 767px) {
    .hero-slider {
        height: 70vh;
        min-height: 400px;
    }
    
    .hero-slide .hero-content {
        max-width: 100%;
        padding: 15px;
    }
    
    .hero-slide .hero-title {
        font-size: clamp(2rem, 5vw, 2.5rem);
    }
    
    .hero-slide .hero-description {
        font-size: 0.9rem;
    }
    
    .hero-slider .slick-dots {
        bottom: 10px;
    }
}

/* 强制重置 Slick 计算的 left 值，避免负偏移 */
.hero-slider .slick-slide {
    left: 0 !important;
}

/* =========================================
   页面模板样式 - 确保所有页面都有统一的样式
   ========================================= */

.page-content,
.single-content,
.entry-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.page-header,
.entry-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--color-border);
}

.page-title,
.entry-title {
    font-family: 'Playfair Display', 'Georgia', serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 400;
    color: var(--color-text-dark);
    margin-bottom: 1rem;
}

.entry-meta {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
    margin-bottom: 1rem;
}

.entry-meta span {
    margin-right: 1rem;
}

.page-featured-image,
.post-featured-image {
    margin-bottom: 2rem;
    text-align: center;
}

.page-featured-image img,
.post-featured-image img {
    max-width: 100%;
    height: auto;
    border-radius: 0; /* 方形设计 */
}

.entry-footer {
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--color-border);
    font-size: 0.9rem;
    color: var(--color-text-secondary);
}

.entry-footer span {
    display: block;
    margin-bottom: 0.5rem;
}

/* 确保页面在所有设备上都正确显示 */
@media (max-width: 768px) {
    .page-content,
    .single-content,
    .entry-content {
        padding: 1rem;
    }

    body {
        padding-top: 70px; /* 移动端稍微减少顶部间距 */
    }
}

/* =========================================
   悬浮按钮组样式强化 - 防止被全局样式覆盖
   ========================================= */

/* 使用最高特异性来确保悬浮按钮样式不被覆盖 */
div.floating-buttons button.floating-button,
div.floating-buttons a.floating-button,
div.floating-buttons .share-button,
div.floating-buttons .contact-button,
div.floating-buttons .back-to-top {
    width: 50px !important;
    height: 50px !important;
    min-width: 50px !important;
    min-height: 50px !important;
    max-width: 50px !important;
    max-height: 50px !important;
    flex: none !important; /* 防止 flex 属性影响尺寸 */
    flex-shrink: 0 !important;
    flex-grow: 0 !important;
    flex-basis: auto !important;
}

/* 确保 SVG 图标不影响按钮尺寸 */
div.floating-buttons button.floating-button svg,
div.floating-buttons a.floating-button svg,
div.floating-buttons .share-button svg,
div.floating-buttons .contact-button svg,
div.floating-buttons .back-to-top svg {
    width: 24px !important;
    height: 24px !important;
    flex-shrink: 0 !important;
}

/* 移动端强化 */
@media (max-width: 768px) {
    div.floating-buttons button.floating-button,
    div.floating-buttons a.floating-button,
    div.floating-buttons .share-button,
    div.floating-buttons .contact-button,
    div.floating-buttons .back-to-top {
        width: 40px !important;
        height: 40px !important;
        min-width: 40px !important;
        min-height: 40px !important;
        max-width: 40px !important;
        max-height: 40px !important;
    }

    div.floating-buttons button.floating-button svg,
    div.floating-buttons a.floating-button svg,
    div.floating-buttons .share-button svg,
    div.floating-buttons .contact-button svg,
    div.floating-buttons .back-to-top svg {
        width: 20px !important;
        height: 20px !important;
    }
}