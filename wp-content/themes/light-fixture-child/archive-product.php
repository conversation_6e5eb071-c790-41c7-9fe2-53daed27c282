<?php
/**
 * The template for displaying Product Archive pages.
 *
 * @package Light_Fixture_Child
 */

get_header(); // 加载主题头部
?>

<main id="main" class="site-main">
    <div class="container">
        <?php light_fixture_breadcrumbs(); ?>
        <header class="page-header">
            <?php
            if ( is_post_type_archive( 'product' ) ) {
                // 这是产品归档主页 (e.g., /products/)
                echo '<h1 class="page-title">All Products</h1>';
                echo '<p class="page-description">Explore Our Curated Lighting Collection</p>';
            } elseif ( is_tax( 'product_category' ) ) {
                // 这是产品分类归档页 (e.g., /product-category/mirrors/)
                $term = get_queried_object();
                echo '<h1 class="page-title">' . esc_html( $term->name ) . 'Collection</h1>';
                if ( ! empty( $term->description ) ) {
                    echo '<p class="page-description">' . esc_html( $term->description ) . '</p>';
                }
            }
            ?>
        </header>

        <div class="products-grid">
            <?php
            if ( have_posts() ) :
                while ( have_posts() ) : the_post();
                    // 获取产品分类名称
                    $product_categories = get_the_terms( get_the_ID(), 'product_category' );
                    $category_name = $product_categories ? $product_categories[0]->name : '';
                    ?>
                    <div class="product-card fade-in">
                        <div class="product-card__image-container">
                            <?php if ( has_post_thumbnail() ) : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php the_post_thumbnail( 'product-thumbnail', array( 'class' => 'product-card__image', 'alt' => get_the_title() ) ); ?>
                                </a>
                            <?php else : ?>
                                <a href="<?php the_permalink(); ?>">
                                    <?php
                                    $placeholder_url = get_stylesheet_directory_uri() . '/assets/images/placeholder.jpg';
                                    if (function_exists('light_fixture_get_optimized_image_url')) {
                                        $placeholder_url = light_fixture_get_optimized_image_url($placeholder_url);
                                    }
                                    ?>
                                    <img src="<?php echo esc_url($placeholder_url); ?>" alt="<?php the_title(); ?>" class="product-card__image">
                                </a>
                            <?php endif; ?>
                            <div class="product-card__overlay">
                                <a href="<?php the_permalink(); ?>" class="product-card__view-button">View Details</a>
                            </div>
                        </div>
                        <div class="product-card__content">
                            <?php if ( $category_name ) : ?>
                                <span class="product-category"><?php echo esc_html( $category_name ); ?></span>
                            <?php endif; ?>
                            <h4 class="product-card__title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                            <p class="product-card__description"><?php the_excerpt(); ?></p>
                        </div>
                    </div>
                    <?php
                endwhile;

                // 分页导航
                the_posts_pagination( array(
                    'prev_text'          => __( 'Previous Page', 'light-fixture-child' ),
                    'next_text'          => __( 'Next Page', 'light-fixture-child' ),
                    'before_page_number' => '<span class="meta-nav screen-reader-text">' . __( 'Page', 'light-fixture-child' ) . ' </span>',
                ) );

            else :
                // 没有找到产品
                echo '<p>Sorry, no products found matching your criteria.</p>';
            endif;
            ?>
        </div>
    </div>
</main>

<?php
get_footer(); // 加载主题底部
?> 