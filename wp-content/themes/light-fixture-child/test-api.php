<?php
/**
 * 测试 REST API 是否正确返回产品分类数据
 */

// 加载 WordPress
require_once('wp-load.php');

// 设置页面头信息
header('Content-Type: text/html; charset=utf-8');

// 输出页面头部
echo '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>REST API 测试</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }
        h1, h2 { color: #333; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>REST API 测试</h1>';

// 测试产品分类 REST API
echo '<h2>产品分类 REST API 测试</h2>';

// 获取产品分类数据
$response = wp_remote_get(rest_url('wp/v2/product-category'), array(
    'timeout' => 30,
    'sslverify' => false,
));

if (is_wp_error($response)) {
    echo '<p class="error">API 请求失败: ' . $response->get_error_message() . '</p>';
} else {
    $status_code = wp_remote_retrieve_response_code($response);
    $body = wp_remote_retrieve_body($response);
    
    echo '<p>状态码: ' . $status_code . '</p>';
    
    if ($status_code === 200) {
        echo '<p class="success">API 请求成功!</p>';
        
        $data = json_decode($body);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            echo '<p>找到 ' . count($data) . ' 个产品分类</p>';
            
            if (count($data) > 0) {
                echo '<h3>产品分类数据:</h3>';
                echo '<pre>' . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
            } else {
                echo '<p>没有产品分类数据。</p>';
            }
        } else {
            echo '<p class="error">JSON 解析失败: ' . json_last_error_msg() . '</p>';
            echo '<pre>' . htmlspecialchars($body) . '</pre>';
        }
    } else {
        echo '<p class="error">API 请求失败，状态码: ' . $status_code . '</p>';
        echo '<pre>' . htmlspecialchars($body) . '</pre>';
    }
}

// 检查分类法注册情况
echo '<h2>分类法注册检查</h2>';

$taxonomies = get_taxonomies(array(), 'objects');
$found = false;

echo '<ul>';
foreach ($taxonomies as $taxonomy) {
    if ($taxonomy->name === 'product_category') {
        $found = true;
        echo '<li class="success"><strong>产品分类 (product_category)</strong> 已注册</li>';
        echo '<li>REST 基础路径: ' . $taxonomy->rest_base . '</li>';
        echo '<li>在 REST 中显示: ' . ($taxonomy->show_in_rest ? '是' : '否') . '</li>';
        echo '<li>REST 控制器类: ' . $taxonomy->rest_controller_class . '</li>';
    }
}
echo '</ul>';

if (!$found) {
    echo '<p class="error">未找到产品分类 (product_category) 分类法!</p>';
}

// 输出页面底部
echo '</body>
</html>'; 