<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>
    <?php wp_body_open(); ?>
    <div class="left-panel-overlay"></div>
    <header id="masthead" class="site-header">
        <div class="container">
            <div class="site-header-inner">
                <div class="site-branding">
                    <?php if (has_custom_logo()) : ?>
                        <div class="site-logo"><?php the_custom_logo(); ?></div>
                    <?php else: ?>
                        <h1 class="site-logo"><a href="<?php echo esc_url(home_url('/')); ?>" rel="home"><?php echo esc_html(get_bloginfo('name')); ?></a></h1>
                    <?php endif; ?>
                </div>
                
                <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                    <span class="hamburger"></span>
                </button>
                
                <nav id="site-navigation" class="main-navigation">
                    <?php
                    // 如果没有主菜单，手动创建一个英文菜单
                    if (has_nav_menu('primary')) {
                        wp_nav_menu(array(
                            'theme_location' => 'primary',
                            'container' => false,
                            'menu_class' => 'menu',
                            'menu_id' => 'primary-menu',
                            'fallback_cb' => false,
                            'items_wrap' => '<ul id="%1$s" class="%2$s"><button class="left-panel-toggle menu-toggle" aria-controls="left-panel-menu-container" aria-expanded="false"><span class="hamburger"></span></button>%3$s</ul>'
                        ));
                    } else {
                        echo '<ul id="primary-menu" class="menu">';
                        echo '<button class="left-panel-toggle menu-toggle" aria-controls="left-panel-menu-container" aria-expanded="false"><span class="hamburger"></span></button>';
                        echo '<li><a href="' . esc_url(home_url('/')) . '">Home</a></li>';
                        echo '<li><a href="' . esc_url(home_url('/index.php/productslist/')) . '">Products</a></li>';
                        echo '<li><a href="' . esc_url(home_url('/index.php/about/')) . '">About Us</a></li>';
                        echo '<li><a href="' . esc_url(home_url('/index.php/contact/')) . '">Contact</a></li>';
                        echo '</ul>';
                    }
                    ?>
                </nav>
            </div>
        </div>
    </header>
    
    <div id="left-panel-menu-container" class="left-panel-menu-container">
        <div class="left-panel-content">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'left-panel-menu',
                'container' => false,
                'menu_class' => 'left-panel-menu',
                'menu_id' => 'left-panel-menu',
                'fallback_cb' => false,
                'depth' => 3
            ));
            ?>
        </div>
    </div>
    
    <div id="content" class="site-content">
        <?php if (!is_front_page() && !is_home() && get_header_image()) : ?>
            <div class="page-header">
                <img src="<?php header_image(); ?>" class="header-image" alt="<?php echo esc_attr(get_bloginfo('name')); ?>">
                <div class="page-header-overlay"></div>
                <div class="container">
                    <h1><?php the_title(); ?></h1>
                </div>
            </div>
        <?php endif; ?>