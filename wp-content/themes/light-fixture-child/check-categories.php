<?php
/**
 * 临时文件：检查产品分类数据
 */

// 加载 WordPress
require_once('wp-load.php');

// 获取产品分类
$args = array(
    'taxonomy' => 'product_category',
    'hide_empty' => false,
);

$product_categories = get_terms($args);

// 输出结果
echo '<h2>产品分类检查</h2>';

if (!empty($product_categories) && !is_wp_error($product_categories)) {
    echo '<p>找到 ' . count($product_categories) . ' 个产品分类:</p>';
    echo '<ul>';
    foreach ($product_categories as $category) {
        echo '<li>';
        echo 'ID: ' . $category->term_id . ' | ';
        echo '名称: ' . $category->name . ' | ';
        echo '别名: ' . $category->slug . ' | ';
        echo '描述: ' . $category->description;
        
        // 检查排序元数据
        $order = get_term_meta($category->term_id, 'product_category_order', true);
        echo ' | 排序值: ' . ($order ? $order : '未设置');
        
        echo '</li>';
    }
    echo '</ul>';
} else {
    if (is_wp_error($product_categories)) {
        echo '<p>获取产品分类时出错: ' . $product_categories->get_error_message() . '</p>';
    } else {
        echo '<p>未找到产品分类。</p>';
    }
}

// 检查分类法是否已注册
$taxonomies = get_taxonomies(array(), 'objects');
echo '<h3>已注册的分类法:</h3>';
echo '<ul>';
foreach ($taxonomies as $taxonomy) {
    echo '<li>' . $taxonomy->name . ' (' . $taxonomy->label . ')</li>';
}
echo '</ul>'; 