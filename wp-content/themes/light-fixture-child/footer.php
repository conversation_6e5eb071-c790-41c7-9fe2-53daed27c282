    </div><!-- #content -->
    
    <!-- 全局悬浮按钮组 -->
    <div class="floating-buttons">
        <!-- 分享按钮 -->
        <div class="share-button-wrapper">
            <button class="floating-button share-button" data-tooltip="Share" aria-label="Share">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="18" cy="5" r="3"></circle>
                    <circle cx="6" cy="12" r="3"></circle>
                    <circle cx="18" cy="19" r="3"></circle>
                    <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                    <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                </svg>
            </button>
            <div class="share-menu">
                <a href="#" class="share-menu-item" onclick="shareOnSocial('facebook')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                    </svg>
                    Facebook
                </a>
                <a href="#" class="share-menu-item" onclick="shareOnSocial('twitter')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"></path>
                    </svg>
                    Twitter
                </a>
                <a href="#" class="share-menu-item" onclick="shareOnSocial('linkedin')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"></path>
                        <rect x="2" y="9" width="4" height="12"></rect>
                        <circle cx="4" cy="4" r="2"></circle>
                    </svg>
                    LinkedIn
                </a>
                <a href="#" class="share-menu-item" onclick="shareOnSocial('pinterest')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M8 12h8"></path>
                        <path d="M12 8v8"></path>
                        <circle cx="12" cy="12" r="10"></circle>
                    </svg>
                    Pinterest
                </a>
            </div>
        </div>
        
        <!-- 联系我们按钮 -->
        <a href="<?php echo esc_url(home_url('/index.php/contact/#contact-form')); ?>" class="floating-button contact-button" data-tooltip="Contact Us" aria-label="Contact Us">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
            </svg>
        </a>
        
        <!-- 返回顶部按钮 -->
        <button class="floating-button back-to-top" data-tooltip="Back to Top" aria-label="Back to Top">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M18 15l-6-6-6 6"/>
            </svg>
        </button>
    </div>
    
    <footer id="colophon" class="site-footer">
        <div class="container">
            <div class="footer-widgets">
                <div class="footer-widget">
                    <h3>About Us</h3>
                    <p>We specialize in providing high-quality lighting solutions that add brilliance and warmth to your space. Elegant design and superior quality create an extraordinary lighting experience.</p>
                </div>
                <div class="footer-widget">
                    <h3>Contact Us</h3>
                    <p>Address: 1250 Broadway, New York, NY 10001</p>
                    <p>Phone: +****************</p>
                    <p>Email: <EMAIL></p>
                </div>
                <div class="footer-widget">
                    <h3>Quick Links</h3>
                    <ul>
                        <li><a href="<?php echo esc_url(home_url('/')); ?>">Home</a></li>
                        <li><a href="<?php echo esc_url(home_url('/index.php/productslist/')); ?>">Products</a></li>
                        <li><a href="<?php echo esc_url(home_url('/index.php/about/')); ?>">About Us</a></li>
                        <li><a href="<?php echo esc_url(home_url('/index.php/contact/')); ?>">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-widget">
                    <h3>Subscribe</h3>
                    <p>Join our newsletter to receive the latest product updates and lighting design inspiration.</p>
                    <form class="newsletter-form">
                        <input type="email" placeholder="Your email address" aria-label="Your email address" />
                        <button type="submit" aria-label="Subscribe">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="22" y1="2" x2="11" y2="13"></line><polygon points="22 2 15 22 11 13 2 9 22 2"></polygon></svg>
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="social-icons">
                    <?php
                    $social_links = get_social_media_links();
                    $platform_names = array(
                        'facebook' => 'Facebook',
                        'twitter' => 'Twitter',
                        'instagram' => 'Instagram',
                        'linkedin' => 'LinkedIn',
                        'youtube' => 'YouTube',
                        'pinterest' => 'Pinterest',
                        'tiktok' => 'TikTok',
                        'whatsapp' => 'WhatsApp'
                    );

                    if (!empty($social_links)) {
                        foreach ($social_links as $platform => $url) {
                            $icon_svg = get_social_media_icon($platform);
                            $platform_name = isset($platform_names[$platform]) ? $platform_names[$platform] : ucfirst($platform);

                            if ($icon_svg) {
                                echo '<a href="' . esc_url($url) . '" target="_blank" rel="noopener noreferrer" aria-label="' . esc_attr($platform_name) . '">';
                                echo '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">';
                                echo $icon_svg;
                                echo '</svg>';
                                echo '</a>';
                            }
                        }
                    }
                    ?>
                </div>
                <p class="copyright">&copy; <?php echo date_i18n('Y'); ?> Illumina. All Rights Reserved</p>
            </div>
        </div>
    </footer>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Back to top button functionality
        const backToTopButton = document.querySelector('.back-to-top');
        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('visible');
                } else {
                    backToTopButton.classList.remove('visible');
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Share button functionality
        const shareButton = document.querySelector('.share-button');
        const shareMenu = document.querySelector('.share-menu');
        
        if (shareButton && shareMenu) {
            shareButton.addEventListener('click', function(e) {
                e.stopPropagation();
                shareMenu.classList.toggle('active');
            });

            // Close share menu when clicking elsewhere
            document.addEventListener('click', function(e) {
                if (!shareMenu.contains(e.target) && !shareButton.contains(e.target)) {
                    shareMenu.classList.remove('active');
                }
            });
        }
    });

    // Social media sharing functionality
    function shareOnSocial(platform) {
        const url = encodeURIComponent(window.location.href);
        const title = encodeURIComponent(document.title);
        let shareUrl;

        switch(platform) {
            case 'facebook':
                shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${url}`;
                break;
            case 'twitter':
                shareUrl = `https://twitter.com/intent/tweet?url=${url}&text=${title}`;
                break;
            case 'linkedin':
                shareUrl = `https://www.linkedin.com/shareArticle?mini=true&url=${url}&title=${title}`;
                break;
            case 'pinterest':
                shareUrl = `https://pinterest.com/pin/create/button/?url=${url}&description=${title}`;
                break;
        }

        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    }
    </script>

    <?php wp_footer(); ?>
</body>
</html> 