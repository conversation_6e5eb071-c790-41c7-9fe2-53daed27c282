/**
 * light-fixture-child-theme/js/specs-admin.js
 * 产品技术规格元框的动态交互脚本
 * 用于处理规格行的添加和移除功能。
 */
jQuery(document).ready(function($) {
    var specsList = $('#light-fixture-specifications-container .light-fixture-specs-list');
    var addSpecButton = $('#light-fixture-add-spec');
    var nextSpecIndex = specsList.find('tr').length; // 初始化下一个可用的索引

    // 添加规格行功能
    addSpecButton.on('click', function(e) {
        e.preventDefault();
        var newSpecRow = $('<tr>' +
            '<td><input type="text" name="_product_specifications[' + nextSpecIndex + '][label]" value="" class="large-text" placeholder="例如: 尺寸"></td>' +
            '<td><input type="text" name="_product_specifications[' + nextSpecIndex + '][value]" value="" class="large-text" placeholder="例如: 直径 60cm × 高 45cm"></td>' +
            '<td><button type="button" class="button button-secondary light-fixture-remove-spec">移除</button></td>' +
            '</tr>');
        specsList.append(newSpecRow);
        nextSpecIndex++; // 递增索引
    });

    // 移除规格行功能 (使用事件委托)
    specsList.on('click', '.light-fixture-remove-spec', function(e) {
        e.preventDefault();
        $(this).closest('tr').remove();
        // 注意：移除后不重新调整索引，因为PHP会基于键名重新构建数组
    });
}); 