/**
 * Light Fixture Child Theme Animation JS
 * 
 * 提供所有页面通用的平滑动画效果
 */
document.addEventListener("DOMContentLoaded", function() {
    // 动画效果
    const animatedElements = document.querySelectorAll(".fade-in, .slide-up");
    
    if ("IntersectionObserver" in window) {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = "1";
                    entry.target.style.transform = "translateY(0)";
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.1 });
        
        animatedElements.forEach(el => {
            observer.observe(el);
        });
    } else {
        // 兼容不支持IntersectionObserver的浏览器
        animatedElements.forEach(el => {
            el.style.opacity = "1";
            el.style.transform = "translateY(0)";
        });
    }
    
    // 导航菜单固定效果
    const siteHeader = document.querySelector(".site-header");
    if (siteHeader) {
        window.addEventListener("scroll", function() {
            if (window.scrollY > 100) {
                siteHeader.classList.add("scrolled");
            } else {
                siteHeader.classList.remove("scrolled");
            }
        });
    }
    
    // 移动端菜单切换
    const menuToggle = document.querySelector(".menu-toggle");
    const mainNav = document.querySelector(".main-navigation ul");
    
    if (menuToggle && mainNav) {
        menuToggle.addEventListener("click", function() {
            menuToggle.classList.toggle("active");
            mainNav.classList.toggle("active");
        });
    }
    
    // 产品筛选按钮
    const filterOptions = document.querySelectorAll(".filter-option");
    
    if (filterOptions.length) {
        filterOptions.forEach(option => {
            option.addEventListener("click", function() {
                const category = this.getAttribute("data-category");
                
                // 激活当前点击的选项
                filterOptions.forEach(opt => opt.classList.remove("active"));
                this.classList.add("active");
                
                // 显示相应类别的产品
                const productCategories = document.querySelectorAll(".product-category");
                
                if (category === "all") {
                    productCategories.forEach(cat => {
                        cat.classList.add("active");
                        const products = cat.querySelectorAll(".product-card");
                        products.forEach(product => {
                            product.classList.add("fade-in");
                            setTimeout(() => {
                                product.style.opacity = "1";
                                product.style.transform = "translateY(0)";
                            }, 100);
                        });
                    });
                } else {
                    productCategories.forEach(cat => {
                        if (cat.getAttribute("data-category") === category) {
                            cat.classList.add("active");
                            const products = cat.querySelectorAll(".product-card");
                            products.forEach(product => {
                                product.classList.add("fade-in");
                                setTimeout(() => {
                                    product.style.opacity = "1";
                                    product.style.transform = "translateY(0)";
                                }, 100);
                            });
                        } else {
                            cat.classList.remove("active");
                        }
                    });
                }
            });
        });
    }
    
    // FAQ 手风琴效果
    const faqItems = document.querySelectorAll(".faq-item");
    
    if (faqItems.length) {
        faqItems.forEach(item => {
            const question = item.querySelector(".faq-question");
            
            if (question) {
                question.addEventListener("click", function() {
                    // 如果当前项已激活，则关闭它
                    if (item.classList.contains("active")) {
                        item.classList.remove("active");
                        question.setAttribute("aria-expanded", "false");
                        item.querySelector(".faq-answer").setAttribute("aria-hidden", "true");
                    } else {
                        // 先关闭所有打开的项
                        faqItems.forEach(faq => {
                            faq.classList.remove("active");
                            const q = faq.querySelector(".faq-question");
                            if (q) q.setAttribute("aria-expanded", "false");
                            const a = faq.querySelector(".faq-answer");
                            if (a) a.setAttribute("aria-hidden", "true");
                        });
                        
                        // 打开当前项
                        item.classList.add("active");
                        question.setAttribute("aria-expanded", "true");
                        item.querySelector(".faq-answer").setAttribute("aria-hidden", "false");
                    }
                });
            }
        });
    }
}); 