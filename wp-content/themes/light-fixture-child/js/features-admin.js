/**
 * light-fixture-child-theme/js/features-admin.js
 * 产品特点元框的动态交互脚本
 * 用于处理特点的添加和移除功能。
 */
jQuery(document).ready(function($) {
    var featuresList = $('#light-fixture-features-container .light-fixture-features-list');
    var addFeatureButton = $('#light-fixture-add-feature');

    // 添加特点功能
    addFeatureButton.on('click', function(e) {
        e.preventDefault();
        var newFeatureItem = $('<li><input type="text" name="_product_features[]" value="" class="large-text" placeholder="输入产品特点"><button type="button" class="button button-secondary light-fixture-remove-feature">移除</button></li>');
        featuresList.append(newFeatureItem);
    });

    // 移除特点功能 (使用事件委托)
    featuresList.on('click', '.light-fixture-remove-feature', function(e) {
        e.preventDefault();
        $(this).closest('li').remove();
    });
}); 