/**
 * light-fixture-child-theme/js/options-group-admin.js
 * 产品定制选项元框 - 选项组的动态交互脚本
 * 用于处理选项组的添加和移除功能。
 */
jQuery(document).ready(function($) {
    var optionGroupsList = $('#light-fixture-options-container .light-fixture-option-groups');
    var addOptionGroupButton = $('#light-fixture-add-option-group');
    var nextGroupIndex = optionGroupsList.find('> li.option-group-item').length; // 初始化下一个可用组索引

    // 添加选项组功能
    addOptionGroupButton.on('click', function(e) {
        e.preventDefault();
        var newOptionGroup = $('<li class="option-group-item">' +
            '<div class="option-group-header">' +
            '<input type="text" name="_product_customization[' + nextGroupIndex + '][label]" value="" class="large-text option-group-label" placeholder="例如: 颜色温度">' +
            '<button type="button" class="button button-secondary light-fixture-remove-option-group">移除选项组</button>' +
            '</div>' +
            '<ul class="light-fixture-option-values">' +
            // 默认添加一个空的选项值行，方便用户填写
            '<li class="option-value-item">' +
            '<input type="text" name="_product_customization[' + nextGroupIndex + '][values][0][label]" value="" class="regular-text" placeholder="显示名称 (例如: 暖白)">' +
            '<input type="text" name="_product_customization[' + nextGroupIndex + '][values][0][value]" value="" class="regular-text" placeholder="实际值 (例如: 2700K)">' +
            '<label><input type="checkbox" name="_product_customization[' + nextGroupIndex + '][values][0][default]" value="1">默认</label>' +
            '<button type="button" class="button button-secondary light-fixture-remove-option-value">移除</button>' +
            '</li>' +
            '</ul>' +
            '<button type="button" class="button button-secondary light-fixture-add-option-value">添加选项值</button>' +
            '</li>');
        optionGroupsList.append(newOptionGroup);
        nextGroupIndex++; // 递增索引
    });

    // 移除选项组功能 (使用事件委托)
    optionGroupsList.on('click', '.light-fixture-remove-option-group', function(e) {
        e.preventDefault();
        $(this).closest('.option-group-item').remove();
        // 注意：移除后不重新调整索引，因为PHP会基于键名重新构建数组
    });

    // 获取下一个可用的选项值索引 (对于新的选项组)
    function getNextOptionValueIndex(optionValuesList) {
        return optionValuesList.find('> li.option-value-item').length;
    }

    // 添加选项值功能 (使用事件委托，监听整个选项组列表)
    optionGroupsList.on('click', '.light-fixture-add-option-value', function(e) {
        e.preventDefault();
        var currentOptionGroup = $(this).closest('.option-group-item');
        var optionValuesList = currentOptionGroup.find('.light-fixture-option-values');
        
        // 获取当前选项组的索引
        var groupIndex = currentOptionGroup.index(); 
        // 查找当前组内下一个可用的选项值索引
        var nextValueIndex = getNextOptionValueIndex(optionValuesList);

        var newOptionValue = $('<li class="option-value-item">' +
            '<input type="text" name="_product_customization[' + groupIndex + '][values][' + nextValueIndex + '][label]" value="" class="regular-text" placeholder="显示名称 (例如: 暖白)">' +
            '<input type="text" name="_product_customization[' + groupIndex + '][values][' + nextValueIndex + '][value]" value="" class="regular-text" placeholder="实际值 (例如: 2700K)">' +
            '<label><input type="checkbox" name="_product_customization[' + groupIndex + '][values][' + nextValueIndex + '][default]" value="1">默认</label>' +
            '<button type="button" class="button button-secondary light-fixture-remove-option-value">移除</button>' +
            '</li>');
        optionValuesList.append(newOptionValue);
    });

    // 移除选项值功能 (使用事件委托)
    optionGroupsList.on('click', '.light-fixture-remove-option-value', function(e) {
        e.preventDefault();
        $(this).closest('.option-value-item').remove();
    });
}); 