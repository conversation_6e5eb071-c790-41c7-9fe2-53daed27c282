jQuery(document).ready(function($) {
    // 初始化媒体上传器
    var mediaUploader;
    
    // 添加图片按钮点击事件
    $('#light-fixture-add-gallery-image').on('click', function(e) {
        e.preventDefault();
        
        // 如果媒体上传器已经存在，则重新打开
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }
        
        // 创建新的媒体上传器
        mediaUploader = wp.media({
            title: '选择产品图片',
            button: {
                text: '添加到图集'
            },
            multiple: true
        });
        
        // 当选择图片时
        mediaUploader.on('select', function() {
            var attachments = mediaUploader.state().get('selection').toJSON();
            var galleryIds = $('#light_fixture_product_gallery_ids').val();
            var idsArray = galleryIds ? galleryIds.split(',') : [];
            
            // 添加新选择的图片
            attachments.forEach(function(attachment) {
                if (!idsArray.includes(attachment.id.toString())) {
                    idsArray.push(attachment.id);
                    var imageHtml = '<li data-id="' + attachment.id + '">' +
                        '<img src="' + attachment.sizes.thumbnail.url + '" alt="">' +
                        '<a href="#" class="light-fixture-gallery-remove dashicons dashicons-no-alt"></a>' +
                        '</li>';
                    $('.light-fixture-gallery-images').append(imageHtml);
                }
            });
            
            // 更新隐藏输入框的值
            $('#light_fixture_product_gallery_ids').val(idsArray.join(','));
        });
        
        // 打开媒体上传器
        mediaUploader.open();
    });
    
    // 删除图片
    $(document).on('click', '.light-fixture-gallery-remove', function(e) {
        e.preventDefault();
        var imageId = $(this).parent().data('id');
        var galleryIds = $('#light_fixture_product_gallery_ids').val();
        var idsArray = galleryIds.split(',');
        
        // 从数组中移除图片ID
        idsArray = idsArray.filter(function(id) {
            return id != imageId;
        });
        
        // 更新隐藏输入框的值
        $('#light_fixture_product_gallery_ids').val(idsArray.join(','));
        
        // 移除图片元素
        $(this).parent().remove();
    });
    
    // 初始化拖拽排序
    $('.light-fixture-gallery-images').sortable({
        update: function(event, ui) {
            var idsArray = [];
            $('.light-fixture-gallery-images li').each(function() {
                idsArray.push($(this).data('id'));
            });
            $('#light_fixture_product_gallery_ids').val(idsArray.join(','));
        }
    });
}); 