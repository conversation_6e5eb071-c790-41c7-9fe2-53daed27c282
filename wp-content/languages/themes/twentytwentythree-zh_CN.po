# Translation of Themes - Twenty Twenty-Three in Chinese (China)
# This file is distributed under the same license as the Themes - Twenty Twenty-Three package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-11-24 09:56:31+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: zh_CN\n"
"Project-Id-Version: Themes - Twenty Twenty-Three\n"

#. Description of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three is designed to take advantage of the new design tools introduced in WordPress 6.1. With a clean, blank base as a starting point, this default theme includes ten diverse style variations created by members of the WordPress community. Whether you want to build a complex or incredibly simple website, you can do it quickly and intuitively through the bundled styles or dive into creation and full customization yourself."
msgstr "二〇二三的设计只在充分利用 WordPress 6.1 中引入的新设计工具。 主题在简洁、清爽的基础之上，同时还包含了 WordPress 社区成员打造的十种不同的样式变体。 无论是复杂还是简单的网站外观设计，您都可通过捆绑的样式快速和直观地完成，或自行潜心创作并完全定制。"

#. Theme Name of the theme
#: style.css
#, gp-priority: high
msgid "Twenty Twenty-Three"
msgstr "二〇二三"

#: patterns/hidden-heading.php:9
msgctxt "Main heading for homepage"
msgid "Mindblown: a blog about philosophy."
msgstr "Mindblown：一个关于哲学的博客。"

#: patterns/hidden-heading.php
msgctxt "Pattern title"
msgid "Hidden Heading for Homepage"
msgstr "主页隐藏标题"

#: patterns/post-meta.php
msgctxt "Pattern description"
msgid "Post meta information with separator on the top."
msgstr "文章元信息，顶部有分隔符。"

#: patterns/footer-default.php
msgctxt "Pattern description"
msgid "Footer with site title and powered by WordPress."
msgstr "页脚有网站标题，由 WordPress 提供。"

#: patterns/call-to-action.php
msgctxt "Pattern description"
msgid "Left-aligned text with a CTA button and a separator."
msgstr "带 CTA 按钮和分隔线的左对齐文本。"

#: theme.json
msgctxt "Template part name"
msgid "Comments Template Part"
msgstr "评论模板部分"

#: theme.json
msgctxt "Template part name"
msgid "Post Meta"
msgstr "文章 Meta"

#: theme.json
msgctxt "Template part name"
msgid "Footer"
msgstr "页脚"

#: theme.json
msgctxt "Template part name"
msgid "Header"
msgstr "页眉"

#: theme.json
msgctxt "Font family name"
msgid "Source Serif Pro"
msgstr "Source Serif Pro"

#: theme.json
msgctxt "Font family name"
msgid "System Font"
msgstr "系统字体"

#: theme.json
msgctxt "Font family name"
msgid "Inter"
msgstr "Inter"

#: theme.json
msgctxt "Font family name"
msgid "IBM Plex Mono"
msgstr "IBM Plex Mono"

#: theme.json
msgctxt "Font family name"
msgid "DM Sans"
msgstr "DM Sans"

#: theme.json
msgctxt "Custom template name"
msgid "404"
msgstr "404"

#: theme.json
msgctxt "Custom template name"
msgid "Blog (Alternative)"
msgstr "博客（替代）"

#: theme.json
msgctxt "Custom template name"
msgid "Blank"
msgstr "空白页面"

#: styles/whisper.json
msgctxt "Style variation name"
msgid "Whisper"
msgstr "Whisper"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary to Primary Fixed"
msgstr "点缀色到副色到主色（固定）"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary Fixed"
msgstr "主色到副色到点缀色（固定）"

#: styles/sherbet.json
msgctxt "Gradient name"
msgid "Primary to Secondary to Tertiary"
msgstr "主色到副色到点缀色"

#: styles/sherbet.json
msgctxt "Style variation name"
msgid "Sherbet"
msgstr "Sherbet"

#: styles/pitch.json
msgctxt "Font size name"
msgid "2X Large"
msgstr "2X 尺寸"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Extra Large"
msgstr "特大号"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Large"
msgstr "大号"

#: styles/pitch.json
msgctxt "Font size name"
msgid "Medium"
msgstr "中号"

#: styles/pitch.json
msgctxt "Font size name"
msgid "small"
msgstr "小号"

#: styles/pitch.json
msgctxt "Space size name"
msgid "7"
msgstr "7"

#: styles/pitch.json
msgctxt "Style variation name"
msgid "Pitch"
msgstr "Pitch"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Dots"
msgstr "点渐变"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Base to Primary"
msgstr "由基准色到主色"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Secondary"
msgstr "由三级色到次色"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Secondary to Primary"
msgstr "由次色到主色"

#: styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Primary to Secondary"
msgstr "由主色到次色"

#: styles/pilgrimage.json
msgctxt "Style variation name"
msgid "Pilgrimage"
msgstr "Pilgrimage"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Gigantic"
msgstr "巨大"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Huge"
msgstr "特大"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Normal"
msgstr "普通"

#: styles/marigold.json
msgctxt "Font size name"
msgid "Tiny"
msgstr "极小"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "6"
msgstr "6"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "5"
msgstr "5"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "4"
msgstr "4"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "3"
msgstr "3"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "2"
msgstr "2"

#: styles/marigold.json styles/pitch.json theme.json
msgctxt "Space size name"
msgid "1"
msgstr "1"

#: styles/marigold.json
msgctxt "Style variation name"
msgid "Marigold"
msgstr "Marigold"

#: styles/grapes.json
msgctxt "Style variation name"
msgid "Grapes"
msgstr "Grapes"

#: styles/electric.json
msgctxt "Style variation name"
msgid "Electric"
msgstr "Electric"

#: styles/canary.json
msgctxt "Style variation name"
msgid "Canary"
msgstr "Canary"

#: styles/block-out.json styles/canary.json styles/pilgrimage.json
#: styles/sherbet.json
msgctxt "Duotone name"
msgid "Default filter"
msgstr "默认筛选"

#: styles/block-out.json
msgctxt "Style variation name"
msgid "Block out"
msgstr "Block out"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Tertiary"
msgstr "点缀色"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Secondary"
msgstr "副色"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Primary"
msgstr "主色"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Contrast"
msgstr "对比色"

#: styles/aubergine.json styles/block-out.json styles/canary.json
#: styles/electric.json styles/grapes.json styles/marigold.json
#: styles/pilgrimage.json styles/pitch.json styles/sherbet.json
#: styles/whisper.json theme.json
msgctxt "Color name"
msgid "Base"
msgstr "基准色"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Primary to Tertiary"
msgstr "主要色彩到第三色彩"

#: styles/aubergine.json styles/pilgrimage.json
msgctxt "Gradient name"
msgid "Tertiary to Primary"
msgstr "第三色彩到主要色彩"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Base to Secondary to Base"
msgstr "基准色到次要色彩到基准色"

#: styles/aubergine.json
msgctxt "Gradient name"
msgid "Secondary to Base"
msgstr "次要色到基准色"

#: styles/aubergine.json
msgctxt "Style variation name"
msgid "Aubergine"
msgstr "Aubergine"

#: patterns/post-meta.php:65
msgctxt "Label for a list of post tags"
msgid "Tags:"
msgstr "标签："

#: patterns/post-meta.php:49
msgctxt "Preposition to show the relationship between the post and its author"
msgid "by"
msgstr "来自"

#: patterns/post-meta.php:37
msgctxt "Preposition to show the relationship between the post and its categories"
msgid "in"
msgstr "分类"

#: patterns/post-meta.php:29
msgctxt "Verb to explain the publication status of a post"
msgid "Posted"
msgstr "已发布"

#: patterns/post-meta.php
msgctxt "Pattern title"
msgid "Post Meta"
msgstr "文章 Meta"

#: patterns/hidden-no-results.php:10
msgctxt "Message explaining that there are no results returned from a search"
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "抱歉，没有符合您搜索条件的结果，换个关键词试试。"

#: patterns/hidden-no-results.php
msgctxt "Pattern title"
msgid "Hidden No Results Content"
msgstr "隐藏没有结果的内容"

#: patterns/hidden-comments.php:13
msgctxt "Title of comments section"
msgid "Comments"
msgstr "评论"

#: patterns/hidden-comments.php
msgctxt "Pattern title"
msgid "Hidden Comments"
msgstr "隐藏评论"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgid "Search"
msgstr "搜索"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "placeholder for search field"
msgid "Search..."
msgstr "搜索…"

#: patterns/hidden-404.php:22 patterns/hidden-no-results.php:14
msgctxt "label"
msgid "Search"
msgstr "搜索"

#: patterns/hidden-404.php:19
msgctxt "Message to convey that a webpage could not be found"
msgid "This page could not be found."
msgstr "找不到此页面。"

#: patterns/hidden-404.php:13
msgctxt "Error code for a webpage that is not found."
msgid "404"
msgstr "404"

#: patterns/hidden-404.php
msgctxt "Pattern title"
msgid "Hidden 404"
msgstr "隐藏 404"

#. Translators: WordPress link.
#: patterns/footer-default.php:20
msgid "Proudly powered by %s"
msgstr "自豪地采用 %s"

#: patterns/footer-default.php
msgctxt "Pattern title"
msgid "Default Footer"
msgstr "默认页脚"

#: patterns/call-to-action.php:25
msgctxt "sample content for call to action button"
msgid "Get In Touch"
msgstr "联系我们"

#: patterns/call-to-action.php:16
msgctxt "sample content for call to action"
msgid "Got any book recommendations?"
msgstr "有任何预订建议吗？"

#: patterns/call-to-action.php
msgctxt "Pattern title"
msgid "Call to action"
msgstr "行动号召"

#. Author URI of the theme
#: style.css patterns/footer-default.php:21
#, gp-priority: low
msgid "https://wordpress.org"
msgstr "https://cn.wordpress.org"

#. Author of the theme
#: style.css
#, gp-priority: low
msgid "the WordPress team"
msgstr "WordPress 团队"

#. Theme URI of the theme
#: style.css
#, gp-priority: low
msgid "https://wordpress.org/themes/twentytwentythree"
msgstr "https://cn.wordpress.org/themes/twentytwentythree"